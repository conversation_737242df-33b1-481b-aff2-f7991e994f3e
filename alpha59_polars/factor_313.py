# Alpha299因子 - factor_313 (Polars版本 - 准确实现)
# 原始因子编号: 313
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_313(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及两个回归贝塔系数、tanh变换、截面排名、取负、加法

    参数:
        w: 基础窗口大小，默认6
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n1 = w if w is not None else 6      # 第一个回归beta窗口
    n2 = 14                             # 第二个回归beta窗口

    # 1. 计算第一个贝塔：volume对close的回归贝塔
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    beta1 = Ops.rolling_regbeta(volume_protected, "Close", n1).over("symbol").alias("_beta1")

    # 2. 应用tanh变换
    tanh_beta1 = pl.col("_beta1").tanh().alias("_tanh_beta1")

    # 3. 计算截面排名
    rank_tanh = pl.col("_tanh_beta1").rank(method="average").over("datetime").alias("_rank_tanh")

    # 4. 计算第二个贝塔：close对low的回归贝塔
    beta2 = Ops.rolling_regbeta("Close", "Low", n2).over("symbol").alias("_beta2")

    # 5. 取负数
    neg_beta2 = (-pl.col("_beta2")).alias("_neg_beta2")

    # 6. 计算因子
    factor_result = (pl.col("_rank_tanh") + pl.col("_neg_beta2")).cast(pl.Float32).alias("factor_313")

    return [beta1, tanh_beta1, rank_tanh, beta2, neg_beta2, factor_result]
