# Alpha299因子 - factor_232 (Polars版本 - 准确实现)
# 原始因子编号: 232
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_232(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及除法、减法、最大值、协方差、截面排名

    参数:
        w: 协方差窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    cov_window = w if w is not None else 5

    # 1. 计算X1 = div(neg(low), sub(high, vwap))
    X1 = ((-pl.col("Low")) / (pl.col("High") - pl.col("Vwap") + eps)).alias("_X1")

    # 2. 计算T3 = gp_max(low, vwap)
    T3 = pl.max_horizontal([pl.col("Low"), pl.col("Vwap")]).alias("_T3")

    # 3. 计算ts_cov(cov_window, T3, volume)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    T4 = Ops.rolling_cov(pl.col("_T3"), volume_protected, cov_window).over("symbol").alias("_T4")

    # 4. 计算rank(T4)
    X2 = pl.col("_T4").rank(method="average").over("datetime").alias("_X2")

    # 5. 计算div(X1, X2)
    factor_result = (pl.col("_X1") / (pl.col("_X2") + eps)).cast(pl.Float32).alias("factor_232")

    return [X1, T3, T4, X2, factor_result]
