# Alpha299因子 - factor_192 (Polars版本 - 准确实现)
# 原始因子编号: 192
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_192(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及VWAP、移动平均、求和、相关系数、截面排名、Z-score、条件判断

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n_ma = w                    # 移动平均窗口
    sum_days = w                # 求和窗口
    correlation_window1 = w     # 第一个相关系数窗口
    correlation_window2 = w     # 第二个相关系数窗口
    zscore_window = w           # Z-score窗口

    # 1. 计算日均值的求和
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    MA_VOL = Ops.rolling_mean(volume_protected, n_ma)
    SumMAVol = Ops.rolling_sum(MA_VOL, sum_days).over("symbol").alias("_SumMAVol")

    # 2. 计算收盘价与SumMAVol的相关系数
    Corr_1 = Ops.rolling_corr("Close", pl.col("_SumMAVol"), correlation_window1).over("symbol").alias("_Corr_1")

    # 3. 横截面排序R1
    R_1 = pl.col("_Corr_1").rank(method="average").over("datetime").alias("_R_1")

    # 4. 计算混合价格
    P_mix = (0.1 * pl.col("High") + 0.9 * pl.col("Vwap")).alias("_P_mix")

    # 5. 横截面排序P_mix
    RankP_mix = pl.col("_P_mix").rank(method="average").over("datetime").alias("_RankP_mix")

    # 6. 计算Z-score标准化成交量
    vol_mean = Ops.rolling_mean(volume_protected, zscore_window)
    vol_std = Ops.rolling_std(volume_protected, zscore_window)
    ZScoreVol = ((volume_protected - vol_mean) / (vol_std + eps)).over("symbol").alias("_ZScoreVol")

    # 7. 横截面排序ZScoreVol
    RankVol = pl.col("_ZScoreVol").rank(method="average").over("datetime").alias("_RankVol")

    # 8. 计算RankP_mix与RankVol的相关系数
    Corr_2 = Ops.rolling_corr(pl.col("_RankP_mix"), pl.col("_RankVol"), correlation_window2).over("symbol").alias("_Corr_2")

    # 9. 横截面排序R2
    R_2 = pl.col("_Corr_2").rank(method="average").over("datetime").alias("_R_2")

    # 10. 最终因子值
    factor_result = pl.when(pl.col("_R_1") < pl.col("_R_2")).then(-1).otherwise(0).cast(pl.Float32).alias("factor_192")

    return [SumMAVol, Corr_1, R_1, P_mix, RankP_mix, ZScoreVol, RankVol, Corr_2, R_2, factor_result]
