# Alpha299因子 - factor_83 (Polars版本 - 准确实现)
# 原始因子编号: 83
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_83(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及高价与成交量的滚动相关系数、差分、收盘价标准差、截面排名

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    base_window = w      # 基础窗口
    vol_window = 4 * w   # 成交量窗口（通常是基础窗口的4倍）

    # 1. 计算High与Volume的滚动相关系数
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    corr_hv = Ops.rolling_corr("High", volume_protected, base_window).over("symbol").alias("_corr_hv")

    # 2. 计算5期差分（Δ5 CorrHV）
    delta_corr_hv = (pl.col("_corr_hv") - pl.col("_corr_hv").shift(base_window)).over("symbol").alias("_delta_corr_hv")

    # 3. 计算Close的滚动标准差
    std_close = Ops.rolling_std("Close", vol_window).over("symbol").alias("_std_close")

    # 4. 横截面百分比排名
    rank_std_close = pl.col("_std_close").rank(method="average").over("datetime").alias("_rank_std_close")

    # 5. 计算最终因子值
    factor_result = (-(pl.col("_delta_corr_hv") * pl.col("_rank_std_close"))).over("symbol").cast(pl.Float32).alias("factor_83")

    return [corr_hv, delta_corr_hv, std_close, rank_std_close, factor_result]
