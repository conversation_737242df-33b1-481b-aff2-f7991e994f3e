# Alpha299因子 - factor_319 (Polars版本 - 准确实现)
# 原始因子编号: 319
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_319(w: int | None = 7, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算因子：涉及成交量对数、收盘价截面排名、差分、乘积

    参数:
        w: 差分窗口大小，默认7
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta_window = w if w is not None else 7

    # 1. 计算 log(volume)，处理 volume <= 0 的情况
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    log_volume = (volume_protected + eps).log().alias("_log_volume")

    # 2. 计算 close 的截面排名（每个时间点内所有 symbol 的 close 排名）
    rank_close = pl.col("Close").rank(method="average").over("datetime").alias("_rank_close")

    # 3. 计算 delta(rank_close, delta_window)：每个 symbol 的 rank_close 与 delta_window 周期前的差值
    delta_rank = (pl.col("_rank_close") - pl.col("_rank_close").shift(delta_window)).over("symbol").alias("_delta_rank")

    # 4. 计算因子值
    factor_result = (pl.col("_log_volume") * pl.col("_delta_rank")).cast(pl.Float32).alias("factor_319")

    return [log_volume, rank_close, delta_rank, factor_result]
