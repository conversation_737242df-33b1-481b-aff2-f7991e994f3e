# Alpha299因子 - factor_183 (Polars版本 - 准确实现)
# 原始因子编号: 183
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_183(w: int | None = 7, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP差分、线性衰减、截面排名、复杂的Term计算、时序排名

    参数:
        w: 基础窗口大小，默认7
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta_vwap_window = w       # VWAP差分窗口
    decay_linear_7 = w          # 第一次线性衰减窗口
    decay_linear_11 = w + 4     # 第二次线性衰减窗口
    ts_rank_7 = w               # 时序排名窗口

    # 1. 计算Delta VWAP
    delta_vwap = (pl.col("Vwap") - pl.col("Vwap").shift(delta_vwap_window)).over("symbol").alias("_delta_vwap")

    # 2. 应用DecayLinear到Delta VWAP
    dl1 = Ops.decaylinear(pl.col("_delta_vwap"), decay_linear_7).over("symbol").alias("_dl1")

    # 3. 计算跨截面排名R1
    r1 = pl.col("_dl1").rank(method="average").over("datetime").alias("_r1")

    # 4. 计算Term_t
    denominator = (pl.col("Open") - (pl.col("High") + pl.col("Low")) / 2).alias("_denominator")
    term_t = ((pl.col("Low") - pl.col("Vwap")) / (pl.col("_denominator") + eps)).alias("_term_t")

    # 5. 应用DecayLinear到Term_t
    dl2 = Ops.decaylinear(pl.col("_term_t"), decay_linear_11).over("symbol").alias("_dl2")

    # 6. 计算ts_rank R2
    r2 = Ops.rolling_rank(pl.col("_dl2"), ts_rank_7).over("symbol").alias("_r2")

    # 7. 计算最终因子
    factor_result = (-(pl.col("_r1") + pl.col("_r2"))).cast(pl.Float32).alias("factor_183")

    return [delta_vwap, dl1, r1, denominator, term_t, dl2, r2, factor_result]
