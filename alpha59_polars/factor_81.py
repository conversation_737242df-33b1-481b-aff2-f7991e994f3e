# Alpha299因子 - factor_81 (Polars版本 - 准确实现)
# 原始因子编号: 81
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_81(w: int | None = 10, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及收盘价截面排名、成交量时序Z-score、再次截面排名、滚动协方差

    参数:
        w: 基础窗口大小，默认10
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n_cova = w      # 协方差窗口
    n_zscore = w    # Z-score窗口

    # 1. 计算收盘价横截面排名 R_C
    R_C = pl.col("Close").rank(method="average").over("datetime").alias("_R_C")

    # 2. 计算成交量时序Z-score标准化 V'
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_mean = Ops.rolling_mean(volume_protected, n_zscore)
    volume_std = Ops.rolling_std(volume_protected, n_zscore)
    V_prime = ((volume_protected - volume_mean) / (volume_std + eps)).over("symbol").alias("_V_prime")

    # 3. 计算标准化成交量横截面排名 R_V'
    R_V_prime = pl.col("_V_prime").rank(method="average").over("datetime").alias("_R_V_prime")

    # 4. 计算协方差 Cov(R_C, R_V')
    Cov = Ops.rolling_cov(pl.col("_R_C"), pl.col("_R_V_prime"), n_cova).over("symbol").alias("_Cov")

    # 5. 计算最终因子：对协方差进行横截面排名并取负
    cov_rank = pl.col("_Cov").rank(method="average").over("datetime").alias("_cov_rank")
    factor_result = (-pl.col("_cov_rank")).over("symbol").cast(pl.Float32).alias("factor_81")

    return [R_C, V_prime, R_V_prime, Cov, cov_rank, factor_result]
