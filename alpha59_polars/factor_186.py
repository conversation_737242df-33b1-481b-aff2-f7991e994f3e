# Alpha299因子 - factor_186 (Polars版本 - 准确实现)
# 原始因子编号: 186
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_186(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算因子：涉及VWAP计算、加权平均价格、差分、截面排名

    参数:
        w: 差分窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    actual_w = w if w is not None else 5

    # 1. 计算加权平均价格 Pt
    Pt = (0.8 * pl.col("Vwap") + 0.1 * pl.col("High") + 0.1 * pl.col("Low")).alias("_Pt")

    # 2. 计算差分 ΔPt = Pt_{t-w} - Pt
    delta_P = (pl.col("_Pt").shift(actual_w) - pl.col("_Pt")).over("symbol").alias("_delta_P")

    # 3. 计算-ΔPt并横截面百分比排序
    delta_P_neg = (-pl.col("_delta_P")).alias("_delta_P_neg")
    factor_result = pl.col("_delta_P_neg").rank(method="average").over("datetime").cast(pl.Float32).alias("factor_186")

    return [Pt, delta_P, delta_P_neg, factor_result]
