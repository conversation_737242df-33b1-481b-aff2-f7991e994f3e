# Alpha299因子 - factor_49 (Polars版本 - 准确实现)
# 原始因子编号: 49
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_49(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及移动平均、滞后、多次截面排名、VWAP计算等

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 1. 计算MA_5(Close)和Term_t
    n_ma = w
    ma_close = Ops.rolling_mean("Close", n_ma)
    term_t = ((pl.col("High") - pl.col("Low")) / (ma_close + eps)).alias("_term_t")

    # 2. 计算Term_t_lagged (滞后2期)
    term_t_lagged = pl.col("_term_t").shift(2).alias("_term_t_lagged")

    # 3. 计算R1：横截面排序
    R1 = (pl.col("_term_t_lagged").rank(method="average").over("datetime") / pl.len().over("datetime")).alias("_R1")

    # 4. 计算ZVol_t：20日Z-score (4*w)
    n_zscore = 4 * w
    volume_mean = Ops.rolling_mean("Volume", n_zscore)
    volume_std = Ops.rolling_std("Volume", n_zscore)
    ZVol_t = ((pl.col("Volume") - volume_mean) / (volume_std + eps)).over("symbol").alias("_ZVol_t")

    # 5. 第一次横截面排序ZVol_t
    RankZVol_t = (pl.col("_ZVol_t").rank(method="average").over("datetime") / pl.len().over("datetime")).alias("_RankZVol_t")

    # 6. 第二次横截面排序RankZVol_t
    RankRankZVol_t = (pl.col("_RankZVol_t").rank(method="average").over("datetime") / pl.len().over("datetime")).alias("_RankRankZVol_t")

    # 7. 计算Denom_t
    denominator = (pl.col("_term_t") / (pl.col("Vwap") - pl.col("Close") + eps)).alias("_denominator")

    # 8. 计算最终因子
    factor_result = ((pl.col("_R1") * pl.col("_RankRankZVol_t")) / (pl.col("_denominator") + eps)).over("symbol").cast(pl.Float32).alias("factor_49")

    return [term_t, term_t_lagged, R1, ZVol_t, RankZVol_t, RankRankZVol_t, denominator, factor_result]
