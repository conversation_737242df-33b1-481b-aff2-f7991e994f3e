# Alpha299因子 - factor_172 (Polars版本 - 准确实现)
# 原始因子编号: 172
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_172(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算因子：涉及加权价格、差分、信号生成、截面排名

    参数:
        w: 差分窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    actual_w = w if w is not None else 5

    # 1. 计算加权价格Pt = 0.85*open + 0.15*high
    Pt = (0.85 * pl.col("Open") + 0.15 * pl.col("High")).alias("_Pt")

    # 2. 计算差分ΔPt = Pt - Pt.shift(actual_w)
    delta_P = (pl.col("_Pt") - pl.col("_Pt").shift(actual_w)).over("symbol").alias("_delta_P")

    # 3. 生成信号S_t
    S_t = pl.when(pl.col("_delta_P") > 0).then(1)\
           .when(pl.col("_delta_P") < 0).then(-1)\
           .otherwise(0).alias("_S_t")

    # 4. 横截面百分比排序
    factor_result = pl.col("_S_t").rank(method="average").over("datetime").cast(pl.Float32).alias("factor_172")

    return [Pt, delta_P, S_t, factor_result]
