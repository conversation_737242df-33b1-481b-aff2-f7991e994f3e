# Alpha299因子 - factor_266 (Polars版本 - 准确实现)
# 原始因子编号: 266
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_266(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP计算、截面排名、滚动相关系数

    参数:
        w: 相关系数计算窗口，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    corr_window = w if w is not None else 5

    # 1. 计算截面排名（按交易日）
    rank_vwap = pl.col("Vwap").rank(method="average").over("date").alias("_rank_vwap")

    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    rank_amount = amount_protected.rank(method="average").over("date").alias("_rank_amount")

    # 2. 计算滚动相关系数
    factor_result = Ops.rolling_corr(pl.col("_rank_vwap"), pl.col("_rank_amount"), corr_window).over("symbol").cast(pl.Float32).alias("factor_266")

    return [rank_vwap, rank_amount, factor_result]
