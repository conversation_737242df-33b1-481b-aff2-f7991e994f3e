# Alpha299因子 - factor_373 (Polars版本 - 准确实现)
# 原始因子编号: 373
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_373(w: int | None = 6, uni_col: str | None = 'Volume', eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及反正切、截面排名、差分、对数、滚动标准差、加法

    参数:
        w: 基础窗口大小，默认6
        uni_col: 单一基础数据列，默认为'Volume'
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta_window = 6        # delta窗口
    std_window = 9          # ts_std窗口

    # 1. 计算成交量的反正切值
    volume_protected = pl.when(pl.col(uni_col) > 0).then(pl.col(uni_col)).otherwise(eps)
    arctan_volume = (volume_protected + eps).arctan().alias("_arctan_volume")

    # 2. 计算截面排名
    rank_arctan = pl.col("_arctan_volume").rank(method="average").over("date").alias("_rank_arctan")

    # 3. 计算过去delta_window个周期的差值
    delta_rank = (pl.col("_rank_arctan") - pl.col("_rank_arctan").shift(delta_window)).over("symbol").alias("_delta_rank")

    # 4. 计算成交量的自然对数
    log_volume = (volume_protected.abs() + eps).log().alias("_log_volume")

    # 5. 计算过去std_window个周期的滚动标准差
    std_log_volume = Ops.rolling_std(pl.col("_log_volume"), std_window).over("symbol").alias("_std_log_volume")

    # 6. 计算两者之和
    factor_result = (pl.col("_delta_rank") + pl.col("_std_log_volume")).cast(pl.Float32).alias("factor_373")

    return [arctan_volume, rank_arctan, delta_rank, log_volume, std_log_volume, factor_result]
