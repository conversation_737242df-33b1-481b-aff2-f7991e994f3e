# Alpha299因子 - factor_215 (Polars版本 - 准确实现)
# 原始因子编号: 215
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_215(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及最大值、时序排名、Z-score、截面排名、除法、加法、协方差

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n1 = w if w is not None else 5  # ts_rank窗口
    n2 = w if w is not None else 5  # ts_cov窗口
    n3 = w if w is not None else 5  # ts_zscore窗口

    # 1. T1 = gp_max(low, volume)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    T1 = pl.max_horizontal([pl.col("Low"), volume_protected]).alias("_T1")

    # 2. X1 = ts_rank(T1, n1)
    X1 = Ops.rolling_rank(pl.col("_T1"), n1).over("symbol").alias("_X1")

    # 3. T2 = ts_zscore(volume, n3)
    vol_mean = Ops.rolling_mean(volume_protected, n3)
    vol_std = Ops.rolling_std(volume_protected, n3)
    T2 = ((volume_protected - vol_mean) / (vol_std + eps)).over("symbol").alias("_T2")

    # 4. T3 = rank(T2) - 按时间截面排名
    T3 = (pl.col("_T2").rank(method="average").over("datetime") / pl.len().over("datetime")).alias("_T3")

    # 5. T4 = mul(high, volume)
    T4 = (pl.col("High") * volume_protected).alias("_T4")

    # 6. T5 = div(T3, T4)
    T5 = (pl.col("_T3") / (pl.col("_T4") + eps)).alias("_T5")

    # 7. T6 = add(vwap, close)
    T6 = (pl.col("Vwap") + pl.col("Close")).alias("_T6")

    # 8. X2 = gp_max(T5, T6)
    X2 = pl.max_horizontal([pl.col("_T5"), pl.col("_T6")]).alias("_X2")

    # 9. ts_cov(n2, X1, X2)
    factor_result = Ops.rolling_cov(pl.col("_X1"), pl.col("_X2"), n2).over("symbol").cast(pl.Float32).alias("factor_215")

    return [T1, X1, T2, T3, T4, T5, T6, X2, factor_result]
