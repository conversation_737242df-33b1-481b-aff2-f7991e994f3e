# Alpha299因子 - factor_133 (Polars版本 - 准确实现)
# 原始因子编号: 133
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_133(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及成交量对数差分、截面排名、收益率、滚动相关系数

    参数:
        w: 滚动相关系数窗口，默认6
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    correlation_window = w

    # 1. 计算成交量的自然对数和一期差分
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    log_volume = volume_protected.log()
    diff_log_volume = (log_volume - log_volume.shift(1)).over("symbol").alias("_diff_log_volume")

    # 2. 对差分值进行横截面百分比排名
    R_V = pl.col("_diff_log_volume").rank(method="average").over("datetime").alias("_R_V")

    # 3. 计算当日开盘到收盘的收益率
    ret_o2c = ((pl.col("Close") - pl.col("Open")) / (pl.col("Open") + eps)).alias("_ret_o2c")

    # 4. 对收益率进行横截面百分比排名
    R_R = pl.col("_ret_o2c").rank(method="average").over("datetime").alias("_R_R")

    # 5. 计算R_V和R_R的滚动相关系数，取负数得到最终因子值
    factor_result = (-Ops.rolling_corr(pl.col("_R_V"), pl.col("_R_R"), correlation_window)).over("symbol").cast(pl.Float32).alias("factor_133")

    return [diff_log_volume, R_V, ret_o2c, R_R, factor_result]
