# Alpha299因子 - factor_298 (Polars版本 - 准确实现)
# 原始因子编号: 298
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_298(w: int | None = 2, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及延迟、对数、Z-score、截面排名、回归残差、最小值、变化率等多个步骤

    参数:
        w: 基础窗口大小，默认2
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    w2 = w if w is not None else 2      # delay窗口
    w5 = 5                              # delay_vwap窗口
    w7 = 7                              # ts_regres窗口
    w10 = 10                            # ts_zscore窗口
    w13 = 13                            # ts_pctchg窗口
    w20 = 20                            # volume_zscore窗口
    
    # 1. 计算delay(amount, 2)和log(delay_amount_2)
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    delay_amount_2 = amount_protected.shift(w2).over("symbol").alias("_delay_amount_2")
    log_delay_amount = (pl.col("_delay_amount_2").abs() + eps).log().alias("_log_delay_amount")

    # 2. 计算delay(vwap, 5)
    delay_vwap_5 = pl.col("Vwap").shift(w5).over("symbol").alias("_delay_vwap_5")

    # 3. 计算ts_zscore(volume, 20)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    vol_mean = Ops.rolling_mean(volume_protected, w20)
    vol_std = Ops.rolling_std(volume_protected, w20)
    volume_zscore_20 = ((volume_protected - vol_mean) / (vol_std + eps)).over("symbol").alias("_volume_zscore_20")

    # 4. 计算rank(volume_zscore_20)
    volume_rank = pl.col("_volume_zscore_20").rank(method="average").over("date").alias("_volume_rank")
    
    # 5. 计算ts_zscore(volume_rank, 10)
    vrank_mean = Ops.rolling_mean(pl.col("_volume_rank"), w10)
    vrank_std = Ops.rolling_std(pl.col("_volume_rank"), w10)
    volume_rank_zscore = ((pl.col("_volume_rank") - vrank_mean) / (vrank_std + eps)).over("symbol").alias("_volume_rank_zscore")

    # 6. 计算ts_zscore(close, 10)
    close_mean = Ops.rolling_mean("Close", w10)
    close_std = Ops.rolling_std("Close", w10)
    close_zscore = ((pl.col("Close") - close_mean) / (close_std + eps)).over("symbol").alias("_close_zscore")

    # 7. 计算add(volume_rank_zscore, close_zscore)
    add_result = (pl.col("_volume_rank_zscore") + pl.col("_close_zscore")).alias("_add_result")

    # 8. 计算ts_regres(delay_vwap_5, add_result, 7)
    vwap_add_regres = Ops.rolling_regres(pl.col("_delay_vwap_5"), pl.col("_add_result"), w7).over("symbol").alias("_vwap_add_regres")
    
    # 9. 计算ts_zscore(log_delay_amount, 10)
    log_mean = Ops.rolling_mean(pl.col("_log_delay_amount"), w10)
    log_std = Ops.rolling_std(pl.col("_log_delay_amount"), w10)
    log_amount_zscore = ((pl.col("_log_delay_amount") - log_mean) / (log_std + eps)).over("symbol").alias("_log_amount_zscore")

    # 10. 计算ts_zscore(vwap_add_regres, 10)
    regres_mean = Ops.rolling_mean(pl.col("_vwap_add_regres"), w10)
    regres_std = Ops.rolling_std(pl.col("_vwap_add_regres"), w10)
    regres_zscore = ((pl.col("_vwap_add_regres") - regres_mean) / (regres_std + eps)).over("symbol").alias("_regres_zscore")

    # 11. 计算gp_min(log_amount_zscore, regres_zscore)
    min_result = pl.min_horizontal([pl.col("_log_amount_zscore"), pl.col("_regres_zscore")]).alias("_min_result")

    # 12. 计算ts_pctchg(min_result, 13)
    factor_result = ((pl.col("_min_result") - pl.col("_min_result").shift(w13).over("symbol")) / (pl.col("_min_result").shift(w13).over("symbol") + eps)).cast(pl.Float32).alias("factor_298")

    return [delay_amount_2, log_delay_amount, delay_vwap_5, volume_zscore_20, volume_rank,
            volume_rank_zscore, close_zscore, add_result, vwap_add_regres, log_amount_zscore,
            regres_zscore, min_result, factor_result]
