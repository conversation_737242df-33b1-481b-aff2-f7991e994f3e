# Alpha299因子 - factor_150 (Polars版本 - 准确实现)
# 原始因子编号: 150
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_150(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算Alpha 45因子：价格动量与VWAP成交量相关性排名乘积因子

    参数:
        w: 核心可调参数（此处因子中无天数参数，设为None）
        uni_col: 基础数据列（此处因子使用多列，设为None）
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 1. 计算混合价格Pmix和1期差分
    pmix = (0.6 * pl.col("Close") + 0.4 * pl.col("Open")).alias("_pmix")
    delta_pmix = (pl.col("_pmix") - pl.col("_pmix").shift(1)).over("symbol").alias("_delta_pmix")

    # 2. 对delta_pmix进行横截面百分比排序（按时间点分组）
    r1 = pl.col("_delta_pmix").rank(method="average").over("datetime").alias("_r1")

    # 3. 计算成交量的150期移动平均和MAVol与VWAP的15期滚动相关系数
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    mavol_150 = Ops.rolling_mean(volume_protected, 150)
    corr_volvwap = Ops.rolling_corr(mavol_150, pl.col("Vwap"), 15).over("symbol").alias("_corr_volvwap")

    # 4. 对相关系数进行横截面百分比排序
    r2 = pl.col("_corr_volvwap").rank(method="average").over("datetime").alias("_r2")

    # 5. 计算最终因子值（R1 * R2）
    factor_result = (pl.col("_r1") * pl.col("_r2")).cast(pl.Float32).alias("factor_150")

    return [pmix, delta_pmix, r1, corr_volvwap, r2, factor_result]
