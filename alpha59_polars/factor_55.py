# Alpha299因子 - factor_55 (Polars版本 - 准确实现)
# 原始因子编号: 55
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_55(w: int | None = 10, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算Alpha 142因子（三重排名乘积因子）

    参数:
        w: 可调时间窗口基准参数，默认10
        uni_col: 单一基础数据列参数，默认None，因涉及多列
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 1. 收盘价10期时序排名
    tsr_c = Ops.rolling_rank("Close", w).over("symbol").alias("_tsr_c")

    # 2. 横截面排名取负
    r1 = (pl.col("_tsr_c").rank(method="average").over("datetime") * -1).alias("_r1")

    # 3. 二阶差分计算
    delta_1 = (pl.col("Close") - pl.col("Close").shift(1)).alias("_delta_1")
    delta_2 = (pl.col("_delta_1") - pl.col("_delta_1").shift(1)).over("symbol").alias("_delta_2")

    # 4. 二阶差分横截面排名
    r2 = pl.col("_delta_2").rank(method="average").over("datetime").alias("_r2")

    # 5. 成交量比率计算
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    ma_vol = Ops.rolling_mean(volume_protected, 2*w)
    vol_ratio = (volume_protected / (ma_vol + eps)).alias("_vol_ratio")

    # 6. 成交量比率时序排名
    tsr_vr = Ops.rolling_rank(pl.col("_vol_ratio"), 2*w).over("symbol").alias("_tsr_vr")

    # 7. 成交量比率横截面排名
    r3 = pl.col("_tsr_vr").rank(method="average").over("datetime").alias("_r3")

    # 8. 最终因子计算
    factor_result = (pl.col("_r1") * pl.col("_r2") * pl.col("_r3")).over("symbol").cast(pl.Float32).alias("factor_55")

    return [tsr_c, r1, delta_1, delta_2, r2, vol_ratio, tsr_vr, r3, factor_result]
