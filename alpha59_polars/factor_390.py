# Alpha299因子 - factor_390 (Polars版本 - 准确实现)
# 原始因子编号: 390
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_390(w: int | None = 4, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算因子：涉及滚动相关系数、截面排名

    参数:
        w: 相关系数窗口大小，默认4
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    corr_window = w if w is not None else 4

    # 1. 计算成交额和收盘价在过去corr_window个周期内的滚动相关系数
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    rolling_corr = Ops.rolling_corr(amount_protected, "Close", corr_window).over("symbol").alias("_rolling_corr")

    # 2. 对每个时间点进行截面排名
    factor_result = pl.col("_rolling_corr").rank(method="average").over("datetime").cast(pl.Float32).alias("factor_390")

    return [rolling_corr, factor_result]
