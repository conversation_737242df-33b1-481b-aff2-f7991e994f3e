# Alpha299因子 - factor_214 (Polars版本 - 准确实现)
# 原始因子编号: 214
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_214(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及回归贝塔系数、Z-score标准化、截面排名

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n1 = w if w is not None else 5  # 第一个回归窗口
    n2 = w if w is not None else 5  # 第二个回归窗口

    # 第一部分计算
    # 1. 计算T1: ts_regbeta(volume, open_price, n1)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    T1 = Ops.rolling_regbeta(volume_protected, "Open", n1).over("symbol").alias("_T1")

    # 2. T1_std: ts_zscore(T1, n1)
    t1_mean = Ops.rolling_mean(pl.col("_T1"), n1)
    t1_std = Ops.rolling_std(pl.col("_T1"), n1)
    T1_std = ((pl.col("_T1") - t1_mean) / (t1_std + eps)).over("symbol").alias("_T1_std")

    # 3. X1: rank(T1_std)
    X1 = pl.col("_T1_std").rank(method="average").over("datetime").alias("_X1")

    # 第二部分计算
    # 4. 计算T2: ts_regbeta(low, close, n2)
    T2 = Ops.rolling_regbeta("Low", "Close", n2).over("symbol").alias("_T2")

    # 5. X2: neg(T2)
    X2 = (-pl.col("_T2")).alias("_X2")

    # 6. 最终因子计算
    factor_result = (pl.col("_X1") + pl.col("_X2")).cast(pl.Float32).alias("factor_214")

    return [T1, T1_std, X1, T2, X2, factor_result]
