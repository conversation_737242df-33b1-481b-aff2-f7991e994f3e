# Alpha299因子 - factor_350 (Polars版本 - 准确实现)
# 原始因子编号: 350
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_350(w: int | None = 5, uni_col: str | None = 'Volume', eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及反正切、余弦、截面排名、差分、对数、Z-score标准化、加法

    参数:
        w: 基础窗口大小，默认5
        uni_col: 单一基础数据列，默认为'Volume'
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta_window = 5        # 差值窗口
    zscore_window = 10      # 滚动Z-score标准化窗口

    # 1. 对成交量应用反正切函数
    volume_protected = pl.when(pl.col(uni_col) > 0).then(pl.col(uni_col)).otherwise(eps)
    I1 = (volume_protected + eps).arctan().alias("_I1")

    # 2. 对I1应用余弦函数进行非单调变换
    I2 = pl.col("_I1").cos().alias("_I2")

    # 3. 对I2进行截面排名
    I3 = pl.col("_I2").rank(method="average").over("date").alias("_I3")

    # 4. 计算I3的delta_window期差值
    I4 = (pl.col("_I3") - pl.col("_I3").shift(delta_window)).over("symbol").alias("_I4")

    # 5. 对成交量取自然对数
    I5 = (volume_protected.clip(eps, None) + eps).log().alias("_I5")

    # 6. 计算I5在过去zscore_window期的滚动Z-score标准化
    I5_mean = Ops.rolling_mean(pl.col("_I5"), zscore_window)
    I5_std = Ops.rolling_std(pl.col("_I5"), zscore_window)
    I6 = ((pl.col("_I5") - I5_mean) / (I5_std + eps)).over("symbol").alias("_I6")

    # 7. 计算I4与I6的和，得到因子值
    factor_result = (pl.col("_I4") + pl.col("_I6")).cast(pl.Float32).alias("factor_350")

    return [I1, I2, I3, I4, I5, I6, factor_result]
