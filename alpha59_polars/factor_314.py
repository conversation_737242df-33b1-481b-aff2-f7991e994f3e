# Alpha299因子 - factor_314 (Polars版本 - 准确实现)
# 原始因子编号: 314
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_314(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及多个Z-score、时序排名、截面排名、最大值、除法、协方差等

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n1 = w if w is not None else 5      # ts_rank窗口
    n2 = 6                              # ts_cov窗口
    zscore_window = 20                  # ts_zscore窗口
    
    # 1. 计算标准化后的low和volume
    low_mean = Ops.rolling_mean("Low", zscore_window)
    low_std = Ops.rolling_std("Low", zscore_window)
    low_z = ((pl.col("Low") - low_mean) / (low_std + eps)).over("symbol").alias("_low_z")

    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_mean = Ops.rolling_mean(volume_protected, zscore_window)
    volume_std = Ops.rolling_std(volume_protected, zscore_window)
    volume_z = ((volume_protected - volume_mean) / (volume_std + eps)).over("symbol").alias("_volume_z")

    # 2. gp_max(low_z, volume_z)
    T1 = pl.max_horizontal([pl.col("_low_z"), pl.col("_volume_z")]).alias("_T1")

    # 3. ts_rank(T1, 5)
    X1 = Ops.rolling_rank(pl.col("_T1"), n1).over("symbol").alias("_X1")

    # 4. rank(volume_z) (截面排名)
    T2 = pl.col("_volume_z").rank(method="average").over("datetime").alias("_T2")
    
    # 5. high_z = ts_zscore(high, 20) 和 T3 = high_z * volume_z
    high_mean = Ops.rolling_mean("High", zscore_window)
    high_std = Ops.rolling_std("High", zscore_window)
    high_z = ((pl.col("High") - high_mean) / (high_std + eps)).over("symbol").alias("_high_z")
    T3 = (pl.col("_high_z") * pl.col("_volume_z")).alias("_T3")

    # 6. T4 = T2 / T3
    T4 = (pl.col("_T2") / (pl.col("_T3") + eps)).alias("_T4")

    # 7. vwap_z和close_z
    vwap_mean = Ops.rolling_mean(pl.col("Vwap"), zscore_window)
    vwap_std = Ops.rolling_std(pl.col("Vwap"), zscore_window)
    vwap_z = ((pl.col("Vwap") - vwap_mean) / (vwap_std + eps)).over("symbol").alias("_vwap_z")

    close_mean = Ops.rolling_mean("Close", zscore_window)
    close_std = Ops.rolling_std("Close", zscore_window)
    close_z = ((pl.col("Close") - close_mean) / (close_std + eps)).over("symbol").alias("_close_z")

    # 8. T5 = vwap_z + close_z
    T5 = (pl.col("_vwap_z") + pl.col("_close_z")).alias("_T5")

    # 9. X2 = gp_max(T4, T5)
    X2 = pl.max_horizontal([pl.col("_T4"), pl.col("_T5")]).alias("_X2")

    # 10. ts_cov(6, X1, X2)
    factor_result = Ops.rolling_cov(pl.col("_X1"), pl.col("_X2"), n2).over("symbol").cast(pl.Float32).alias("factor_314")

    return [low_z, volume_z, T1, X1, T2, high_z, T3, T4, vwap_z, close_z, T5, X2, factor_result]
