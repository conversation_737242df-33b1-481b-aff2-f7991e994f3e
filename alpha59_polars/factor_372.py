# Alpha299因子 - factor_372 (Polars版本 - 准确实现)
# 原始因子编号: 372
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_372(w: int | None = 15, uni_col: str | None = 'Close', eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及二阶差分、Z-score标准化、截面排名、滚动最大值、加法

    参数:
        w: 基础窗口大小，默认15
        uni_col: 单一基础数据列，默认为'Close'
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta1_window = 15      # 第一个delta窗口
    delta2_window = 4       # 第二个delta窗口
    zscore_window = 20      # zscore窗口
    tsmax_window = 13       # ts_max窗口

    # 1. 计算收盘价在过去delta1_window个周期内的差值：T1 = delta(close, delta1_window)
    T1 = (pl.col(uni_col) - pl.col(uni_col).shift(delta1_window)).over("symbol").alias("_T1")

    # 2. 计算T1在过去delta2_window个周期内的差值：X1 = delta(T1, delta2_window)
    X1 = (pl.col("_T1") - pl.col("_T1").shift(delta2_window)).over("symbol").alias("_X1")

    # 3. 对X1进行滚动标准化（窗口期zscore_window）：X1' = ts_zscore(X1, zscore_window)
    X1_mean = Ops.rolling_mean(pl.col("_X1"), zscore_window)
    X1_std = Ops.rolling_std(pl.col("_X1"), zscore_window)
    X1_zscore = ((pl.col("_X1") - X1_mean) / (X1_std + eps)).over("symbol").alias("_X1_zscore")

    # 4. 计算成交额的截面排名：T2 = rank(amount)
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    T2 = amount_protected.rank(method="average").over("datetime").alias("_T2")

    # 5. 计算T2在过去tsmax_window个周期内的滚动最大值：X2 = ts_max(T2, tsmax_window)
    X2 = Ops.rolling_max(pl.col("_T2"), tsmax_window).over("symbol").alias("_X2")

    # 6. 计算X1'与X2的和得到Alpha23：Alpha23 = add(X1', X2)
    factor_result = (pl.col("_X1_zscore") + pl.col("_X2")).cast(pl.Float32).alias("factor_372")

    return [T1, X1, X1_zscore, T2, X2, factor_result]
