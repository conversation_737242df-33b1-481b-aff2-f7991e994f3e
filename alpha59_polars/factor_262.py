# Alpha299因子 - factor_262 (Polars版本 - 准确实现)
# 原始因子编号: 262
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_262(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP、差分、除法、最大值、协方差、截面排名

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta_window = w if w is not None else 5    # 差分窗口
    cov_window = w if w is not None else 5      # 协方差窗口

    # 1. -VWAP
    neg_vwap = (-pl.col("Vwap")).alias("_neg_vwap")

    # 2. DELTA(AMOUNT, delta_window)
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    delta_amount = (amount_protected - amount_protected.shift(delta_window)).over("symbol").alias("_delta_amount")

    # 3. -VWAP / DELTA(AMOUNT, delta_window)
    part1 = (pl.col("_neg_vwap") / (pl.col("_delta_amount") + eps)).alias("_part1")

    # 4. GP_MAX(LOW, VWAP)
    gp_max = pl.max_horizontal([pl.col("Low"), pl.col("Vwap")]).alias("_gp_max")

    # 5. TS_COV(cov_window, gp_max, volume)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    ts_cov = Ops.rolling_cov(pl.col("_gp_max"), volume_protected, cov_window).over("symbol").alias("_ts_cov")

    # 6. RANK(ts_cov)
    rank_cov = pl.col("_ts_cov").rank(method="average").over("datetime").alias("_rank_cov")

    # 7. part1 / rank
    factor_result = (pl.col("_part1") / (pl.col("_rank_cov") + eps)).cast(pl.Float32).alias("factor_262")

    return [neg_vwap, delta_amount, part1, gp_max, ts_cov, rank_cov, factor_result]
