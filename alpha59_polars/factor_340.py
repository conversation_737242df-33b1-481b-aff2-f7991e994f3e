# Alpha299因子 - factor_340 (Polars版本 - 准确实现)
# 原始因子编号: 340
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_340(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及延迟、截面排名、反正切、变化率、最小值、回归残差等多个步骤

    参数:
        w: 基础窗口大小，默认3
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n1 = 8      # delay(open_price, n1)
    n2 = 8      # ts_pctchg(T3, n2)
    n3 = 5      # ts_pctchg(T5, n3)
    n4 = 3      # delay(T6, n4)
    n5 = 8      # ts_regres窗口
    
    # 1. T1 = delay(open_price, n1)
    T1 = pl.col("Open").shift(n1).over("symbol").alias("_T1")

    # 2. T2 = rank(vwap) - 截面排名
    T2 = pl.col("Vwap").rank(method="average").over("datetime").alias("_T2")

    # 3. T3 = arctan(T2)
    T3 = (pl.col("_T2") + eps).arctan().alias("_T3")

    # 4. T4 = ts_pctchg(T3, n2)
    T4 = ((pl.col("_T3") - pl.col("_T3").shift(n2).over("symbol")) / (pl.col("_T3").shift(n2).over("symbol") + eps)).alias("_T4")

    # 5. T5 = gp_min(T1, T4)
    T5 = pl.min_horizontal([pl.col("_T1"), pl.col("_T4")]).alias("_T5")

    # 6. T6 = ts_pctchg(T5, n3)
    T6 = ((pl.col("_T5") - pl.col("_T5").shift(n3).over("symbol")) / (pl.col("_T5").shift(n3).over("symbol") + eps)).alias("_T6")

    # 7. T7 = delay(T6, n4)
    T7 = pl.col("_T6").shift(n4).over("symbol").alias("_T7")

    # 8. X1 = arctan(T7)
    X1 = (pl.col("_T7") + eps).arctan().alias("_X1")

    # 9. X2 = neg(close)
    X2 = (-pl.col("Close")).alias("_X2")

    # 10. Alpha140 = ts_regres(X1, X2, n5)
    factor_result = Ops.rolling_regres(pl.col("_X1"), pl.col("_X2"), n5).over("symbol").cast(pl.Float32).alias("factor_340")

    return [T1, T2, T3, T4, T5, T6, T7, X1, X2, factor_result]
