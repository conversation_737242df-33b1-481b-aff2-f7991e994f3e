# Alpha299因子 - factor_339 (Polars版本 - 准确实现)
# 原始因子编号: 339
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_339(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算因子：涉及成交量对数、开盘价截面排名、差分、乘积

    参数:
        w: 差分窗口大小，默认9
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta_window = w if w is not None else 9

    # 1. 计算成交量的自然对数
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    log_volume = (volume_protected.abs() + eps).log().alias("_log_volume")

    # 2. 计算开盘价在截面上的排名
    rank_open = pl.col("Open").rank(method="average").over("date").alias("_rank_open")

    # 3. 计算开盘价排名的delta_window天差值
    delta_rank_open = (pl.col("_rank_open") - pl.col("_rank_open").shift(delta_window)).over("symbol").alias("_delta_rank_open")

    # 4. 计算因子值：log(abs(volume)) * delta(rank(open), delta_window)
    factor_result = (pl.col("_log_volume") * pl.col("_delta_rank_open")).cast(pl.Float32).alias("factor_339")

    return [log_volume, rank_open, delta_rank_open, factor_result]
