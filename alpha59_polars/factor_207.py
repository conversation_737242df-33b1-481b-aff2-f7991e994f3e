# Alpha299因子 - factor_207 (Polars版本 - 准确实现)
# 原始因子编号: 207
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_207(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算因子：涉及成交额与开盘价的滚动相关系数、截面排名

    参数:
        w: 相关系数窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    corr_window = w if w is not None else 5

    # 1. 计算滚动相关系数
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    ts_corr = Ops.rolling_corr(amount_protected, "Open", corr_window).over("symbol").alias("_ts_corr")

    # 2. 截面排名（每个时间点对所有symbol的ts_corr进行排名）
    factor_result = pl.col("_ts_corr").rank(method="average").over("datetime").cast(pl.Float32).alias("factor_207")

    return [ts_corr, factor_result]
