# Alpha299因子 - factor_46 (Polars版本 - 准确实现)
# 原始因子编号: 46
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_46(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算Alpha 107优化版因子，包含局部基准消除的ts_zscore变换和横截面排序
    
    参数:
        w: 时间序列滚动窗口（天数），默认20
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 1. 计算前一日的high, close, low
    high_lag1 = pl.col("High").shift(1).alias("_high_lag1")
    close_lag1 = pl.col("Close").shift(1).alias("_close_lag1")
    low_lag1 = pl.col("Low").shift(1).alias("_low_lag1")
    
    # 2. 计算差值
    open_diff_high = (pl.col("Open") - high_lag1).alias("_open_diff_high")
    open_diff_close = (pl.col("Open") - close_lag1).alias("_open_diff_close")
    open_diff_low = (pl.col("Open") - low_lag1).alias("_open_diff_low")
    
    # 3. 计算滚动Z-score (w天窗口)
    # 对open_diff_high进行Z-score标准化
    diff_high_mean = Ops.rolling_mean(open_diff_high, w).alias("_diff_high_mean")
    diff_high_std = Ops.rolling_std(open_diff_high, w).alias("_diff_high_std")
    z1 = ((open_diff_high - diff_high_mean) / (diff_high_std + eps)).over("symbol").alias("_z1")
    
    # 对open_diff_close进行Z-score标准化
    diff_close_mean = Ops.rolling_mean(open_diff_close, w).alias("_diff_close_mean")
    diff_close_std = Ops.rolling_std(open_diff_close, w).alias("_diff_close_std")
    z2 = ((open_diff_close - diff_close_mean) / (diff_close_std + eps)).over("symbol").alias("_z2")
    
    # 对open_diff_low进行Z-score标准化
    diff_low_mean = Ops.rolling_mean(open_diff_low, w).alias("_diff_low_mean")
    diff_low_std = Ops.rolling_std(open_diff_low, w).alias("_diff_low_std")
    z3 = ((open_diff_low - diff_low_mean) / (diff_low_std + eps)).over("symbol").alias("_z3")
    
    # 4. 横截面排序（按时间点）
    # 使用alias避免重复字段问题，然后进行截面排名
    rank_z1 = pl.col("_z1").rank(method="average").over("datetime").alias("_rank_z1")
    rank_z2 = pl.col("_z2").rank(method="average").over("datetime").alias("_rank_z2")
    rank_z3 = pl.col("_z3").rank(method="average").over("datetime").alias("_rank_z3")
    
    # 5. 计算最终因子值
    factor_result = ((-pl.col("_rank_z1")) * pl.col("_rank_z2") * pl.col("_rank_z3")).over("symbol").cast(pl.Float32).alias("factor_46")
    
    return [z1, z2, z3, rank_z1, rank_z2, rank_z3, factor_result]

