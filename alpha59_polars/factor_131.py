# Alpha299因子 - factor_131 (Polars版本 - 准确实现)
# 原始因子编号: 131
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_131(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算Alpha 185因子：开盘收盘价比率平方的负排名因子

    参数:
        w: 时间窗口参数（本因子无需时间窗口，设为None）
        uni_col: 单一基础数据列（本因子使用open和close，设为None）
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 1. 计算开盘价与收盘价的比率和项Term_t
    ratio = (pl.col("Open") / (pl.col("Close") + eps)).alias("_ratio")
    term = (-(1 - pl.col("_ratio")).pow(2)).alias("_term")

    # 2. 横截面百分比排序（按时间分组）
    factor_result = pl.col("_term").rank(method="average").over("datetime").cast(pl.Float32).alias("factor_131")

    return [ratio, term, factor_result]
