# Alpha299因子 - factor_379 (Polars版本 - 准确实现)
# 原始因子编号: 379
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_379(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP计算、截面排名、滚动相关系数

    参数:
        w: 滚动相关系数窗口，默认9
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    rolling_corr_window = w if w is not None else 9

    # 1. 计算vwap (成交量加权平均价)
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    vwap = (amount_protected / volume_protected).alias("_vwap")

    # 2. 计算vwap在截面上的排名
    rank_vwap = pl.col("_vwap").rank(method="average").over("datetime").alias("_rank_vwap")

    # 3. 计算rank_vwap和volume在过去rolling_corr_window个周期内的滚动相关系数
    factor_result = Ops.rolling_corr(pl.col("_rank_vwap"), volume_protected, rolling_corr_window).over("symbol").cast(pl.Float32).alias("factor_379")

    return [vwap, rank_vwap, factor_result]
