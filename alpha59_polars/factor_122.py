# Alpha299因子 - factor_122 (Polars版本 - 准确实现)
# 原始因子编号: 122
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_122(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及多个复杂的项（TermA、TermB、TermC）、截面排名、移动平均、tanh变换

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    w_ma_volume = 4 * w  # TermA的MA窗口
    w_ma_high = w        # TermB的MA窗口

    # 1. 计算TermA
    # 计算1/close的截面排名
    one_close = (1 / (pl.col("Close") + eps)).alias("_1_close")
    R_1_C = pl.col("_1_close").rank(method="average").over("datetime").alias("_R_1_C")

    # 计算MA_volume和TermA
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    MA_volume = Ops.rolling_mean(volume_protected, w_ma_volume)
    TermA = ((volume_protected * pl.col("_R_1_C")) / (MA_volume + eps)).over("symbol").alias("_TermA")

    # 2. 计算TermB
    # 计算high-close的截面排名
    high_minus_close = (pl.col("High") - pl.col("Close")).alias("_high_minus_close")
    R_HC = pl.col("_high_minus_close").rank(method="average").over("datetime").alias("_R_HC")

    # 计算MA_high和TermB
    MA_high = Ops.rolling_mean("High", w_ma_high)
    TermB = ((pl.col("High") * pl.col("_R_HC")) / (MA_high + eps)).over("symbol").alias("_TermB")

    # 3. 计算TermC
    # VWAP差分
    VWAP_shifted = pl.col("Vwap").shift(5).alias("_VWAP_shifted")
    VWAP_diff = (pl.col("Vwap") - pl.col("_VWAP_shifted")).over("symbol").alias("_VWAP_diff")
    R_VWAP_diff = pl.col("_VWAP_diff").rank(method="average").over("datetime").alias("_R_VWAP_diff")

    # TermC计算
    TermC = pl.col("_R_VWAP_diff").alias("_TermC")

    # 4. 应用tanh变换和组合最终因子
    factor_result = (pl.col("_TermA").tanh() * pl.col("_TermB").tanh() - pl.col("_TermC").tanh()).over("symbol").cast(pl.Float32).alias("factor_122")

    return [one_close, R_1_C, TermA, high_minus_close, R_HC, TermB, VWAP_shifted, VWAP_diff, R_VWAP_diff, TermC, factor_result]
