{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2534f042", "metadata": {}, "outputs": [{"data": {"text/plain": ["241"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import polars as pl\n", "\n", "univ = pl.read_parquet(\"/disk4/shared/intern/laiyc/minRawData/ru_min1/univ_full.parquet\")\n", "periods = [(\"0930\",\"1129\"), (\"1300\",\"1459\")]\n", "\n", "VALID_TIME = [\"0925\"] + [\n", "    f\"{m//60:02d}{m%60:02d}\"\n", "    for start, end in periods\n", "    for m in range(\n", "        int(start[:2])*60 + int(start[2:]),\n", "        int(end[:2])*60   + int(end[2:]) + 1\n", "    )\n", "] \n", "\n", "len(VALID_TIME)"]}, {"cell_type": "code", "execution_count": 2, "id": "f11350dd", "metadata": {}, "outputs": [], "source": ["univ = univ.filter(pl.col(\"hhmm\").is_in(VALID_TIME))\n"]}, {"cell_type": "code", "execution_count": 3, "id": "ff2dcf56", "metadata": {}, "outputs": [], "source": ["univ_1bar = univ.with_columns(\n", "    pl.col(\"univ_full\").filter(pl.col(\"hhmm\") == \"0925\").first()\n", "    .over([\"date\", \"symbol\"])\n", "    .cast(pl.Int8)\n", "    .alias(\"univ_1bar\")\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "edc1d6b0", "metadata": {}, "outputs": [], "source": ["univ_1bar = univ_1bar.filter(pl.col(\"year\") != 2024)"]}, {"cell_type": "code", "execution_count": 5, "id": "f0e34ed6", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "\n", "class CONFIG:\n", "    DATA_DIR = \"/disk4/shared/intern/laiyc/minRawData/\"\n", "    PREPROCESS_DIR = \"/disk4/shared/intern/laiyc/preprocess/\"\n", "    RU_DIR = DATA_DIR + \"ru_min1/\"\n", "    DSH_COLS = ['date', 'symbol', 'hhmm']\n", "    SPEC_TIME = [\"0959\", \"1029\", \"1059\", \"1129\", \"1329\", \"1359\", \"1429\"]\n", "    LOCAL_DIR = \"/home/<USER>/polarsAlpha/diskBig/\"\n"]}, {"cell_type": "code", "execution_count": 6, "id": "05a2c937", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (3_377_763_480, 9)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>symbol</th><th>hhmm</th><th>Open</th><th>High</th><th>Low</th><th>Close</th><th>Volume</th><th>Amount</th></tr><tr><td>str</td><td>str</td><td>str</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td></tr></thead><tbody><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0915&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0916&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0917&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0918&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0919&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1526&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1527&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1528&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1529&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1530&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr></tbody></table></div>"], "text/plain": ["shape: (3_377_763_480, 9)\n", "┌──────────┬───────────┬──────┬──────┬───┬──────┬───────┬────────┬────────┐\n", "│ date     ┆ symbol    ┆ hhmm ┆ Open ┆ … ┆ Low  ┆ Close ┆ Volume ┆ Amount │\n", "│ ---      ┆ ---       ┆ ---  ┆ ---  ┆   ┆ ---  ┆ ---   ┆ ---    ┆ ---    │\n", "│ str      ┆ str       ┆ str  ┆ f32  ┆   ┆ f32  ┆ f32   ┆ f32    ┆ f32    │\n", "╞══════════╪═══════════╪══════╪══════╪═══╪══════╪═══════╪════════╪════════╡\n", "│ 20150105 ┆ 000001.SZ ┆ 0915 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "│ 20150105 ┆ 000001.SZ ┆ 0916 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "│ 20150105 ┆ 000001.SZ ┆ 0917 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "│ 20150105 ┆ 000001.SZ ┆ 0918 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "│ 20150105 ┆ 000001.SZ ┆ 0919 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "│ …        ┆ …         ┆ …    ┆ …    ┆ … ┆ …    ┆ …     ┆ …      ┆ …      │\n", "│ 20231228 ┆ 301630.SZ ┆ 1526 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "│ 20231228 ┆ 301630.SZ ┆ 1527 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "│ 20231228 ┆ 301630.SZ ┆ 1528 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "│ 20231228 ┆ 301630.SZ ┆ 1529 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "│ 20231228 ┆ 301630.SZ ┆ 1530 ┆ null ┆ … ┆ null ┆ null  ┆ null   ┆ null   │\n", "└──────────┴───────────┴──────┴──────┴───┴──────┴───────┴────────┴────────┘"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["OHLCVA = pl.read_parquet(CONFIG.DATA_DIR + \"OHLCVA.parquet\")\n", "OHLCVA"]}, {"cell_type": "code", "execution_count": 7, "id": "00128ae1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (3_377_763_480, 1)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>Vwap</th></tr><tr><td>f64</td></tr></thead><tbody><tr><td>null</td></tr><tr><td>null</td></tr><tr><td>null</td></tr><tr><td>null</td></tr><tr><td>null</td></tr><tr><td>&hellip;</td></tr><tr><td>null</td></tr><tr><td>null</td></tr><tr><td>null</td></tr><tr><td>null</td></tr><tr><td>null</td></tr></tbody></table></div>"], "text/plain": ["shape: (3_377_763_480, 1)\n", "┌──────┐\n", "│ Vwap │\n", "│ ---  │\n", "│ f64  │\n", "╞══════╡\n", "│ null │\n", "│ null │\n", "│ null │\n", "│ null │\n", "│ null │\n", "│ …    │\n", "│ null │\n", "│ null │\n", "│ null │\n", "│ null │\n", "│ null │\n", "└──────┘"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["vwap = pl.read_parquet(CONFIG.DATA_DIR + \"Vwap.parquet\", columns=[\"Vwap\"])\n", "vwap"]}, {"cell_type": "code", "execution_count": 8, "id": "683a7edd", "metadata": {}, "outputs": [], "source": ["OHLCVA_Vwap = OHLCVA.hstack(vwap)"]}, {"cell_type": "code", "execution_count": 9, "id": "f56dbe23", "metadata": {}, "outputs": [], "source": ["OHLCVA_Vwap = OHLCVA_Vwap.filter(pl.col(\"hhmm\").is_in(VALID_TIME))"]}, {"cell_type": "code", "execution_count": 10, "id": "6da30218", "metadata": {}, "outputs": [], "source": ["OHLCVA_Vwap = OHLCVA_Vwap.hstack(univ_1bar.select(pl.col(\"univ_1bar\")))"]}, {"cell_type": "code", "execution_count": 11, "id": "5c9db5a7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (2_876_469_960, 12)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>symbol</th><th>hhmm</th><th>Open</th><th>High</th><th>Low</th><th>Close</th><th>Volume</th><th>Amount</th><th>Vwap</th><th>univ_1bar</th><th>datetime</th></tr><tr><td>str</td><td>str</td><td>str</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f64</td><td>i8</td><td>datetime[μs]</td></tr></thead><tbody><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0925&quot;</td><td>15.99</td><td>15.99</td><td>15.99</td><td>15.99</td><td>1.437e6</td><td>2.297763e7</td><td>15.99</td><td>1</td><td>2015-01-05 09:25:00</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0930&quot;</td><td>15.99</td><td>16.08</td><td>15.98</td><td>16.040001</td><td>5.216562e6</td><td>8.357156e7</td><td>16.020429</td><td>1</td><td>2015-01-05 09:30:00</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0931&quot;</td><td>16.049999</td><td>16.07</td><td>15.99</td><td>15.99</td><td>5.583692e6</td><td>8.9362824e7</td><td>16.004254</td><td>1</td><td>2015-01-05 09:31:00</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0932&quot;</td><td>16.0</td><td>16.040001</td><td>15.99</td><td>16.0</td><td>4.381953e6</td><td>7.0109816e7</td><td>15.999673</td><td>1</td><td>2015-01-05 09:32:00</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0933&quot;</td><td>16.01</td><td>16.059999</td><td>16.0</td><td>16.049999</td><td>1.551925e6</td><td>2.489388e7</td><td>16.040647</td><td>1</td><td>2015-01-05 09:33:00</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1455&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:55:00</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1456&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:56:00</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1457&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:57:00</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1458&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:58:00</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1459&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:59:00</td></tr></tbody></table></div>"], "text/plain": ["shape: (2_876_469_960, 12)\n", "┌──────────┬───────────┬──────┬───────────┬───┬─────────────┬───────────┬───────────┬──────────────┐\n", "│ date     ┆ symbol    ┆ hhmm ┆ Open      ┆ … ┆ Amount      ┆ Vwap      ┆ univ_1bar ┆ datetime     │\n", "│ ---      ┆ ---       ┆ ---  ┆ ---       ┆   ┆ ---         ┆ ---       ┆ ---       ┆ ---          │\n", "│ str      ┆ str       ┆ str  ┆ f32       ┆   ┆ f32         ┆ f64       ┆ i8        ┆ datetime[μs] │\n", "╞══════════╪═══════════╪══════╪═══════════╪═══╪═════════════╪═══════════╪═══════════╪══════════════╡\n", "│ 20150105 ┆ 000001.SZ ┆ 0925 ┆ 15.99     ┆ … ┆ 2.297763e7  ┆ 15.99     ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:25:00     │\n", "│ 20150105 ┆ 000001.SZ ┆ 0930 ┆ 15.99     ┆ … ┆ 8.357156e7  ┆ 16.020429 ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:30:00     │\n", "│ 20150105 ┆ 000001.SZ ┆ 0931 ┆ 16.049999 ┆ … ┆ 8.9362824e7 ┆ 16.004254 ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:31:00     │\n", "│ 20150105 ┆ 000001.SZ ┆ 0932 ┆ 16.0      ┆ … ┆ 7.0109816e7 ┆ 15.999673 ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:32:00     │\n", "│ 20150105 ┆ 000001.SZ ┆ 0933 ┆ 16.01     ┆ … ┆ 2.489388e7  ┆ 16.040647 ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:33:00     │\n", "│ …        ┆ …         ┆ …    ┆ …         ┆ … ┆ …           ┆ …         ┆ …         ┆ …            │\n", "│ 20231228 ┆ 301630.SZ ┆ 1455 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:55:00     │\n", "│ 20231228 ┆ 301630.SZ ┆ 1456 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:56:00     │\n", "│ 20231228 ┆ 301630.SZ ┆ 1457 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:57:00     │\n", "│ 20231228 ┆ 301630.SZ ┆ 1458 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:58:00     │\n", "│ 20231228 ┆ 301630.SZ ┆ 1459 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:59:00     │\n", "└──────────┴───────────┴──────┴───────────┴───┴─────────────┴───────────┴───────────┴──────────────┘"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["OHLCVA_Vwap = OHLCVA_Vwap.with_columns(\n", "    pl.concat_str([pl.col(\"date\"), pl.col(\"hhmm\")], separator=\" \")\n", "      .str.strptime(pl.Datetime, format=\"%Y%m%d %H%M\")\n", "      .alias(\"datetime\")\n", ")\n", "OHLCVA_Vwap"]}, {"cell_type": "code", "execution_count": 12, "id": "3dd4b740", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (2_876_469_960, 12)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>symbol</th><th>hhmm</th><th>Open</th><th>High</th><th>Low</th><th>Close</th><th>Volume</th><th>Amount</th><th>Vwap</th><th>univ_1bar</th><th>datetime</th></tr><tr><td>str</td><td>str</td><td>str</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>i8</td><td>datetime[μs]</td></tr></thead><tbody><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0925&quot;</td><td>15.99</td><td>15.99</td><td>15.99</td><td>15.99</td><td>1.437e6</td><td>2.297763e7</td><td>15.99</td><td>1</td><td>2015-01-05 09:25:00</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0930&quot;</td><td>15.99</td><td>16.08</td><td>15.98</td><td>16.040001</td><td>5.216562e6</td><td>8.357156e7</td><td>16.02043</td><td>1</td><td>2015-01-05 09:30:00</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0931&quot;</td><td>16.049999</td><td>16.07</td><td>15.99</td><td>15.99</td><td>5.583692e6</td><td>8.9362824e7</td><td>16.004255</td><td>1</td><td>2015-01-05 09:31:00</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0932&quot;</td><td>16.0</td><td>16.040001</td><td>15.99</td><td>16.0</td><td>4.381953e6</td><td>7.0109816e7</td><td>15.999673</td><td>1</td><td>2015-01-05 09:32:00</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0933&quot;</td><td>16.01</td><td>16.059999</td><td>16.0</td><td>16.049999</td><td>1.551925e6</td><td>2.489388e7</td><td>16.040648</td><td>1</td><td>2015-01-05 09:33:00</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1455&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:55:00</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1456&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:56:00</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1457&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:57:00</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1458&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:58:00</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1459&quot;</td><td>null</td><td>null</td><td>null</td><td>null</td><td>0.0</td><td>0.0</td><td>null</td><td>null</td><td>2023-12-28 14:59:00</td></tr></tbody></table></div>"], "text/plain": ["shape: (2_876_469_960, 12)\n", "┌──────────┬───────────┬──────┬───────────┬───┬─────────────┬───────────┬───────────┬──────────────┐\n", "│ date     ┆ symbol    ┆ hhmm ┆ Open      ┆ … ┆ Amount      ┆ Vwap      ┆ univ_1bar ┆ datetime     │\n", "│ ---      ┆ ---       ┆ ---  ┆ ---       ┆   ┆ ---         ┆ ---       ┆ ---       ┆ ---          │\n", "│ str      ┆ str       ┆ str  ┆ f32       ┆   ┆ f32         ┆ f32       ┆ i8        ┆ datetime[μs] │\n", "╞══════════╪═══════════╪══════╪═══════════╪═══╪═════════════╪═══════════╪═══════════╪══════════════╡\n", "│ 20150105 ┆ 000001.SZ ┆ 0925 ┆ 15.99     ┆ … ┆ 2.297763e7  ┆ 15.99     ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:25:00     │\n", "│ 20150105 ┆ 000001.SZ ┆ 0930 ┆ 15.99     ┆ … ┆ 8.357156e7  ┆ 16.02043  ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:30:00     │\n", "│ 20150105 ┆ 000001.SZ ┆ 0931 ┆ 16.049999 ┆ … ┆ 8.9362824e7 ┆ 16.004255 ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:31:00     │\n", "│ 20150105 ┆ 000001.SZ ┆ 0932 ┆ 16.0      ┆ … ┆ 7.0109816e7 ┆ 15.999673 ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:32:00     │\n", "│ 20150105 ┆ 000001.SZ ┆ 0933 ┆ 16.01     ┆ … ┆ 2.489388e7  ┆ 16.040648 ┆ 1         ┆ 2015-01-05   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 09:33:00     │\n", "│ …        ┆ …         ┆ …    ┆ …         ┆ … ┆ …           ┆ …         ┆ …         ┆ …            │\n", "│ 20231228 ┆ 301630.SZ ┆ 1455 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:55:00     │\n", "│ 20231228 ┆ 301630.SZ ┆ 1456 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:56:00     │\n", "│ 20231228 ┆ 301630.SZ ┆ 1457 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:57:00     │\n", "│ 20231228 ┆ 301630.SZ ┆ 1458 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:58:00     │\n", "│ 20231228 ┆ 301630.SZ ┆ 1459 ┆ null      ┆ … ┆ 0.0         ┆ null      ┆ null      ┆ 2023-12-28   │\n", "│          ┆           ┆      ┆           ┆   ┆             ┆           ┆           ┆ 14:59:00     │\n", "└──────────┴───────────┴──────┴───────────┴───┴─────────────┴───────────┴───────────┴──────────────┘"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# case Vwap to f32\n", "OHLCVA_Vwap = OHLCVA_Vwap.with_columns([\n", "    pl.col(\"Vwap\").cast(pl.Float32),\n", "])\n", "OHLCVA_Vwap"]}, {"cell_type": "code", "execution_count": 13, "id": "8a2e52ad", "metadata": {}, "outputs": [], "source": ["OHLCVA_Vwap = OHLCVA_Vwap.filter(~pl.col(\"univ_1bar\").is_null())"]}, {"cell_type": "code", "execution_count": 14, "id": "d8f30869", "metadata": {}, "outputs": [], "source": ["OHLCVA_Vwap = OHLCVA_Vwap.drop([\"univ_1bar\"])"]}, {"cell_type": "code", "execution_count": 15, "id": "680318dc", "metadata": {}, "outputs": [], "source": ["univ_1bar.select(pl.col(\"univ_1bar\")).write_parquet(\"/disk4/shared/intern/laiyc/preprocess/univ_1bar.parquet\")"]}, {"cell_type": "code", "execution_count": 16, "id": "de9583d1", "metadata": {}, "outputs": [], "source": ["# fill method:\n", "# 1. Volume, Amount: fill0\n", "# 2. Vwap: ffill\n", "# 3. Close: ffill\n", "# 4. OHL: ffill with Close\n", "OHLCVA_Vwap = OHLCVA_Vwap.with_columns([\n", "    pl.col(\"Volume\").fill_null(0.0),\n", "    pl.col(\"Amount\").fill_null(0.0),\n", "    pl.col(\"Vwap\").fill_null(strategy=\"forward\"),\n", "    pl.col(\"Close\").fill_null(strategy=\"forward\"),\n", "])"]}, {"cell_type": "code", "execution_count": 17, "id": "91e3bf72", "metadata": {}, "outputs": [], "source": ["OHLCVA_Vwap = OHLCVA_Vwap.with_columns([\n", "    pl.col(\"Open\").fill_null(pl.col(\"Close\")),\n", "    pl.col(\"High\").fill_null(pl.col(\"Close\")),\n", "    pl.col(\"Low\").fill_null(pl.col(\"Close\")),\n", "])"]}, {"cell_type": "code", "execution_count": 18, "id": "86f4f5b5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (1, 11)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>symbol</th><th>hhmm</th><th>Open</th><th>High</th><th>Low</th><th>Close</th><th>Volume</th><th>Amount</th><th>Vwap</th><th>datetime</th></tr><tr><td>u32</td><td>u32</td><td>u32</td><td>u32</td><td>u32</td><td>u32</td><td>u32</td><td>u32</td><td>u32</td><td>u32</td><td>u32</td></tr></thead><tbody><tr><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr></tbody></table></div>"], "text/plain": ["shape: (1, 11)\n", "┌──────┬────────┬──────┬──────┬───┬────────┬────────┬──────┬──────────┐\n", "│ date ┆ symbol ┆ hhmm ┆ Open ┆ … ┆ Volume ┆ Amount ┆ Vwap ┆ datetime │\n", "│ ---  ┆ ---    ┆ ---  ┆ ---  ┆   ┆ ---    ┆ ---    ┆ ---  ┆ ---      │\n", "│ u32  ┆ u32    ┆ u32  ┆ u32  ┆   ┆ u32    ┆ u32    ┆ u32  ┆ u32      │\n", "╞══════╪════════╪══════╪══════╪═══╪════════╪════════╪══════╪══════════╡\n", "│ 0    ┆ 0      ┆ 0    ┆ 0    ┆ … ┆ 0      ┆ 0      ┆ 0    ┆ 0        │\n", "└──────┴────────┴──────┴──────┴───┴────────┴────────┴──────┴──────────┘"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# see each col null count\n", "OHLCVA_Vwap.select([(pl.col(c).is_null().sum() ) for c in OHLCVA_Vwap.columns])\n"]}, {"cell_type": "code", "execution_count": null, "id": "bfeb2a47", "metadata": {}, "outputs": [], "source": ["cols = OHLCVA_Vwap.columns\n", "cols = cols[:-4] + ['Vwap', 'datetime']\n", "OHLCVA_Vwap = OHLCVA_Vwap.select(cols)"]}, {"cell_type": "code", "execution_count": 19, "id": "f94c1845", "metadata": {}, "outputs": [], "source": ["OHLCVA_Vwap.write_parquet(\"/home/<USER>/polarsAlpha/diskBig/OHLCVA_vwap.parquet\")"]}, {"cell_type": "code", "execution_count": 21, "id": "51aa6c97", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (3_752_039_470, 5)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>symbol</th><th>hhmm</th><th>y@inter@1_lag@1_full</th><th>year</th></tr><tr><td>str</td><td>str</td><td>str</td><td>f64</td><td>i64</td></tr></thead><tbody><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0915&quot;</td><td>null</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0916&quot;</td><td>null</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0917&quot;</td><td>null</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0918&quot;</td><td>null</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0919&quot;</td><td>null</td><td>2015</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1526&quot;</td><td>null</td><td>2024</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1527&quot;</td><td>null</td><td>2024</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1528&quot;</td><td>null</td><td>2024</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1529&quot;</td><td>null</td><td>2024</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1530&quot;</td><td>null</td><td>2024</td></tr></tbody></table></div>"], "text/plain": ["shape: (3_752_039_470, 5)\n", "┌──────────┬───────────┬──────┬──────────────────────┬──────┐\n", "│ date     ┆ symbol    ┆ hhmm ┆ y@inter@1_lag@1_full ┆ year │\n", "│ ---      ┆ ---       ┆ ---  ┆ ---                  ┆ ---  │\n", "│ str      ┆ str       ┆ str  ┆ f64                  ┆ i64  │\n", "╞══════════╪═══════════╪══════╪══════════════════════╪══════╡\n", "│ 20150105 ┆ 000001.SZ ┆ 0915 ┆ null                 ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0916 ┆ null                 ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0917 ┆ null                 ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0918 ┆ null                 ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0919 ┆ null                 ┆ 2015 │\n", "│ …        ┆ …         ┆ …    ┆ …                    ┆ …    │\n", "│ 20241231 ┆ 301630.SZ ┆ 1526 ┆ null                 ┆ 2024 │\n", "│ 20241231 ┆ 301630.SZ ┆ 1527 ┆ null                 ┆ 2024 │\n", "│ 20241231 ┆ 301630.SZ ┆ 1528 ┆ null                 ┆ 2024 │\n", "│ 20241231 ┆ 301630.SZ ┆ 1529 ┆ null                 ┆ 2024 │\n", "│ 20241231 ┆ 301630.SZ ┆ 1530 ┆ null                 ┆ 2024 │\n", "└──────────┴───────────┴──────┴──────────────────────┴──────┘"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["y1 = pl.read_parquet(CONFIG.RU_DIR + \"y@inter@1_lag@1_full.parquet\")\n", "y1"]}, {"cell_type": "code", "execution_count": 22, "id": "76b3fda0", "metadata": {}, "outputs": [], "source": ["y1 = y1.filter(pl.col(\"hhmm\").is_in(VALID_TIME))"]}, {"cell_type": "code", "execution_count": 23, "id": "ffd55783", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (3_195_199_690, 5)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>symbol</th><th>hhmm</th><th>y@inter@1_lag@1_full</th><th>year</th></tr><tr><td>str</td><td>str</td><td>str</td><td>f64</td><td>i64</td></tr></thead><tbody><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0925&quot;</td><td>-0.01808</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0930&quot;</td><td>-0.015343</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0931&quot;</td><td>-0.015</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0932&quot;</td><td>-0.01433</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0933&quot;</td><td>-0.012461</td><td>2015</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1455&quot;</td><td>null</td><td>2024</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1456&quot;</td><td>null</td><td>2024</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1457&quot;</td><td>null</td><td>2024</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1458&quot;</td><td>null</td><td>2024</td></tr><tr><td>&quot;20241231&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1459&quot;</td><td>null</td><td>2024</td></tr></tbody></table></div>"], "text/plain": ["shape: (3_195_199_690, 5)\n", "┌──────────┬───────────┬──────┬──────────────────────┬──────┐\n", "│ date     ┆ symbol    ┆ hhmm ┆ y@inter@1_lag@1_full ┆ year │\n", "│ ---      ┆ ---       ┆ ---  ┆ ---                  ┆ ---  │\n", "│ str      ┆ str       ┆ str  ┆ f64                  ┆ i64  │\n", "╞══════════╪═══════════╪══════╪══════════════════════╪══════╡\n", "│ 20150105 ┆ 000001.SZ ┆ 0925 ┆ -0.01808             ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0930 ┆ -0.015343            ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0931 ┆ -0.015               ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0932 ┆ -0.01433             ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0933 ┆ -0.012461            ┆ 2015 │\n", "│ …        ┆ …         ┆ …    ┆ …                    ┆ …    │\n", "│ 20241231 ┆ 301630.SZ ┆ 1455 ┆ null                 ┆ 2024 │\n", "│ 20241231 ┆ 301630.SZ ┆ 1456 ┆ null                 ┆ 2024 │\n", "│ 20241231 ┆ 301630.SZ ┆ 1457 ┆ null                 ┆ 2024 │\n", "│ 20241231 ┆ 301630.SZ ┆ 1458 ┆ null                 ┆ 2024 │\n", "│ 20241231 ┆ 301630.SZ ┆ 1459 ┆ null                 ┆ 2024 │\n", "└──────────┴───────────┴──────┴──────────────────────┴──────┘"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["y1"]}, {"cell_type": "code", "execution_count": 25, "id": "186d8942", "metadata": {}, "outputs": [], "source": ["y1 = y1.filter(pl.col(\"year\") != 2024)"]}, {"cell_type": "code", "execution_count": 26, "id": "d84a4bcb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (2_876_469_960, 5)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>symbol</th><th>hhmm</th><th>y@inter@1_lag@1_full</th><th>year</th></tr><tr><td>str</td><td>str</td><td>str</td><td>f64</td><td>i64</td></tr></thead><tbody><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0925&quot;</td><td>-0.01808</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0930&quot;</td><td>-0.015343</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0931&quot;</td><td>-0.015</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0932&quot;</td><td>-0.01433</td><td>2015</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0933&quot;</td><td>-0.012461</td><td>2015</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1455&quot;</td><td>null</td><td>2023</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1456&quot;</td><td>null</td><td>2023</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1457&quot;</td><td>null</td><td>2023</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1458&quot;</td><td>null</td><td>2023</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1459&quot;</td><td>null</td><td>2023</td></tr></tbody></table></div>"], "text/plain": ["shape: (2_876_469_960, 5)\n", "┌──────────┬───────────┬──────┬──────────────────────┬──────┐\n", "│ date     ┆ symbol    ┆ hhmm ┆ y@inter@1_lag@1_full ┆ year │\n", "│ ---      ┆ ---       ┆ ---  ┆ ---                  ┆ ---  │\n", "│ str      ┆ str       ┆ str  ┆ f64                  ┆ i64  │\n", "╞══════════╪═══════════╪══════╪══════════════════════╪══════╡\n", "│ 20150105 ┆ 000001.SZ ┆ 0925 ┆ -0.01808             ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0930 ┆ -0.015343            ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0931 ┆ -0.015               ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0932 ┆ -0.01433             ┆ 2015 │\n", "│ 20150105 ┆ 000001.SZ ┆ 0933 ┆ -0.012461            ┆ 2015 │\n", "│ …        ┆ …         ┆ …    ┆ …                    ┆ …    │\n", "│ 20231228 ┆ 301630.SZ ┆ 1455 ┆ null                 ┆ 2023 │\n", "│ 20231228 ┆ 301630.SZ ┆ 1456 ┆ null                 ┆ 2023 │\n", "│ 20231228 ┆ 301630.SZ ┆ 1457 ┆ null                 ┆ 2023 │\n", "│ 20231228 ┆ 301630.SZ ┆ 1458 ┆ null                 ┆ 2023 │\n", "│ 20231228 ┆ 301630.SZ ┆ 1459 ┆ null                 ┆ 2023 │\n", "└──────────┴───────────┴──────┴──────────────────────┴──────┘"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["y1"]}, {"cell_type": "code", "execution_count": 24, "id": "c28aa82e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (2_876_469_960, 6)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>symbol</th><th>hhmm</th><th>univ_full</th><th>year</th><th>univ_1bar</th></tr><tr><td>str</td><td>str</td><td>str</td><td>f64</td><td>i64</td><td>i8</td></tr></thead><tbody><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0925&quot;</td><td>1.0</td><td>2015</td><td>1</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0930&quot;</td><td>1.0</td><td>2015</td><td>1</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0931&quot;</td><td>1.0</td><td>2015</td><td>1</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0932&quot;</td><td>1.0</td><td>2015</td><td>1</td></tr><tr><td>&quot;20150105&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0933&quot;</td><td>1.0</td><td>2015</td><td>1</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1455&quot;</td><td>null</td><td>2023</td><td>null</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1456&quot;</td><td>null</td><td>2023</td><td>null</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1457&quot;</td><td>null</td><td>2023</td><td>null</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1458&quot;</td><td>null</td><td>2023</td><td>null</td></tr><tr><td>&quot;20231228&quot;</td><td>&quot;301630.SZ&quot;</td><td>&quot;1459&quot;</td><td>null</td><td>2023</td><td>null</td></tr></tbody></table></div>"], "text/plain": ["shape: (2_876_469_960, 6)\n", "┌──────────┬───────────┬──────┬───────────┬──────┬───────────┐\n", "│ date     ┆ symbol    ┆ hhmm ┆ univ_full ┆ year ┆ univ_1bar │\n", "│ ---      ┆ ---       ┆ ---  ┆ ---       ┆ ---  ┆ ---       │\n", "│ str      ┆ str       ┆ str  ┆ f64       ┆ i64  ┆ i8        │\n", "╞══════════╪═══════════╪══════╪═══════════╪══════╪═══════════╡\n", "│ 20150105 ┆ 000001.SZ ┆ 0925 ┆ 1.0       ┆ 2015 ┆ 1         │\n", "│ 20150105 ┆ 000001.SZ ┆ 0930 ┆ 1.0       ┆ 2015 ┆ 1         │\n", "│ 20150105 ┆ 000001.SZ ┆ 0931 ┆ 1.0       ┆ 2015 ┆ 1         │\n", "│ 20150105 ┆ 000001.SZ ┆ 0932 ┆ 1.0       ┆ 2015 ┆ 1         │\n", "│ 20150105 ┆ 000001.SZ ┆ 0933 ┆ 1.0       ┆ 2015 ┆ 1         │\n", "│ …        ┆ …         ┆ …    ┆ …         ┆ …    ┆ …         │\n", "│ 20231228 ┆ 301630.SZ ┆ 1455 ┆ null      ┆ 2023 ┆ null      │\n", "│ 20231228 ┆ 301630.SZ ┆ 1456 ┆ null      ┆ 2023 ┆ null      │\n", "│ 20231228 ┆ 301630.SZ ┆ 1457 ┆ null      ┆ 2023 ┆ null      │\n", "│ 20231228 ┆ 301630.SZ ┆ 1458 ┆ null      ┆ 2023 ┆ null      │\n", "│ 20231228 ┆ 301630.SZ ┆ 1459 ┆ null      ┆ 2023 ┆ null      │\n", "└──────────┴───────────┴──────┴───────────┴──────┴───────────┘"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["univ_1bar"]}, {"cell_type": "code", "execution_count": 29, "id": "abfb0fdf", "metadata": {}, "outputs": [], "source": ["y1 = y1.hstack(univ_1bar.select(pl.col(\"univ_1bar\")))"]}, {"cell_type": "code", "execution_count": 31, "id": "56c31428", "metadata": {}, "outputs": [], "source": ["y1 = y1.filter(pl.col(\"univ_1bar\") == 1).select(pl.col(\"y@inter@1_lag@1_full\").cast(pl.Float32).alias(\"y1\"))"]}, {"cell_type": "code", "execution_count": 32, "id": "78c80cbd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (1_671_146_297, 1)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>y1</th></tr><tr><td>f32</td></tr></thead><tbody><tr><td>-0.01808</td></tr><tr><td>-0.015343</td></tr><tr><td>-0.015</td></tr><tr><td>-0.01433</td></tr><tr><td>-0.012461</td></tr><tr><td>&hellip;</td></tr><tr><td>0.015969</td></tr><tr><td>0.015969</td></tr><tr><td>0.015969</td></tr><tr><td>0.015969</td></tr><tr><td>0.018182</td></tr></tbody></table></div>"], "text/plain": ["shape: (1_671_146_297, 1)\n", "┌───────────┐\n", "│ y1        │\n", "│ ---       │\n", "│ f32       │\n", "╞═══════════╡\n", "│ -0.01808  │\n", "│ -0.015343 │\n", "│ -0.015    │\n", "│ -0.01433  │\n", "│ -0.012461 │\n", "│ …         │\n", "│ 0.015969  │\n", "│ 0.015969  │\n", "│ 0.015969  │\n", "│ 0.015969  │\n", "│ 0.018182  │\n", "└───────────┘"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["y1"]}, {"cell_type": "code", "execution_count": 33, "id": "cb00a1b4", "metadata": {}, "outputs": [], "source": ["y1.write_parquet(\"/home/<USER>/polarsAlpha/diskBig/y1.parquet\")"]}, {"cell_type": "markdown", "id": "8d5cb259", "metadata": {}, "source": ["## BacktestClass"]}, {"cell_type": "code", "execution_count": null, "id": "6ec2cf0c", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "import numpy as np\n", "from typing import List, Dict, Tuple, Optional\n", "\n", "class MinuteFactorBacktest:\n", "    \"\"\"分钟频因子回测类\"\"\"\n", "    \n", "    def __init__(self, df: pl.DataFrame, factor_name: str):\n", "        \"\"\"\n", "        初始化回测类\n", "        \n", "        Args:\n", "            df: polars DataFrame, 包含columns: date, symbol, hhmm, factor_123, y1\n", "        \"\"\"\n", "        self.df = df\n", "        self.factor_name = factor_name\n", "        self.ic_results = None\n", "\n", "        \n", "    def calculate_cross_sectional_ic(self, specific_times: Optional[List[str]] = None) -> Dict:\n", "        \"\"\"\n", "        计算截面IC\n", "        \n", "        Args:\n", "            specific_times: 指定的时间点列表，如[\"0959\", \"1029\", \"1059\", \"1129\", \"1329\", \"1359\", \"1429\"]\n", "                          如果为None，则计算所有时间点\n", "        \n", "        Returns:\n", "            Dict包含IC统计信息\n", "        \"\"\"\n", "        # 如果指定了特定时间点，先过滤数据\n", "        if specific_times is not None:\n", "            df_filtered = self.df.filter(pl.col('hhmm').is_in(specific_times))\n", "        else:\n", "            df_filtered = self.df\n", "        \n", "        # 按date和hhmm分组计算每个截面的IC\n", "        ic_df = df_filtered.drop_nulls([self.factor_name, 'y1']).group_by(['datetime']).agg([\n", "            # 计算样本数量（去除缺失值后）\n", "            pl.col(self.factor_name).len().alias('sample_size'),\n", "            # 计算Spearman相关系数（RankIC）\n", "            pl.corr(self.factor_name, 'y1', method='spearman').alias('ic')\n", "        ]).filter(\n", "            (pl.col('ic').is_not_nan()) & \n", "            (pl.col('ic').is_not_null())\n", "        )\n", "        \n", "        # 计算IC统计指标\n", "        ic_stats = {\n", "            'mean_ic': ic_df.select(pl.col('ic').mean()).item(),\n", "            'std_ic': ic_df.select(pl.col('ic').std()).item(),\n", "            'ir': ic_df.select(pl.col('ic').mean() / pl.col('ic').std()).item(),\n", "            'win_rate': ic_df.select((pl.col('ic') > 0).mean()).item(),\n", "            'ic_positive_rate': ic_df.select((pl.col('ic') > 0).mean()).item(),\n", "            'abs_ic_mean': ic_df.select(pl.col('ic').abs().mean()).item(),\n", "            'max_ic': ic_df.select(pl.col('ic').max()).item(),\n", "            'min_ic': ic_df.select(pl.col('ic').min()).item(),\n", "            'total_periods': ic_df.height,\n", "            'avg_sample_size': ic_df.select(pl.col('sample_size').mean()).item()\n", "        }\n", "        \n", "        self.ic_results = {\n", "            'ic_series': ic_df,\n", "            'ic_stats': ic_stats\n", "        }\n", "        \n", "        return self.ic_results\n", "    \n", "    \n", "    def print_ic_summary(self):\n", "        \"\"\"打印IC统计摘要\"\"\"\n", "        if self.ic_results is None:\n", "            print(\"请先运行 calculate_cross_sectional_ic()\")\n", "            return\n", "        \n", "        stats = self.ic_results['ic_stats']\n", "        print(\"=\" * 50)\n", "        print(\"截面IC统计摘要\")\n", "        print(\"=\" * 50)\n", "        print(f\"平均IC: {stats['mean_ic']:.4f}\")\n", "        print(f\"IC标准差: {stats['std_ic']:.4f}\")\n", "        print(f\"信息比率(IR): {stats['ir']:.4f}\")\n", "        print(f\"IC胜率: {stats['win_rate']:.4f}\")\n", "        print(f\"绝对IC均值: {stats['abs_ic_mean']:.4f}\")\n", "        print(f\"最大IC: {stats['max_ic']:.4f}\")\n", "        print(f\"最小IC: {stats['min_ic']:.4f}\")\n", "        print(f\"总观测期数: {stats['total_periods']}\")\n", "        print(f\"平均样本量: {stats['avg_sample_size']:.1f}\")\n", "\n"]}, {"cell_type": "markdown", "id": "1e672771", "metadata": {}, "source": ["## factor_21"]}, {"cell_type": "code", "execution_count": null, "id": "3fb393d0", "metadata": {}, "outputs": [], "source": ["def factor_21_new(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:\n", "    \"\"\"\n", "    计算Alpha42因子：VWAP与CLOSE的差值排名除以VWAP与CLOSE的和的排名\n", "    \n", "    参数:\n", "        w: 基准参数（本因子不使用窗口，设为None）\n", "        uni_col: 单一基础列参数（本因子涉及多列，设为None）\n", "        eps: 防止除零的小常数\n", "        \n", "    返回:\n", "        pl.Expr: 因子表达式\n", "    \"\"\"\n", "    # 计算VWAP与CLOSE的差值和和\n", "    vdiff = pl.col(\"Vwap\") - pl.col(\"Close\")\n", "    vsum = pl.col(\"Vwap\") + pl.col(\"Close\")\n", "\n", "    # 计算横截面排名（在同一时间点datetime内排名）\n", "    rank_diff = vdiff.rank(method=\"dense\", descending=True).over([\"datetime\"]) \n", "    rank_sum = vsum.rank(method=\"dense\", descending=True).over([\"datetime\"]) \n", "\n", "    # 计算最终因子值：差值排名除以和的排名\n", "    return (rank_diff / (rank_sum + eps)).cast(pl.Float32).alias(\"factor_21\")\n", "\n", "s_new = df.select(factor_21_new())\n", "s_new"]}, {"cell_type": "code", "execution_count": null, "id": "bf5889f1", "metadata": {}, "outputs": [], "source": ["def factor_21(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:\n", "    vdiff = pl.col(\"Vwap\") - pl.col(\"Close\")\n", "    vsum  = pl.col(\"Vwap\") + pl.col(\"Close\")\n", "\n", "    return (\n", "        vdiff.rank().over([\"datetime\"])\n", "        / (vsum.rank().over([\"datetime\"]) + eps)\n", "    ).alias(\"factor_21\").cast(pl.Float32)\n", "\n", "\n", "s = df.select(factor_21())\n", "s"]}, {"cell_type": "code", "execution_count": null, "id": "f7e8f4f5", "metadata": {}, "outputs": [], "source": ["df = df.drop([\"y1\", \"factor_21\"])"]}, {"cell_type": "code", "execution_count": null, "id": "417b445c", "metadata": {}, "outputs": [], "source": ["y1 = pl.read_parquet(CONFIG.LOCAL_DIR + \"y1.parquet\")\n", "# concat df's CONFIG.DSH_COLS + s + y1\n", "df = df.hstack(y1)\n", "df = df.hstack(s_new)\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "65ae49bb", "metadata": {}, "outputs": [], "source": ["backtest = MinuteFactorBacktest(df, factor_name=\"factor_21\")\n", "\n", "\n", "ic_results = backtest.calculate_cross_sectional_ic(specific_times=CONFIG.SPEC_TIME)\n", "print(f\"ic_results, all hhmm: {ic_results}\")"]}, {"cell_type": "code", "execution_count": null, "id": "0fa49c5f", "metadata": {}, "outputs": [], "source": ["ic = ic_results['ic_series']\n", "ic = ic.drop_nans(subset=[\"ic\"])\n", "\n", "print(f\"IC mean: {ic.select(pl.col('ic').mean()).item()}\")\n", "for hhmm in [\"0959\", \"1029\", \"1059\", \"1129\", \"1329\", \"1359\", \"1429\"]:\n", "    print(f\"IC mean hhmm={hhmm}: {ic.filter(pl.col('hhmm') == hhmm).select(pl.col('ic').mean()).item()}\")"]}, {"cell_type": "code", "execution_count": null, "id": "53568d22", "metadata": {}, "outputs": [], "source": ["ic"]}, {"cell_type": "markdown", "id": "d3ddbaee", "metadata": {}, "source": ["## factor_123"]}, {"cell_type": "code", "execution_count": null, "id": "ad1f81ad", "metadata": {}, "outputs": [], "source": ["def factor_123_expr() -> pl.Expr:\n", "    \"\"\"\n", "    Returns a pl.Expr computing:\n", "       ((close – low) * (open + eps)^5)\n", "        /\n", "       ((high – close) * (close + eps)^5 + eps)\n", "    \"\"\"\n", "    eps = 1e-8\n", "    num = (pl.col(\"Close\") - pl.col(\"Low\")) * (pl.col(\"Open\") + eps) ** 5\n", "    den = (pl.col(\"High\") - pl.col(\"Close\")) * (pl.col(\"Close\") + eps) ** 5 + eps\n", "    return (num / den)"]}, {"cell_type": "code", "execution_count": null, "id": "503fa095", "metadata": {}, "outputs": [], "source": ["s = df.select(factor_123_expr().alias(\"factor_123\"))\n", "s"]}, {"cell_type": "code", "execution_count": null, "id": "b2e17121", "metadata": {}, "outputs": [], "source": ["y1 = pl.read_parquet(CONFIG.PREPROCESS_DIR + \"y1.parquet\")\n", "# concat df's CONFIG.DSH_COLS + s + y1\n", "df = df.hstack(s)\n", "df = df.hstack(y1)\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "e8c7432c", "metadata": {}, "outputs": [], "source": ["\n", "# 创建回测实例\n", "backtest = MinuteFactorBacktest(df, factor_name='factor_123')\n", "\n", "# 计算截面IC（所有时间点）\n", "ic_results = backtest.calculate_cross_sectional_ic(specific_times=CONFIG.SPEC_TIME)\n", "print(f\"ic_results, all hhmm: {ic_results}\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "cfc35eca", "metadata": {}, "outputs": [], "source": ["ic = ic_results['ic_series']\n", "ic = ic.drop_nans(subset=[\"ic\"])\n", "\n", "print(f\"IC mean: {ic.select(pl.col('ic').mean()).item()}\")\n", "for hhmm in [\"0959\", \"1029\", \"1059\", \"1129\", \"1329\", \"1359\", \"1429\"]:\n", "    print(f\"IC mean hhmm={hhmm}: {ic.filter(pl.col('hhmm') == hhmm).select(pl.col('ic').mean()).item()}\")"]}, {"cell_type": "markdown", "id": "1bb11838", "metadata": {}, "source": ["## factor_12"]}, {"cell_type": "code", "execution_count": null, "id": "3a35061b", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "import numpy as np\n", "from typing import Union\n", "\n", "def rolling_rank_numpy(values: np.ndarray, window_size: int, min_periods: int = 1) -> np.ndarray:\n", "    \"\"\"\n", "    使用numpy实现滚动排名\n", "    \"\"\"\n", "    n = len(values)\n", "    result = np.full(n, np.nan)\n", "    \n", "    for i in range(n):\n", "        start_idx = max(0, i - window_size + 1)\n", "        end_idx = i + 1\n", "        \n", "        # 检查是否满足最小期数要求\n", "        if end_idx - start_idx < min_periods:\n", "            continue\n", "            \n", "        window_data = values[start_idx:end_idx]\n", "        \n", "        # 处理NaN值\n", "        valid_mask = ~np.isnan(window_data)\n", "        if np.sum(valid_mask) < min_periods:\n", "            continue\n", "            \n", "        # 计算排名（scipy.stats.rankdata的简化版本）\n", "        valid_data = window_data[valid_mask]\n", "        current_value = values[i]\n", "        \n", "        if not np.isnan(current_value):\n", "            # 计算当前值在窗口中的排名\n", "            rank = np.sum(valid_data <= current_value)\n", "            result[i] = rank\n", "    \n", "    return result\n", "\n", "def create_rolling_rank_expr(column: str, window_size: int, min_periods: int = 1) -> pl.Expr:\n", "    \"\"\"\n", "    创建滚动排名表达式\n", "    \"\"\"\n", "    def rolling_rank_func(s: pl.Series) -> pl.Series:\n", "        values = s.to_numpy()\n", "        ranks = rolling_rank_numpy(values, window_size, min_periods)\n", "        return pl.Series(ranks)\n", "    \n", "    return pl.col(column).map_batches(rolling_rank_func, return_dtype=pl.Float64)\n", "\n", "# 修改后的factor_12_expr函数\n", "def factor_12_expr(\n", "    w: int | None = 5, \n", "    uni_col: str | None = \"Close\", \n", "    eps: float | None = 1e-8\n", ") -> pl.Expr:\n", "    \n", "    window_configs = {\n", "        'n1': 5.0,\n", "        'n2': 2.0,\n", "        'n3': 1.0,\n", "        'n4': 6.0,\n", "        'n5': 5.0\n", "    }\n", "\n", "    def calculate_window_sizes(w1_input):\n", "        import numpy as np\n", "        \n", "        w_max = 300.0\n", "        lambda_rate = 0.1\n", "        alpha = 1.0\n", "        \n", "        w1 = max(1.0, float(w1_input))\n", "        \n", "        min_base = min(window_configs.values())\n", "        max_base = max(window_configs.values())\n", "        \n", "        if max_base == min_base:\n", "            base_val = min_base\n", "            final_sizes = {}\n", "            for name in window_configs:\n", "                final_value = min(max(base_val, w1), w_max)\n", "                final_sizes[name] = int(round(final_value))\n", "            return final_sizes\n", "        \n", "        final_sizes = {}\n", "        \n", "        if w1 < min_base:\n", "            max_window_current_val = w1 * (max_base / min_base)\n", "            for name, base_value in window_configs.items():\n", "                final_value = max_window_current_val * (base_value / max_base)\n", "                final_sizes[name] = int(round(final_value))\n", "        elif w1 == min_base:\n", "            for name, base_value in window_configs.items():\n", "                final_sizes[name] = int(round(base_value))\n", "        else:\n", "            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))\n", "            dynamic_max = min(dynamic_max, w_max)\n", "            for name, base_value in window_configs.items():\n", "                position = ((base_value - min_base) / (max_base - min_base)) ** alpha\n", "                target_range_size = max(0, dynamic_max - w1)\n", "                final_value = w1 + position * target_range_size\n", "                final_value = min(max(final_value, w1), w_max)\n", "                final_sizes[name] = int(round(final_value))\n", "        \n", "        return final_sizes\n", "    \n", "    window_sizes = calculate_window_sizes(w)\n", "    n1 = window_sizes['n1']\n", "    n2 = window_sizes['n2']\n", "    n3 = window_sizes['n3']\n", "    n4 = window_sizes['n4']\n", "    n5 = window_sizes['n5']\n", "\n", "    minp = w\n", "    \n", "    # 1) log 收益率\n", "    expr_ret = (\n", "        (np.log(pl.col(uni_col) + eps) - np.log(pl.col(uni_col).shift(1) + eps))\n", "        .alias(\"returns\")\n", "    )\n", "\n", "    # 2) Δ(close, n1)\n", "    expr_delta = (\n", "        pl.col(uni_col).diff(n1)\n", "        .alias(\"delta\")\n", "    )\n", "\n", "    # 3) rank1 = -rank(delta) over n5 (使用自定义rolling_rank)\n", "    expr_r1 = (\n", "        -create_rolling_rank_expr(\"delta\", n5, minp)\n", "        .alias(\"rank1\")\n", "    )\n", "\n", "    # 4) rank2 = rank(rank1) over n5\n", "    expr_r2 = (\n", "        create_rolling_rank_expr(\"rank1\", n5, minp)\n", "        .alias(\"rank2\")\n", "    )\n", "\n", "    # 5) ts_min(rank2, n2)\n", "    expr_tsmin = (\n", "        pl.col(\"rank2\")\n", "          .rolling_min(window_size=n2, min_periods=minp)\n", "        .alias(\"ts_min\")\n", "    )\n", "\n", "    # 6) sum sum_val = sum(ts_min, n3)\n", "    expr_sum = (\n", "        pl.col(\"ts_min\")\n", "          .rolling_sum(window_size=n3, min_periods=minp)\n", "        .alias(\"sum_val\")\n", "    )\n", "\n", "    # 7) log_val = log(sum_val + eps)\n", "    expr_log = (\n", "        np.log(pl.col(\"sum_val\") + eps)\n", "        .alias(\"log_val\")\n", "    )\n", "\n", "    # 8) scale_val = (log_val - rolling_mean)/rolling_std\n", "    mean5 = pl.col(\"log_val\").rolling_mean(window_size=n5, min_periods=minp)\n", "    std5  = (pl.col(\"log_val\").rolling_std(window_size=n5, min_periods=minp) + eps)\n", "    expr_scale = (\n", "        ((pl.col(\"log_val\") - mean5) / std5)\n", "        .alias(\"scale_val\")\n", "    )\n", "\n", "    # 9) rank3, rank4 on scale_val\n", "    expr_r3 = (\n", "        create_rolling_rank_expr(\"scale_val\", n5, minp)\n", "        .alias(\"rank3\")\n", "    )\n", "    expr_r4 = (\n", "        create_rolling_rank_expr(\"rank3\", n5, minp)\n", "        .alias(\"rank4\")\n", "    )\n", "\n", "    # 10) min_val = clip(rank4, upper=5)\n", "    expr_min = (\n", "        pl.col(\"rank4\").clip(5)\n", "        .alias(\"min_val\")\n", "    )\n", "\n", "    # 11) ts_rank of delayed_returns = -returns.shift(n4)\n", "    expr_dret = (\n", "        -pl.col(\"returns\").shift(n4)\n", "        .alias(\"delayed_returns\")\n", "    )\n", "    expr_tsr = (\n", "        create_rolling_rank_expr(\"delayed_returns\", n5, minp)\n", "        .alias(\"ts_rank\")\n", "    )\n", "\n", "    # 12) 最终合成 factor_12\n", "    expr_f12 = (\n", "        (pl.col(\"min_val\") + pl.col(\"ts_rank\"))\n", "        .alias(\"factor_12\")\n", "    )\n", "\n", "    # 返回所有需要的表达式\n", "    return [\n", "        expr_ret,\n", "        expr_delta,\n", "        expr_r1,\n", "        expr_r2,\n", "        expr_tsmin,\n", "        expr_sum,\n", "        expr_log,\n", "        expr_scale,\n", "        expr_r3,\n", "        expr_r4,\n", "        expr_min,\n", "        expr_dret,\n", "        expr_tsr,\n", "        expr_f12\n", "    ]\n", "\n", "# 使用示例\n", "# df = df.with_columns(factor_12_expr(w=5, uni_col=\"Close\"))"]}, {"cell_type": "code", "execution_count": null, "id": "f8b8e7aa", "metadata": {}, "outputs": [], "source": ["s = df.with_columns(factor_12_expr(w=5, uni_col=\"Close\"))"]}, {"cell_type": "code", "execution_count": null, "id": "0a257c1a", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "\n", "def rolling_rank(s: pl.Series, method=\"ordinal\"):\n", "    \"\"\"返回窗口中最后一个元素的排名（含自身），method同 pandas/Polars 的 rank 方法。\"\"\"\n", "    # Polars `rank` 在 Series 级别即可用\n", "    return int(s.rank(method=method)[-1])\n", "\n", "w = 5  # 窗口长度\n", "df = df.with_columns(\n", "    pl.col(\"value\")\n", "      .rolling_apply(lambda s: rolling_rank(s, method=\"dense\"), window_size=w)\n", "      .alias(f\"rolling_rank_{w}\")\n", ")\n"]}], "metadata": {"kernelspec": {"display_name": "lyc", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}