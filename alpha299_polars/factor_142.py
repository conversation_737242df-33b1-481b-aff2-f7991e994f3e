# Alpha299因子 - factor_142 (Polars版本)
# 原始因子编号: 142
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_142(w: int | None = 7, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于开盘价变化和开盘价-成交量相关性的复合因子（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
        uni_col: 单一基础数据列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'decay_linear_1': 7,        # 开盘价变化线性衰减窗口
        'correlation_window': 15,   # 开盘价-成交量相关性窗口
        'decay_linear_2': 7         # 相关性线性衰减窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    decay_linear_1 = window_sizes['decay_linear_1']
    correlation_window = window_sizes['correlation_window']
    decay_linear_2 = window_sizes['decay_linear_2']

    # 1. 计算开盘价的一期差分
    delta_open = pl.col("Open") - pl.col("Open").shift(1)

    # 2. 计算DL1 = DecayLinear(DELTA(OPEN, 1), decay_linear_1)
    DL1 = Ops.decaylinear(delta_open, decay_linear_1)

    # 3. 简化实现：使用时序排名代替横截面排名
    R1 = Ops.rolling_rank(DL1, decay_linear_1)

    # 4. 计算开盘价与成交量的相关性
    Corr_OV = Ops.rolling_corr("Open", "Volume", correlation_window)

    # 5. 计算DL2 = DecayLinear(Corr_OV, decay_linear_2)
    DL2 = Ops.decaylinear(Corr_OV, decay_linear_2)

    # 6. 简化实现：使用时序排名代替横截面排名
    R2 = Ops.rolling_rank(DL2, decay_linear_2)

    # 7. 计算最终因子值：-min(R1, R2)
    factor_result = -pl.min_horizontal([R1, R2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_142")
