# Alpha299因子 - factor_178 (Polars版本)
# 原始因子编号: 178
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_178(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha7因子：VWAP与收盘价偏离及成交量变化因子（简化版）
    
    参数:
        w: 滚动窗口天数，默认3天
        uni_col: 单一基础数据列参数，此处设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'w': 3  # 基础窗口参数
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    actual_w = window_sizes['w']

    # 1. 计算VWAP
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. 计算偏离度
    deviation = vwap - pl.col("Close")

    # 3. 计算滚动最大值和最小值
    D_max3 = Ops.rolling_max(deviation, actual_w)
    D_min3 = Ops.rolling_min(deviation, actual_w)

    # 4. 简化实现：使用时序排名代替横截面排名
    R_Dmax = Ops.rolling_rank(D_max3, actual_w)
    R_Dmin = Ops.rolling_rank(D_min3, actual_w)

    # 5. 计算成交量的差分
    delta_vol = pl.col("Volume") - pl.col("Volume").shift(actual_w)

    # 6. 对delta_vol进行滚动标准化
    delta_vol_mean = Ops.rolling_mean(delta_vol, actual_w)
    delta_vol_std = Ops.rolling_std(delta_vol, actual_w)
    Z_delta_vol = (delta_vol - delta_vol_mean) / (delta_vol_std + eps)

    # 7. 简化实现：使用时序排名代替横截面排名
    R_delta_vol = Ops.rolling_rank(Z_delta_vol, actual_w)

    # 8. 计算最终因子值
    factor_result = (R_Dmax + R_Dmin) * R_delta_vol

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_178")
