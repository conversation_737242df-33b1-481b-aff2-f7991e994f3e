# Alpha299因子 - factor_228 (Polars版本)
# 原始因子编号: 228
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_228(w: int | None = 10, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha150因子：基于成交量平方根与收盘价排名-VWAP最大值协方差的Z-score和收盘价Z-score排名的和（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
        uni_col: 单一基础数据列名称（默认'close'）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'rank_window': 20,  # ts_rank窗口
        'cov_window': 10,   # ts_cov窗口
        'zscore_window1': 10,  # ts_zscore窗口1
        'zscore_window2': 20   # ts_zscore窗口2
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['rank_window']  # ts_rank窗口
    n2 = window_sizes['cov_window']   # ts_cov窗口
    n3 = window_sizes['zscore_window1']  # ts_zscore窗口1
    n4 = window_sizes['zscore_window2']  # ts_zscore窗口2

    # 1. 计算成交量的绝对值平方根
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    sqrt_volume = (volume_protected.abs()).sqrt()

    # 2. 计算收盘价的滚动排名（ts_rank）
    ts_rank_close = Ops.rolling_rank("Close", n1)

    # 3. 取ts_rank和vwap的较大值
    gp_max_value = pl.max_horizontal([ts_rank_close, pl.col("Vwap")])

    # 4. 计算sqrt_volume和gp_max_value的滚动协方差
    ts_cov_value = Ops.rolling_cov(sqrt_volume, gp_max_value, n2)

    # 5. 对协方差结果进行滚动标准化
    cov_mean = Ops.rolling_mean(ts_cov_value, n3)
    cov_std = Ops.rolling_std(ts_cov_value, n3)
    ts_zscore_X1 = (ts_cov_value - cov_mean) / (cov_std + eps)

    # 6. 对收盘价进行滚动标准化
    close_mean = Ops.rolling_mean("Close", n4)
    close_std = Ops.rolling_std("Close", n4)
    ts_zscore_close = (pl.col("Close") - close_mean) / (close_std + eps)

    # 7. 对标准化后的收盘价进行排名 - 简化实现：使用时序排名代替横截面排名
    rank_T4 = Ops.rolling_rank(ts_zscore_close, n4)

    # 8. 计算最终因子值
    factor_result = ts_zscore_X1 + rank_T4

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_228")
