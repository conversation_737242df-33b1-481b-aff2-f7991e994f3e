# Alpha299因子 - factor_34 (Polars版本)
# 原始因子编号: 34
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_34(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算复杂因子：基于VWAP行业中性化、截面排名、相关性等的复合计算
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 3,       # DELTA窗口
        'ts_max_window': 5,      # ts_max窗口
        'corr_window': 5,        # 相关性窗口
        'ts_rank_window': 10,    # ts_rank窗口
        'adv_window': 20         # ADV计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delta_window = window_sizes['delta_window']
    ts_max_window = window_sizes['ts_max_window']
    corr_window = window_sizes['corr_window']
    ts_rank_window = window_sizes['ts_rank_window']
    adv_window = window_sizes['adv_window']

    # 使用别名避免重复字段问题
    # 1. 计算VWAP（简化版，直接使用现有的Vwap列）
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    vwap = pl.col("Vwap").alias("_vwap")

    # 2. 行业中性化VWAP（简化实现：使用滚动均值代替全市场均值）
    vwap_mean = Ops.rolling_mean(vwap, 20).alias("_vwap_mean")
    indneutralize_vwap = (vwap - vwap_mean).alias("_indneutralize_vwap")

    # 3. 计算delta_vwap
    delta_vwap = (indneutralize_vwap - indneutralize_vwap.shift(delta_window)).alias("_delta_vwap")

    # 4. ts_max
    ts_max_delta = Ops.rolling_max(delta_vwap, ts_max_window).alias("_ts_max_delta")

    # 5. 截面排名（简化实现：使用时序排名）
    rank_part = Ops.rolling_rank(ts_max_delta, 20).alias("_rank_part")

    # 6. 加权价格
    weighted_price = (0.490655 * pl.col("Close") + (1 - 0.490655) * vwap).alias("_weighted_price")

    # 7. ADV
    adv20 = Ops.rolling_mean(volume_protected, adv_window).alias("_adv20")

    # 8. 相关性
    correlation = Ops.rolling_corr(weighted_price, adv20, corr_window).alias("_correlation")

    # 9. ts_rank
    ts_rank_part = Ops.rolling_rank(correlation, ts_rank_window).alias("_ts_rank_part")

    # 10. 组合并取负
    # 确保底数大于0，指数不为nan
    rank_part_clipped = pl.when(rank_part > eps).then(rank_part).otherwise(eps).alias("_rank_part_clipped")
    ts_rank_part_protected = pl.when(ts_rank_part.is_not_null()).then(ts_rank_part).otherwise(0).alias("_ts_rank_part_protected")
    
    factor_result = -(rank_part_clipped.pow(ts_rank_part_protected))

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_34")
