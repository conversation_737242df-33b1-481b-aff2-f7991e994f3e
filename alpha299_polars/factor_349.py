# Alpha299因子 - factor_349 (Polars版本)
# 原始因子编号: 349
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_349(w: int | None = 5, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha183因子：基于close的二阶差分Z-score加上amount截面排名的滚动最大值（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta1_window': 16,    # 第一个delta的窗口
        'delta2_window': 5,     # 第二个delta的窗口
        'ts_zscore_window': 20, # ts_zscore的窗口
        'ts_max_window': 14,    # ts_max的窗口
        'rank_window': 20       # 时序排名窗口（简化实现）
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delta1_window = window_sizes['delta1_window']
    delta2_window = window_sizes['delta2_window']
    ts_zscore_window = window_sizes['ts_zscore_window']
    ts_max_window = window_sizes['ts_max_window']
    rank_window = window_sizes['rank_window']

    # 使用别名避免重复字段问题
    # 1. 计算收盘价的第一阶差分
    delta1 = (pl.col(uni_col) - pl.col(uni_col).shift(delta1_window)).alias("_delta1")

    # 2. 计算delta1的第二阶差分
    delta2 = (delta1 - delta1.shift(delta2_window)).alias("_delta2")

    # 3. 对delta2进行滚动Z-score标准化
    delta2_mean = Ops.rolling_mean(delta2, ts_zscore_window).alias("_delta2_mean")
    delta2_std = Ops.rolling_std(delta2, ts_zscore_window).alias("_delta2_std")
    zscore_delta2 = ((delta2 - delta2_mean) / (delta2_std + eps)).alias("_zscore_delta2")

    # 4. 简化实现：使用时序排名代替横截面排名
    rank_amount = Ops.rolling_rank("Amount", rank_window).alias("_rank_amount")

    # 5. 计算rank_amount的滚动最大值
    ts_max_rank = Ops.rolling_max(rank_amount, ts_max_window).alias("_ts_max_rank")

    # 6. 计算zscore_delta2与ts_max_rank的和
    factor_result = zscore_delta2 + ts_max_rank

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_349")
