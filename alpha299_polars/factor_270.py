# Alpha299因子 - factor_270 (Polars版本)
# 原始因子编号: 270
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_270(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha218因子：基于arctan(volume)与volume-vwap相关系数乘积的对数绝对值与vwap差分最大值的平方根
    
    参数:
        w: 核心可调参数，用于推导其他窗口长度，默认5
        uni_col: 单一基础列参数（此处设为None，因涉及多列运算）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 5,       # DELTA计算窗口
        'corr_window': 10        # TS_CORR计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delta_window = window_sizes['delta_window']
    corr_window = window_sizes['corr_window']

    # 步骤1: 计算ARCTAN(VOLUME)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    arctan_volume = (volume_protected + eps).arctan()

    # 步骤2: 计算TS_CORR(corr_window, VOLUME, VWAP)
    ts_corr = Ops.rolling_corr(volume_protected, "Vwap", corr_window)

    # 步骤3: 相乘
    product = arctan_volume * ts_corr

    # 步骤4: LOG(绝对值)
    log_result = (product.abs() + eps).log()

    # 步骤5: DELTA(VWAP, delta_window)
    delta_vwap = pl.col("Vwap") - pl.col("Vwap").shift(delta_window)

    # 步骤6: GP_MAX
    gp_max = pl.max_horizontal([log_result, delta_vwap])

    # 步骤7: SQRT
    factor_result = gp_max.abs().sqrt()

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_270")
