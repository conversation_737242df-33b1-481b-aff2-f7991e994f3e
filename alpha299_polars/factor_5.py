# Alpha299因子 - factor_5 (Polars版本)
# 原始因子编号: 5
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_5(w: int | None = 7, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算因子5：基于收益率累计和排名的复合因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
        uni_col: 单一基础列参数，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 7        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    main_window = window_sizes['main_window']
    
    # 参数推导
    delay_window = w
    diff_window = w
    sum_window = max(1, int(w / 7 * 250))  # 因子公式中指定250期，但要确保至少为1
    
    # 计算收益率（百分比变化）
    returns = (pl.col(uni_col) / pl.col(uni_col).shift(1) - 1)
    
    # 计算Close的延迟和差分
    delayed_close = pl.col(uni_col).shift(delay_window)
    delta_close = pl.col(uni_col) - pl.col(uni_col).shift(diff_window)
    
    # 计算Close延迟和差分的和
    sum_part = delayed_close + delta_close
    
    # 取符号
    sign_part = sum_part.sign()
    
    # 计算收益率的累计和
    returns_sum = Ops.rolling_sum(returns, sum_window)
    
    # 加1后排名（使用滚动排名）
    returns_plus_one = returns_sum + 1
    rank_returns = Ops.rolling_rank(returns_plus_one, sum_window)
    
    # 计算rank_part = 1 + rank
    rank_part = 1 + rank_returns
    
    # 计算最终因子：-sign_part * rank_part，只在最后使用一次.over()
    return (-(sign_part * rank_part)).over("symbol").cast(pl.Float32).alias("factor_5")
