# Alpha299因子 - factor_211 (Polars版本)
# 原始因子编号: 211
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_211(w: int | None = 8, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha127因子：基于高价差分的Z-score与成交量-收盘价协方差最小值的反正切之和
    
    参数:
        w: 基础时间窗口，用于动态窗口系统，默认值8
        uni_col: 本因子不依赖单一基础列，因此设置为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'base_window': 8,        # 基础窗口
        'cov_window': 16,        # 协方差窗口 (2*w)
        'zscore_window': 16,     # zscore窗口 (2*w)
        'min_window': 6          # 最小值窗口 (0.75*w)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    base_window = window_sizes['base_window']
    cov_window = window_sizes['cov_window']
    zscore_window = window_sizes['zscore_window']
    min_window = window_sizes['min_window']

    # 1. 计算delta(high, base_window)
    delta_high = pl.col("High") - pl.col("High").shift(base_window)

    # 2. 计算ts_zscore(delta_high, zscore_window)
    delta_high_mean = Ops.rolling_mean(delta_high, zscore_window)
    delta_high_std = Ops.rolling_std(delta_high, zscore_window)
    zscore_delta_high = (delta_high - delta_high_mean) / (delta_high_std + eps)

    # 3. 计算ts_cov(volume, close, cov_window)
    ts_cov = Ops.rolling_cov("Volume", "Close", cov_window)

    # 4. 计算ts_min(ts_cov, min_window)
    ts_min = Ops.rolling_min(ts_cov, min_window)

    # 5. 计算arctan(ts_min)
    arctan_ts_min = ts_min.arctan()

    # 6. 合并两个无量纲项
    factor_result = zscore_delta_high + arctan_ts_min

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_211")
