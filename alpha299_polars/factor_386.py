# Alpha299因子 - factor_386 (Polars版本)
# 原始因子编号: 386
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_386(w: int | None = 5, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha65因子：基于volume和close的Z-score和与close百分比变化平方根的滚动相关系数
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'window_zscore': 20,      # ts_zscore窗口
        'window_corr': 7,         # ts_corr窗口
        'window_pctchg': 5        # ts_pctchg窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    window_zscore = window_sizes['window_zscore']
    window_corr = window_sizes['window_corr']
    window_pctchg = window_sizes['window_pctchg']

    # 使用别名避免重复字段问题
    # 1. 计算成交量的滚动标准化
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    volume_mean = Ops.rolling_mean(volume_protected, window_zscore).alias("_volume_mean")
    volume_std = Ops.rolling_std(volume_protected, window_zscore).alias("_volume_std")
    volume_zscore = ((volume_protected - volume_mean) / (volume_std + eps)).alias("_volume_zscore")

    # 2. 计算收盘价的滚动标准化
    close_mean = Ops.rolling_mean(uni_col, window_zscore).alias("_close_mean")
    close_std = Ops.rolling_std(uni_col, window_zscore).alias("_close_std")
    close_zscore = ((pl.col(uni_col) - close_mean) / (close_std + eps)).alias("_close_zscore")

    # 3. 计算标准化后的成交量与标准化后的收盘价的和
    x1 = (volume_zscore + close_zscore).alias("_x1")

    # 4. 计算收盘价的百分比变化率
    close_shifted = pl.col(uni_col).shift(window_pctchg).alias("_close_shifted")
    pct_change = ((pl.col(uni_col) - close_shifted) / (close_shifted + eps)).alias("_pct_change")

    # 5. 计算pct_change的绝对值的平方根
    x2 = (pct_change.abs() + eps).sqrt().alias("_x2")

    # 6. 计算x1和x2的滚动相关系数
    factor_result = Ops.rolling_corr(x1, x2, window_corr)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_386")
