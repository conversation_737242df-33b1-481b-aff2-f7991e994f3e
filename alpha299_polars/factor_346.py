# Alpha299因子 - factor_346 (Polars版本)
# 原始因子编号: 346
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_346(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha15因子：基于vwap差值标准化、amount排名、协方差等的复合计算
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 15,  # delta(vwap, 15)
        'n2': 15,  # ts_zscore(Y1, 15)
        'n3': 9,   # ts_max(X3, 9)
        'n4': 9,   # ts_cov(9, vwap, amount) 和 ts_zscore(Y5, 9)
        'n5': 14   # ts_std(X6, 14)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']

    # 使用别名避免重复字段问题
    # 1. 计算vwap在过去n1个周期内的差值: Y1 = delta(vwap, n1)
    Y1 = (pl.col("Vwap") - pl.col("Vwap").shift(n1)).alias("_Y1")

    # 2. 对Y1进行过去n2个周期的滚动标准化: X1 = ts_zscore(Y1, n2)
    Y1_mean = Ops.rolling_mean(Y1, n2).alias("_Y1_mean")
    Y1_std = Ops.rolling_std(Y1, n2).alias("_Y1_std")
    X1 = ((Y1 - Y1_mean) / (Y1_std + eps)).alias("_X1")

    # 3. 计算成交额(amount)的截面排名: X2 = rank(amount)（简化实现：使用时序排名）
    X2 = Ops.rolling_rank("Amount", 20).alias("_X2")

    # 4. 取X1和X2中逐元素的较大值: X3 = gp_max(X1, X2)
    X3 = pl.max_horizontal([X1, X2]).alias("_X3")

    # 5. 计算X3在过去n3个周期内的滚动最大值: X4 = ts_max(X3, n3)
    X4 = Ops.rolling_max(X3, n3).alias("_X4")

    # 6. 计算vwap和amount在过去n4个周期内的滚动协方差: Y5 = ts_cov(n4, vwap, amount)
    Y5 = Ops.rolling_cov("Vwap", "Amount", n4).alias("_Y5")

    # 7. 对Y5进行过去n4个周期的滚动标准化: X5 = ts_zscore(Y5, n4)
    Y5_mean = Ops.rolling_mean(Y5, n4).alias("_Y5_mean")
    Y5_std = Ops.rolling_std(Y5, n4).alias("_Y5_std")
    X5 = ((Y5 - Y5_mean) / (Y5_std + eps)).alias("_X5")

    # 8. 取X4和X5中逐元素的较大值: X6 = gp_max(X4, X5)
    X6 = pl.max_horizontal([X4, X5]).alias("_X6")

    # 9. 计算X6在过去n5个周期内的滚动标准差得到Alpha15: Alpha15 = ts_std(X6, n5)
    factor_result = Ops.rolling_std(X6, n5)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_346")
