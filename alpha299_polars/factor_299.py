# Alpha299因子 - factor_299 (Polars版本)
# 原始因子编号: 299
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_299(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha57因子：基于low和volume-low最大值的Z-score和与log(vwap)百分比变化的相关系数
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 14,     # ts_zscore窗口
        'pctchg_window': 5       # ts_pctchg窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    zscore_window = window_sizes['zscore_window']
    pctchg_window = window_sizes['pctchg_window']

    # 1. 计算gp_max(volume, low)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    gp_max = pl.max_horizontal([volume_protected, pl.col("Low")])

    # 2. 计算ts_zscore(low, zscore_window)
    low_mean = Ops.rolling_mean("Low", zscore_window)
    low_std = Ops.rolling_std("Low", zscore_window)
    ts_zscore_low = (pl.col("Low") - low_mean) / (low_std + eps)

    # 3. 计算ts_zscore(gp_max, zscore_window)
    gp_max_mean = Ops.rolling_mean(gp_max, zscore_window)
    gp_max_std = Ops.rolling_std(gp_max, zscore_window)
    ts_zscore_gp_max = (gp_max - gp_max_mean) / (gp_max_std + eps)

    # 4. 计算X1 = ts_zscore_low + ts_zscore_gp_max
    X1 = ts_zscore_low + ts_zscore_gp_max

    # 5. 计算log(vwap)
    log_vwap = (pl.col("Vwap").abs() + eps).log()

    # 6. 计算ts_pctchg(log_vwap, pctchg_window)
    log_vwap_shifted = log_vwap.shift(pctchg_window)
    log_vwap_pctchg = (log_vwap - log_vwap_shifted) / (log_vwap_shifted + eps)

    # 7. 计算ts_corr(zscore_window, X1, log_vwap_pctchg)
    factor_result = Ops.rolling_corr(X1, log_vwap_pctchg, zscore_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_299")
