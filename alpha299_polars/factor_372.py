# Alpha299因子 - factor_372 (Polars版本)
# 原始因子编号: 372
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_372(w: int | None = 4, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha23因子：基于close二阶差分Z-score加上amount截面排名滚动最大值（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为4天。
        uni_col: 单一基础列参数，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta1_window': 15,      # 第一个差分窗口
        'delta2_window': 4,       # 第二个差分窗口
        'zscore_window': 20,      # Z-score窗口
        'tsmax_window': 13,       # 滚动最大值窗口
        'rank_window': 20         # 时序排名窗口（简化实现）
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delta1_window = window_sizes['delta1_window']
    delta2_window = window_sizes['delta2_window']
    zscore_window = window_sizes['zscore_window']
    tsmax_window = window_sizes['tsmax_window']
    rank_window = window_sizes['rank_window']

    # 使用别名避免重复字段问题
    # 1. 计算收盘价的第一阶差分
    T1 = (pl.col(uni_col) - pl.col(uni_col).shift(delta1_window)).alias("_T1")

    # 2. 计算T1的第二阶差分
    X1 = (T1 - T1.shift(delta2_window)).alias("_X1")

    # 3. 对X1进行滚动Z-score标准化
    X1_mean = Ops.rolling_mean(X1, zscore_window).alias("_X1_mean")
    X1_std = Ops.rolling_std(X1, zscore_window).alias("_X1_std")
    X1_zscore = ((X1 - X1_mean) / (X1_std + eps)).alias("_X1_zscore")

    # 4. 简化实现：使用时序排名代替横截面排名
    T2 = Ops.rolling_rank("Amount", rank_window).alias("_T2")

    # 5. 计算T2的滚动最大值
    X2 = Ops.rolling_max(T2, tsmax_window).alias("_X2")

    # 6. 计算X1_zscore与X2的和
    factor_result = X1_zscore + X2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_372")
