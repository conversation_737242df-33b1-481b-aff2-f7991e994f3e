# Alpha299因子 - factor_394 (Polars版本)
# 原始因子编号: 394
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_394(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha79因子：基于复杂的vwap、amount计算的最小值
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 5,   # 滚动最小值窗口
        'n2': 11,  # 差值窗口
        'n3': 8    # 滚动均值窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']

    # 使用别名避免重复字段问题
    # 1. 计算vwap的相反数
    T1 = (-pl.col("Vwap")).alias("_T1")

    # 2. 计算T1的滚动最小值
    T2 = Ops.rolling_min(T1, n1).alias("_T2")

    # 3. 计算成交额的绝对值
    T3 = pl.col("Amount").abs().alias("_T3")

    # 4. 计算vwap的滚动平均值
    T4 = Ops.rolling_mean("Vwap", n3).alias("_T4")

    # 5. 计算amount的滚动平均值
    T5 = Ops.rolling_mean("Amount", n3).alias("_T5")

    # 6. 计算T4除以T5
    T6 = (T4 / (T5 + eps)).alias("_T6")

    # 7. 计算T3乘以T6
    T7 = (T3 * T6).alias("_T7")

    # 8. 计算T2与T7的差值
    T8 = (T2 - T7).alias("_T8")

    # 9. 计算vwap除以T8
    X1 = (pl.col("Vwap") / (T8 + eps)).alias("_X1")

    # 10. 计算vwap的差值
    X2 = (pl.col("Vwap") - pl.col("Vwap").shift(n2)).alias("_X2")

    # 11. 计算X2的滚动平均值
    X3 = Ops.rolling_mean(X2, n3).alias("_X3")

    # 12. 计算X2除以X3
    X4 = (X2 / (X3 + eps)).alias("_X4")

    # 13. 取X1和X4中逐元素的较小值
    factor_result = pl.min_horizontal([X1, X4])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_394")
