# Alpha299因子 - factor_333 (Polars版本)
# 原始因子编号: 333
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_333(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha118因子：基于high百分比变化率与volume最小值Z-score的最大值乘以low
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 9,   # ts_pctchg窗口
        'n2': 12,  # ts_min窗口
        'n3': 20   # ts_zscore窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']

    # 使用别名避免重复字段问题
    # 1. 计算high的百分比变化率
    high_shifted = pl.col("High").shift(n1).alias("_high_shifted")
    high_pct_change = ((pl.col("High") - high_shifted) / (high_shifted + eps)).alias("_high_pct_change")

    # 2. 计算volume的滚动最小值
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    volume_min = Ops.rolling_min(volume_protected, n2).alias("_volume_min")

    # 3. 计算volume_min的Z-score
    volume_min_mean = Ops.rolling_mean(volume_min, n3).alias("_volume_min_mean")
    volume_min_std = Ops.rolling_std(volume_min, n3).alias("_volume_min_std")
    volume_min_zscore = ((volume_min - volume_min_mean) / (volume_min_std + eps)).alias("_volume_min_zscore")

    # 4. 取high_pct_change和volume_min_zscore的最大值
    max_value = pl.max_horizontal([high_pct_change, volume_min_zscore]).alias("_max_value")

    # 5. 计算low与max_value的乘积
    factor_result = pl.col("Low") * max_value

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_333")
