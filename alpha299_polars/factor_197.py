# Alpha299因子 - factor_197 (Polars版本)
# 原始因子编号: 197
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_197(w: int | None = 10, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha103因子：基于VWAP-成交量相关性和VWAP-成交金额最小值与低价标准差协方差的最小值因子
    
    参数:
        w: 基础窗口参数，默认10
        uni_col: 用于指定单一基础数据列（本因子不使用）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 16,   # w，ts_corr窗口
        'n2': 10,   # int(10*w/16) = int(10*16/16) = 10，ts_std窗口
        'n3': 18    # int(18*w/16) = int(18*16/16) = 18，ts_cov窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']

    # 1. 计算VWAP
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. 计算X1: ts_corr(vwap, volume, n1)
    X1 = Ops.rolling_corr(vwap, "Volume", n1)

    # 3. 计算T1: min(vwap, amount)
    T1 = pl.min_horizontal([vwap, pl.col("Amount")])

    # 4. 计算T2: ts_std(low, n2)
    T2 = Ops.rolling_std("Low", n2)

    # 5. 计算X2: ts_cov(T1, T2, n3)
    X2 = Ops.rolling_cov(T1, T2, n3)

    # 6. 计算Alpha103: min(X1, X2)
    factor_result = pl.min_horizontal([X1, X2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_197")
