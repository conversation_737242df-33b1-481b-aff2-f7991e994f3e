# Alpha299因子 - factor_488 (Polars版本)
# 原始因子编号: 488
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def mfi_numba(high, low, close, volume, window):
    """
    使用Numba优化的资金流量指标(MFI)计算
    """
    n = len(high)
    if n < window:
        return np.full(n, np.nan)
    
    mfi = np.full(n, np.nan)
    
    # 计算典型价格
    typical_price = (high + low + close) / 3.0
    
    # 计算资金流量
    money_flow = typical_price * volume
    
    for i in range(1, n):
        # 计算典型价格变化
        tp_change = typical_price[i] - typical_price[i-1]
        
        # 确定正负资金流
        if tp_change > 0:
            positive_mf = money_flow[i]
            negative_mf = 0.0
        elif tp_change < 0:
            positive_mf = 0.0
            negative_mf = money_flow[i]
        else:
            positive_mf = 0.0
            negative_mf = 0.0
        
        # 计算窗口内的正负资金流总和
        if i >= window - 1:
            total_positive_mf = 0.0
            total_negative_mf = 0.0
            
            for j in range(i - window + 1, i + 1):
                if j > 0:  # 确保不越界
                    tp_change_j = typical_price[j] - typical_price[j-1]
                    if tp_change_j > 0:
                        total_positive_mf += money_flow[j]
                    elif tp_change_j < 0:
                        total_negative_mf += money_flow[j]
            
            # 计算MFI
            total_mf = total_positive_mf + total_negative_mf
            
            if abs(total_mf) < 1e-8:
                mfi[i] = 0.0
            elif abs(total_negative_mf) < 1e-8 and total_positive_mf > 1e-8:
                mfi[i] = 100.0
            else:
                mfi[i] = 100.0 * total_positive_mf / (total_mf + 1e-8)
    
    return mfi

def factor_488(w: int | None = 14, uni_col: str | None = None) -> pl.Expr:
    """
    计算资金流量指标 (Money Flow Index, MFI)
    
    参数:
        w: 计算MFI的时间周期长度，默认为14天
        uni_col: 单一列参数，MFI计算需要多列数据，因此设为None
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'mfi_window': 14
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    mfi_window = window_sizes['mfi_window']
    
    def apply_mfi(high_values, low_values, close_values, volume_values):
        return mfi_numba(high_values, low_values, close_values, volume_values, mfi_window)
    
    return pl.map_batches(
        exprs=[pl.col("High"), pl.col("Low"), pl.col("Close"), pl.col("Volume")],
        function=lambda x: pl.Series(apply_mfi(x[0].to_numpy(), x[1].to_numpy(), x[2].to_numpy(), x[3].to_numpy()))
    ).over("symbol").alias("factor_488")
