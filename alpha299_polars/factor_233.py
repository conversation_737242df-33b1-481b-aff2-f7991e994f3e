# Alpha299因子 - factor_233 (Polars版本)
# 原始因子编号: 233
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_233(w: int | None = 10, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha155因子：基于VWAP负值与amount均值比值和close-vwap最大值与volume协方差排名的比值（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
        uni_col: 本因子不依赖单一基础列，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'mean_window': 16,  # amount的滚动均值窗口
        'cov_window': 10    # 协方差计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    mean_window = window_sizes['mean_window']
    cov_window = window_sizes['cov_window']

    # 1. 计算T1 = neg(vwap)
    T1 = -pl.col("Vwap")

    # 2. 计算amount的滚动均值
    T2 = Ops.rolling_mean("Amount", mean_window)

    # 3. 计算X1 = T1 / T2
    X1 = T1 / (T2 + eps)

    # 4. 计算close和vwap的逐元素最大值
    T3 = pl.max_horizontal([pl.col("Close"), pl.col("Vwap")])

    # 5. 计算T3和volume的滚动协方差
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    T4 = Ops.rolling_cov(T3, volume_protected, cov_window)

    # 6. 简化实现：使用时序排名代替横截面排名
    X2 = Ops.rolling_rank(T4, cov_window)

    # 7. 最终因子计算
    factor_result = X1 / (X2 + eps)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_233")
