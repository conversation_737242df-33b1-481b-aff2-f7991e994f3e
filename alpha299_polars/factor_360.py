# Alpha299因子 - factor_360 (Polars版本)
# 原始因子编号: 360
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_360(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha1因子：基于volume*vwap标准差最大值与amount均值的协方差的滚动最大值
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'std_window': 10,         # 标准差窗口
        'max_window_1': 5,        # 第一个最大值窗口
        'mean_window': 12,        # 均值窗口
        'cov_window': 8,          # 协方差窗口
        'max_window_2': 11        # 第二个最大值窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    std_window = window_sizes['std_window']
    max_window_1 = window_sizes['max_window_1']
    mean_window = window_sizes['mean_window']
    cov_window = window_sizes['cov_window']
    max_window_2 = window_sizes['max_window_2']

    # 使用别名避免重复字段问题
    # 1. 计算volume与vwap的乘积
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    X1 = (volume_protected * pl.col("Vwap")).alias("_X1")

    # 2. 计算X1的滚动标准差
    X2 = Ops.rolling_std(X1, std_window).alias("_X2")

    # 3. 计算X2的滚动最大值
    X3 = Ops.rolling_max(X2, max_window_1).alias("_X3")

    # 4. 计算amount的滚动均值
    X4 = Ops.rolling_mean("Amount", mean_window).alias("_X4")

    # 5. 计算X3和X4的滚动协方差
    X5 = Ops.rolling_cov(X3, X4, cov_window).alias("_X5")

    # 6. 计算X5的滚动最大值得到Alpha1
    factor_result = Ops.rolling_max(X5, max_window_2)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_360")
