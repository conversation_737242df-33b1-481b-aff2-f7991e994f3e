# Alpha299因子 - factor_492 (Polars版本)
# 原始因子编号: 492
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def adx_numba(high, low, close, window):
    """
    使用Numba优化的平均趋向指数(ADX)计算
    """
    n = len(high)
    if n < 2 * window:
        return np.full(n, np.nan)
    
    adx = np.full(n, np.nan)
    
    # 计算真实波幅(TR)和趋向变动(+DM, -DM)
    tr = np.zeros(n)
    plus_dm = np.zeros(n)
    minus_dm = np.zeros(n)
    
    for i in range(1, n):
        # 计算真实波幅
        hl = high[i] - low[i]
        hc = abs(high[i] - close[i-1])
        lc = abs(low[i] - close[i-1])
        tr[i] = max(hl, hc, lc)
        
        # 计算上升和下降移动
        up_move = high[i] - high[i-1]
        down_move = low[i-1] - low[i]
        
        # 计算+DM和-DM
        if up_move > down_move and up_move > 0:
            plus_dm[i] = up_move
        else:
            plus_dm[i] = 0.0
            
        if down_move > up_move and down_move > 0:
            minus_dm[i] = down_move
        else:
            minus_dm[i] = 0.0
    
    # 计算平滑的TR, +DM, -DM
    tr_n = np.zeros(n)
    plus_dm_n = np.zeros(n)
    minus_dm_n = np.zeros(n)
    
    # 初始化：前window个值的总和
    if n >= window:
        tr_n[window-1] = np.sum(tr[:window])
        plus_dm_n[window-1] = np.sum(plus_dm[:window])
        minus_dm_n[window-1] = np.sum(minus_dm[:window])
        
        # 使用Wilder平滑法计算后续值
        for i in range(window, n):
            tr_n[i] = tr_n[i-1] - (tr_n[i-1] / window) + tr[i]
            plus_dm_n[i] = plus_dm_n[i-1] - (plus_dm_n[i-1] / window) + plus_dm[i]
            minus_dm_n[i] = minus_dm_n[i-1] - (minus_dm_n[i-1] / window) + minus_dm[i]
    
    # 计算趋向指标(+DI, -DI)
    plus_di = np.zeros(n)
    minus_di = np.zeros(n)
    dx = np.zeros(n)
    
    for i in range(window-1, n):
        if tr_n[i] > 1e-8:
            plus_di[i] = 100.0 * plus_dm_n[i] / tr_n[i]
            minus_di[i] = 100.0 * minus_dm_n[i] / tr_n[i]
        else:
            plus_di[i] = 0.0
            minus_di[i] = 0.0
        
        # 计算趋向指数(DX)
        di_sum = plus_di[i] + minus_di[i]
        di_diff = abs(plus_di[i] - minus_di[i])
        
        if di_sum > 1e-8:
            dx[i] = 100.0 * di_diff / di_sum
        else:
            dx[i] = 0.0
    
    # 计算ADX
    if n >= 2 * window - 1:
        # 初始ADX值是前window个DX的平均值
        dx_sum = 0.0
        dx_count = 0
        for i in range(window-1, 2*window-1):
            if not np.isnan(dx[i]) and not np.isinf(dx[i]):
                dx_sum += dx[i]
                dx_count += 1
        
        if dx_count > 0:
            adx[2*window-2] = dx_sum / dx_count
        else:
            adx[2*window-2] = 0.0
        
        # 使用Wilder平滑法计算后续ADX值
        for i in range(2*window-1, n):
            prev_adx = adx[i-1]
            current_dx = dx[i]
            
            if np.isnan(prev_adx) or np.isinf(prev_adx):
                prev_adx = 0.0
            if np.isnan(current_dx) or np.isinf(current_dx):
                current_dx = 0.0
            
            adx[i] = (prev_adx * (window - 1) + current_dx) / window
    
    return adx

def factor_492(w: int | None = 14, uni_col: str | None = None) -> pl.Expr:
    """
    计算平均趋向指数 (Average Directional Movement Index, ADX)
    
    参数:
        w: 计算ADX所选定的时间周期长度（窗口期），默认为14
        uni_col: 因为ADX计算需要高开低收数据，所以设为None
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'adx_window': 14
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    adx_window = window_sizes['adx_window']
    
    def apply_adx(high_values, low_values, close_values):
        return adx_numba(high_values, low_values, close_values, adx_window)
    
    return pl.map_batches(
        exprs=[pl.col("High"), pl.col("Low"), pl.col("Close")],
        function=lambda x: pl.Series(apply_adx(x[0].to_numpy(), x[1].to_numpy(), x[2].to_numpy()))
    ).over("symbol").alias("factor_492")
