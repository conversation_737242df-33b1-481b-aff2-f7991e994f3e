# Alpha299因子 - factor_220 (Polars版本)
# 原始因子编号: 220
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_220(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha138因子：基于open差分Z-score和low-volume相关性的复合因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n_1': 9,    # delta和zscore窗口
        'n_2': 12    # ts_corr窗口 (4/3 * 9)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n_1 = window_sizes['n_1']  # delta的窗口
    n_2 = window_sizes['n_2']  # ts_corr的窗口

    # 1. 计算delta(open, n_1)
    delta_open = pl.col("Open") - pl.col("Open").shift(n_1)

    # 2. 计算ts_zscore(delta_open, n_1)
    delta_mean = Ops.rolling_mean(delta_open, n_1)
    delta_std = Ops.rolling_std(delta_open, n_1)
    ts_zscore_delta = (delta_open - delta_mean) / (delta_std + eps)

    # 3. 计算log(volume)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    log_volume = (volume_protected + eps).log()

    # 4. 计算ts_corr(low, volume, n_2)
    ts_corr = Ops.rolling_corr("Low", "Volume", n_2)

    # 5. 取log_volume和ts_corr的较大值
    gp_max = pl.max_horizontal([log_volume, ts_corr])

    # 6. 取负数
    neg_gp_max = -gp_max

    # 7. 最终因子计算
    factor_result = ts_zscore_delta - neg_gp_max

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_220")
