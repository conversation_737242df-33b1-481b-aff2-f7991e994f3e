# Alpha299因子 - factor_373 (Polars版本)
# 原始因子编号: 373
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_373(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha24因子：基于volume反正切截面排名差分加上log(volume)标准差（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 6,        # delta窗口
        'std_window': 9,          # ts_std窗口
        'rank_window': 20         # 时序排名窗口（简化实现）
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delta_window = window_sizes['delta_window']
    std_window = window_sizes['std_window']
    rank_window = window_sizes['rank_window']

    # 使用别名避免重复字段问题
    # 1. 计算成交量的反正切值
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    arctan_volume = (volume_protected + eps).arctan().alias("_arctan_volume")

    # 2. 简化实现：使用时序排名代替横截面排名
    rank_arctan = Ops.rolling_rank(arctan_volume, rank_window).alias("_rank_arctan")

    # 3. 计算过去delta_window个周期的差值
    delta_rank = (rank_arctan - rank_arctan.shift(delta_window)).alias("_delta_rank")

    # 4. 计算成交量的自然对数
    log_volume = (volume_protected.abs() + eps).log().alias("_log_volume")

    # 5. 计算过去std_window个周期的滚动标准差
    std_log_volume = Ops.rolling_std(log_volume, std_window).alias("_std_log_volume")

    # 6. 计算两者之和
    factor_result = delta_rank + std_log_volume

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_373")
