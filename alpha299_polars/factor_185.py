# Alpha299因子 - factor_185 (Polars版本)
# 原始因子编号: 185
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_185(w: int | None = 9, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha89因子，基于MACD指标变体，应用tanh变换预防局部基准违规
    
    参数:
        w: 核心可调窗口参数，默认9（对应EMA短期跨度）
        uni_col: 单一基础数据列，默认'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'short_span': 12,   # w，短期EMA跨度
        'long_span': 26,    # w*2+2 = 12*2+2 = 26，长期EMA跨度
        'signal_span': 9    # 9*w/12 = 9*12/12 = 9，信号线EMA跨度
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    short_span = window_sizes['short_span']
    long_span = min(window_sizes['long_span'], 30)  # 限制最大窗口为30
    signal_span = window_sizes['signal_span']

    # 1. 计算EMA
    alpha_short = 2.0 / (short_span + 1)
    alpha_long = 2.0 / (long_span + 1)
    alpha_signal = 2.0 / (signal_span + 1)
    
    ema_short = pl.col(uni_col).ewm_mean(alpha=alpha_short, adjust=False)
    ema_long = pl.col(uni_col).ewm_mean(alpha=alpha_long, adjust=False)

    # 2. 计算MACD线
    macd_line = ema_short - ema_long

    # 3. 计算信号线
    signal_line = macd_line.ewm_mean(alpha=alpha_signal, adjust=False)

    # 4. 计算原始因子值并应用tanh变换
    alpha_89_raw = 2 * (macd_line - signal_line)
    factor_result = alpha_89_raw.tanh()

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_185")
