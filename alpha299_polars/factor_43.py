# Alpha299因子 - factor_43 (Polars版本)
# 原始因子编号: 43
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_43(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha94因子：基于VWAP差值排名与相关性排名的幂运算因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值（简化版本）
    window_configs = {
        'ts_min_window': 16,            # ts_min窗口
        'ts_rank_vwap_window': 20,      # VWAP排名窗口
        'ts_rank_adv_window': 4,        # ADV排名窗口
        'correlation_window': 18,       # 相关性窗口
        'ts_rank_correlation_window': 3, # 相关性排名窗口
        'adv_window': 60                # ADV计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    ts_min_window = window_sizes['ts_min_window']
    ts_rank_vwap_window = window_sizes['ts_rank_vwap_window']
    ts_rank_adv_window = window_sizes['ts_rank_adv_window']
    correlation_window = window_sizes['correlation_window']
    ts_rank_correlation_window = window_sizes['ts_rank_correlation_window']
    adv_window = window_sizes['adv_window']

    # 计算VWAP（成交量加权平均价）
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 计算ADV60（60日平均成交量）
    adv60 = Ops.rolling_mean("Volume", adv_window)

    # 计算VWAP的ts_min
    ts_min_vwap = Ops.rolling_min(vwap, ts_min_window)

    # 计算VWAP - ts_min(VWAP)
    vwap_diff = vwap - ts_min_vwap

    # 对vwap_diff进行滚动窗口排名（归一化到0-1）
    rank_vwap_diff = Ops.rolling_rank(vwap_diff, ts_rank_vwap_window)

    # 计算VWAP的ts_rank（归一化到0-1）
    ts_rank_vwap = Ops.rolling_rank(vwap, ts_rank_vwap_window)

    # 计算ADV60的ts_rank（归一化到0-1）
    ts_rank_adv = Ops.rolling_rank(adv60, ts_rank_adv_window)

    # 计算VWAP_ts_rank和ADV_ts_rank的相关性
    correlation = Ops.rolling_corr(ts_rank_vwap, ts_rank_adv, correlation_window)

    # 对相关性结果进行ts_rank（归一化到0-1）
    ts_rank_correlation = Ops.rolling_rank(correlation, ts_rank_correlation_window)

    # 计算最终因子值：-(rank_vwap_diff ** ts_rank_correlation)
    # 为了避免数值问题，限制指数范围
    safe_exponent = ts_rank_correlation.clip(0.01, 5.0)
    factor_result = -(rank_vwap_diff.clip(0.001, None) ** safe_exponent)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_43")
