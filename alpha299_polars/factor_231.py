# Alpha299因子 - factor_231 (Polars版本)
# 原始因子编号: 231
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_231(w: int | None = 8, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha153因子：基于成交量标准化、开盘价Z-score和高价差分的复合因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
        uni_col: 单一基础数据列（本因子不适用，保留为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'mean_window': 20,    # volume ts_mean窗口
        'zscore_window': 20,  # open ts_zscore窗口
        'delta_window': 8     # high delta窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    mean_window = window_sizes['mean_window']
    zscore_window = window_sizes['zscore_window']
    delta_window = window_sizes['delta_window']

    # 1. 计算vwap（成交量加权平均价）= amount / volume
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. 计算volume的滚动均值
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_mean = Ops.rolling_mean(volume_protected, mean_window)

    # 3. 计算V_norm = volume / volume_mean
    V_norm = volume_protected / (volume_mean + eps)

    # 4. 计算open的ts_zscore
    open_mean = Ops.rolling_mean("Open", zscore_window)
    open_std = Ops.rolling_std("Open", zscore_window)
    O_zscore = (pl.col("Open") - open_mean) / (open_std + eps)

    # 5. 计算T1 = O_zscore - V_norm
    T1 = O_zscore - V_norm

    # 6. 计算T2 = max(V_norm, vwap)
    T2 = pl.max_horizontal([V_norm, vwap])

    # 7. 计算X1 = max(T1, T2)
    X1 = pl.max_horizontal([T1, T2])

    # 8. 计算delta(high, delta_window)
    X2 = pl.col("High") - pl.col("High").shift(delta_window)

    # 9. 计算Alpha153 = X1 * X2
    factor_result = X1 * X2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_231")
