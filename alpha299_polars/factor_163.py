# Alpha299因子 - factor_163 (Polars版本)
# 原始因子编号: 163
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_163(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha5因子：高点与成交量时序排名的相关性因子
    
    参数:
        w: 核心可调参数，默认为3
        uni_col: 单一基础数据列参数（本因子使用多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'ts_rank_window': 5,  # w
        'corr_window': 5,     # w
        'max_window': 3       # 固定窗口，不基于w推导
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    ts_rank_window = window_sizes['ts_rank_window']
    corr_window = window_sizes['corr_window']
    max_window = window_sizes['max_window']

    # 1. 计算高点和成交量的时序排名
    ts_rank_high = Ops.rolling_rank("High", ts_rank_window)
    ts_rank_volume = Ops.rolling_rank("Volume", ts_rank_window)

    # 2. 计算滚动相关系数
    corr = Ops.rolling_corr(ts_rank_high, ts_rank_volume, corr_window)

    # 3. 计算滚动最大值
    max_corr = Ops.rolling_max(corr, max_window)

    # 4. 计算最终因子值
    factor_result = -max_corr

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_163")
