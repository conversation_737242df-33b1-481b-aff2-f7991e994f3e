# Alpha299因子 - factor_124 (Polars版本)
# 原始因子编号: 124
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_124(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于ADX指标的技术分析因子（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础数据列（此处不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'dm_tr_window': 14,  # DM和TR的滚动求和窗口
        'dx_ma_window': 6    # DX的移动平均窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    dm_tr_window = window_sizes['dm_tr_window']
    dx_ma_window = window_sizes['dx_ma_window']

    # 简化ADX计算
    # Step 1: Calculate LD and HD
    LD = pl.col("Low").shift(1) - pl.col("Low")
    HD = pl.col("High") - pl.col("High").shift(1)

    # Step 2: Calculate True Range (TR)
    prev_close = pl.col("Close").shift(1)
    TR1 = pl.col("High") - pl.col("Low")
    TR2 = (pl.col("High") - prev_close).abs()
    TR3 = (pl.col("Low") - prev_close).abs()
    TR = pl.max_horizontal([TR1, TR2, TR3])

    # Step 3: Calculate +DM and -DM
    plus_DM = pl.when((LD > 0) & (LD > HD)).then(LD).otherwise(0)
    minus_DM = pl.when((HD > 0) & (HD > LD)).then(HD).otherwise(0)

    # Step 4: Rolling sums for DM and TR
    sum_plus_DM = Ops.rolling_sum(plus_DM, dm_tr_window)
    sum_minus_DM = Ops.rolling_sum(minus_DM, dm_tr_window)
    sum_TR = Ops.rolling_sum(TR, dm_tr_window)

    # Step 5: Calculate +DI and -DI
    plus_DI = (sum_plus_DM / (sum_TR + eps)) * 100
    minus_DI = (sum_minus_DM / (sum_TR + eps)) * 100

    # Step 6: Calculate DX
    DI_diff = (plus_DI - minus_DI).abs()
    DI_sum = plus_DI + minus_DI
    DX = (DI_diff / (DI_sum + eps)) * 100

    # Step 7: 移动平均of DX
    factor_result = Ops.rolling_mean(DX, dx_ma_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_124")
