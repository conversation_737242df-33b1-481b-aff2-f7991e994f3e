# Alpha299因子 - factor_152 (Polars版本)
# 原始因子编号: 152
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_152(w: int | None = 12, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha49因子：基于趋向指标UOS的一部分
    
    参数:
        w: 滚动窗口大小，默认为12
        uni_col: 本因子不依赖单一列，故设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'w': 12  # 基础窗口参数
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    actual_w = window_sizes['w']

    # 1. 计算前一日的high和low
    DH = pl.col("High").shift(1)
    DL = pl.col("Low").shift(1)

    # 2. 计算Term_t
    term_t = pl.max_horizontal([
        (pl.col("High") - DH).abs(),
        (pl.col("Low") - DL).abs()
    ])

    # 3. 计算条件
    cond_decrease = (pl.col("High") + pl.col("Low")) < (DH + DL)
    cond_increase = (pl.col("High") + pl.col("Low")) > (DH + DL)

    # 4. 计算P1和P2的组成部分
    P1_part = term_t * cond_decrease.cast(pl.Int32)
    P2_part = term_t * cond_increase.cast(pl.Int32)

    # 5. 计算P1和P2的滚动和
    P1 = Ops.rolling_sum(P1_part, actual_w)
    P2 = Ops.rolling_sum(P2_part, actual_w)

    # 6. 计算最终因子值
    P1_plus_P2 = P1 + P2
    factor_result = P1 / (P1_plus_P2 + eps)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_152")
