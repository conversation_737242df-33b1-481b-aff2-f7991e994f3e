# Alpha299因子 - factor_106 (Polars版本)
# 原始因子编号: 106
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_106(w: int | None = 20, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha 146因子：价格相对强度EMA因子
    
    原始逻辑：
    1. 计算价格相对强度 RelStr_t = Close_t / Close_{t-20}
    2. 计算RelStr_t的39期EMA
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期，默认为20天
        uni_col: 单一基础列参数，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式，别名为"factor_106"
    """
    
    # 定义所有窗口的基准值
    window_configs = {
        'lag_window': 20,       # 价格滞后窗口
        'ema_span': 39          # EMA计算span (2*w - 1)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    lag_window = window_sizes['lag_window']
    ema_span = window_sizes['ema_span']
    
    # 计算价格相对强度RelStr_t = Close_t / Close_{t-lag_window}
    close_lag = pl.col(uni_col).shift(lag_window)
    rel_str = pl.col(uni_col) / (close_lag + eps)
    
    # 处理分母为0的情况
    rel_str = pl.when(close_lag.abs() < eps).then(eps).otherwise(rel_str)
    
    # 计算RelStr_t的EMA
    # 使用ewm_mean，alpha = 2/(span+1)
    alpha_ema = 2.0 / (ema_span + 1)
    ema_result = rel_str.ewm_mean(alpha=alpha_ema, adjust=False)
    
    return ema_result.over("symbol").cast(pl.Float32).alias("factor_106")
