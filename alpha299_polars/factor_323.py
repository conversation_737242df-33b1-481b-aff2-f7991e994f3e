# Alpha299因子 - factor_323 (Polars版本)
# 原始因子编号: 323
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_323(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha101因子：基于amount和low的Z-score tanh变换和与open Z-score回归贝塔和volume-vwap回归贝塔的最小值
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'window_zscore': 20,      # Z-score窗口
        'window_regbeta1': 15,    # 第一个回归贝塔窗口
        'window_regbeta2': 9      # 第二个回归贝塔窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    window_zscore = window_sizes['window_zscore']
    window_regbeta1 = window_sizes['window_regbeta1']
    window_regbeta2 = window_sizes['window_regbeta2']

    # 使用别名避免重复字段问题
    # 1. 计算amount的Z-score
    amount_mean = Ops.rolling_mean("Amount", window_zscore).alias("_amount_mean")
    amount_std = Ops.rolling_std("Amount", window_zscore).alias("_amount_std")
    amount_z = ((pl.col("Amount") - amount_mean) / (amount_std + eps)).alias("_amount_z")

    # 2. 计算low的Z-score
    low_mean = Ops.rolling_mean("Low", window_zscore).alias("_low_mean")
    low_std = Ops.rolling_std("Low", window_zscore).alias("_low_std")
    low_z = ((pl.col("Low") - low_mean) / (low_std + eps)).alias("_low_z")

    # 3. 应用tanh变换
    tanh_amount_z = amount_z.tanh().alias("_tanh_amount_z")
    tanh_low_z = low_z.tanh().alias("_tanh_low_z")

    # 4. 计算T1 = tanh_amount_z + tanh_low_z
    T1 = (tanh_amount_z + tanh_low_z).alias("_T1")

    # 5. 计算open的Z-score
    open_mean = Ops.rolling_mean("Open", window_zscore).alias("_open_mean")
    open_std = Ops.rolling_std("Open", window_zscore).alias("_open_std")
    open_price_z = ((pl.col("Open") - open_mean) / (open_std + eps)).alias("_open_price_z")

    # 6. 计算X1 = ts_regbeta(T1, open_price_z, window_regbeta1)
    X1 = Ops.rolling_regbeta(T1, open_price_z, window_regbeta1).alias("_X1")

    # 7. 计算volume的保护值
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")

    # 8. 计算X2 = ts_regbeta(volume, vwap, window_regbeta2)
    X2 = Ops.rolling_regbeta(volume_protected, "Vwap", window_regbeta2).alias("_X2")

    # 9. 计算gp_min(X1, X2)
    factor_result = pl.min_horizontal([X1, X2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_323")
