# Alpha299因子 - factor_415 (Polars版本)
# 原始因子编号: 415
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def stochrsi_k_numba(close, rsi_window, stoch_window):
    """
    使用Numba优化的StochRSI K值计算
    """
    n = len(close)
    if n < rsi_window + stoch_window:
        return np.full(n, np.nan)
    
    # 计算价格变化
    price_changes = np.zeros(n)
    for i in range(1, n):
        price_changes[i] = close[i] - close[i-1]
    
    # 分离上涨和下跌
    up_moves = np.where(price_changes > 0, price_changes, 0.0)
    down_moves = np.where(price_changes < 0, -price_changes, 0.0)
    
    # 计算平均上涨和平均下跌
    avg_up = np.zeros(n)
    avg_down = np.zeros(n)
    
    # 初始化第rsi_window个位置的平均值
    if rsi_window > 0:
        avg_up[rsi_window-1] = np.mean(up_moves[:rsi_window])
        avg_down[rsi_window-1] = np.mean(down_moves[:rsi_window])
        
        # 使用Wilder's平滑法计算后续的平均上涨和平均下跌
        for i in range(rsi_window, n):
            avg_up[i] = (avg_up[i-1] * (rsi_window-1) + up_moves[i]) / rsi_window
            avg_down[i] = (avg_down[i-1] * (rsi_window-1) + down_moves[i]) / rsi_window
    
    # 计算RSI
    rsi = np.zeros(n)
    for i in range(rsi_window-1, n):
        if avg_down[i] > 1e-8:
            rs = avg_up[i] / avg_down[i]
            rsi[i] = 100 - (100 / (1 + rs))
        else:
            rsi[i] = 100.0
    
    # 计算StochRSI K值
    stoch_rsi_k = np.full(n, np.nan)
    start_index = rsi_window + stoch_window - 1
    
    for i in range(start_index, n):
        start_idx = i - stoch_window + 1
        min_rsi = np.min(rsi[start_idx:i+1])
        max_rsi = np.max(rsi[start_idx:i+1])
        
        if max_rsi - min_rsi > 1e-8:
            stoch_rsi_k[i] = 100 * (rsi[i] - min_rsi) / (max_rsi - min_rsi)
        else:
            stoch_rsi_k[i] = 0.0
    
    return stoch_rsi_k

def factor_415(w: int | None = 5, uni_col: str | None = 'Close') -> pl.Expr:
    """
    计算随机相对强弱指标K值 (Stochastic RSI %K, StochRSI K)
    
    参数:
        w: 计算StochRSI K值的时间窗口参数，默认为5
        uni_col: 用于计算的数据列，默认为'Close'
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'rsi_window': 14,
        'stoch_window': 5
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    rsi_window = window_sizes['rsi_window']
    stoch_window = window_sizes['stoch_window']
    
    def apply_stochrsi_k(close_values):
        return stochrsi_k_numba(close_values, rsi_window, stoch_window)

    return pl.map_batches(
        exprs=[pl.col(uni_col)],
        function=lambda x: pl.Series(apply_stochrsi_k(x[0].to_numpy()))
    ).over("symbol").alias("factor_415")
