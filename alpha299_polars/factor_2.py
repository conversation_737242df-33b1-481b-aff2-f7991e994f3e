# Alpha299因子 - factor_2 (Polars版本)
# 原始因子编号: 2
# 转写时间: 2025-07-17

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_2(w: int | None = 1, uni_col: str | None = None) -> pl.Expr:
    """
    计算Alpha12因子，基于成交量与收盘价的1期差分及符号函数。

    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为1天。
        uni_col: 本因子未使用，强制设为None

    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'diff_period': 1    # 差分周期
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    diff_period = window_sizes['diff_period']

    # 强制uni_col设为None
    if uni_col is not None:
        raise ValueError("本因子不需要uni_col参数，必须设为None")

    # 计算Volume和Close的差分，然后计算因子值
    # 使用重构后的算子库，需要显式使用.over()进行分组
    volume_diff = (pl.col("Volume") - pl.col("Volume").shift(diff_period)).over("symbol")
    close_diff = (pl.col("Close") - pl.col("Close").shift(diff_period)).over("symbol")

    # 计算符号函数与因子值: sign(volume_diff) * (-close_diff)
    return (volume_diff.sign() * (-close_diff)).cast(pl.Float32).alias("factor_2")
