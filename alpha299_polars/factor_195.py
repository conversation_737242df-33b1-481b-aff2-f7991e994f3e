# Alpha299因子 - factor_195 (Polars版本)
# 原始因子编号: 195
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_195(w: int | None = 8, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha101因子：复杂的协方差、回归beta和最小值计算因子
    
    参数:
        w: 核心可调参数（天数），默认8
        uni_col: 未使用（因子涉及多列）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 10,  # w // 2 = 20 // 2 = 10
        'n2': 20,  # w = 20
        'n3': 15,  # 固定值（非w的函数）
        'n4': 8    # 固定值（非w的函数）
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']

    # 1. 计算VWAP
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. T1: open_price * volume
    T1 = pl.col("Open") * pl.col("Volume")

    # 3. T2: arctan(low)
    T2 = (pl.col("Low") + eps).arctan()

    # 4. T3: max(high, volume)
    T3 = pl.max_horizontal([pl.col("High"), pl.col("Volume")])

    # 5. T4: T3 + vwap
    T4 = T3 + vwap

    # 6. T5: ts_cov(T2, T4, n1)
    T5 = Ops.rolling_cov(T2, T4, n1)

    # 7. T6: ts_regbeta(T1, T5, n2)
    T6 = Ops.rolling_regbeta(T1, T5, n2)

    # 8. X1: delay(T6, n3)
    X1 = T6.shift(n3)

    # 9. X2: ts_pctchg(close, n4)
    X2 = (pl.col("Close") - pl.col("Close").shift(n4)) / (pl.col("Close").shift(n4) + eps)

    # 10. Alpha101: min(X1, X2)
    factor_result = pl.min_horizontal([X1, X2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_195")
