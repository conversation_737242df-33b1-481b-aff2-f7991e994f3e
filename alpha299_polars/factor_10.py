# Alpha299因子 - factor_10 (Polars版本)
# 原始因子编号: 10
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_10(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha25因子：基于收益率、成交量、VWAP和价差的复合因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'adv_window': 20,       # 平均成交量窗口
        'zscore_window': 20     # Z-score标准化窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    adv_window = window_sizes['adv_window']
    zscore_window = window_sizes['zscore_window']
    
    # 计算收益率
    returns = (pl.col("Close") / pl.col("Close").shift(1) - 1)
    
    # 计算平均成交量ADV
    adv = Ops.rolling_mean("Volume", adv_window)
    
    # 计算VWAP（成交量加权平均价）
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)
    
    # 计算HIGH - CLOSE
    high_close = pl.col("High") - pl.col("Close")
    
    # 计算核心乘积
    product = -(returns * adv * vwap * high_close)
    
    # 滚动Z-score标准化
    product_mean = Ops.rolling_mean(product, zscore_window)
    product_std = Ops.rolling_std(product, zscore_window)
    zscore = (product - product_mean) / (product_std + eps)
    
    # 截面排名（同一时间点维度）- 使用rank().over(["datetime"])
    return (zscore.rank(method="dense", descending=True).over(["datetime"]).cast(pl.Float32) \
            / pl.count().over(["datetime"]).cast(pl.Float32)).alias("factor_10")