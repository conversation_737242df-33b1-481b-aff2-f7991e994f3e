# Alpha299因子 - factor_271 (Polars版本)
# 原始因子编号: 271
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_271(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha220因子：基于VWAP标准差Z-score、VOLUME最大值Z-score的最大值与HIGH标准差Z-score的和的标准差
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'high_std_window': 5,     # HIGH标准差窗口
        'vwap_std_window': 7,     # VWAP标准差窗口
        'volume_max_window': 10,  # VOLUME最大值窗口
        'final_std_window': 14,   # 最终标准差窗口
        'std_window': 20          # 标准化窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    high_std_window = window_sizes['high_std_window']
    vwap_std_window = window_sizes['vwap_std_window']
    volume_max_window = window_sizes['volume_max_window']
    final_std_window = window_sizes['final_std_window']
    std_window = window_sizes['std_window']

    # 1. 计算VWAP的标准差
    vwap_std = Ops.rolling_std("Vwap", vwap_std_window)

    # 2. 计算VWAP标准差的Z-score
    vwap_std_mean = Ops.rolling_mean(vwap_std, std_window)
    vwap_std_std = Ops.rolling_std(vwap_std, std_window)
    ts_zscore_vwap_std = (vwap_std - vwap_std_mean) / (vwap_std_std + eps)

    # 3. 计算VOLUME的最大值
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_max = Ops.rolling_max(volume_protected, volume_max_window)

    # 4. 计算VOLUME最大值的Z-score
    volume_max_mean = Ops.rolling_mean(volume_max, std_window)
    volume_max_std = Ops.rolling_std(volume_max, std_window)
    ts_zscore_volume_max = (volume_max - volume_max_mean) / (volume_max_std + eps)

    # 5. GP_MAX操作（比较标准化后的VWAP标准差和VOLUME最大值）
    gp_max_result = pl.max_horizontal([ts_zscore_vwap_std, ts_zscore_volume_max])

    # 6. 计算HIGH的标准差
    high_std = Ops.rolling_std("High", high_std_window)

    # 7. 计算HIGH标准差的Z-score
    high_std_mean = Ops.rolling_mean(high_std, std_window)
    high_std_std = Ops.rolling_std(high_std, std_window)
    ts_zscore_high_std = (high_std - high_std_mean) / (high_std_std + eps)

    # 8. 将GP_MAX结果与HIGH标准差Z-score相加
    sum_zscores = gp_max_result + ts_zscore_high_std

    # 9. 计算最终标准差
    factor_result = Ops.rolling_std(sum_zscores, final_std_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_271")
