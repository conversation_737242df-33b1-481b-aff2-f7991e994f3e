# Alpha299因子 - factor_192 (Polars版本)
# 原始因子编号: 192
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_192(w: int | None = 11, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha101因子：基于成交量相关性和价格排名的复合因子（简化版）
    
    参数:
        w: 核心可调参数，单位为天，默认11
        uni_col: 单一基础数据列，此处设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n_ma': 30,                    # int(1.5 * w) = 1.5 * 20 = 30
        'sum_days': 30,                # int(1.85 * w) = 1.85 * 20 = 37，限制为30
        'correlation_window1': 15,     # int(0.75 * w) = 0.75 * 20 = 15  
        'correlation_window2': 11,     # 固定窗口11，与w无关
        'zscore_window': 20            # w = 20，Z-score窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n_ma = window_sizes['n_ma']
    sum_days = window_sizes['sum_days']
    correlation_window1 = window_sizes['correlation_window1']
    correlation_window2 = window_sizes['correlation_window2']
    zscore_window = window_sizes['zscore_window']

    # 1. 计算VWAP
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. 计算成交量移动平均和求和
    ma_vol = Ops.rolling_mean("Volume", n_ma)
    sum_ma_vol = Ops.rolling_sum(ma_vol, sum_days)

    # 3. 计算收盘价与SumMAVol的相关系数
    corr_1 = Ops.rolling_corr("Close", sum_ma_vol, correlation_window1)

    # 4. 简化实现：使用时序排名代替横截面排名
    R_1 = Ops.rolling_rank(corr_1, correlation_window1)

    # 5. 计算混合价格
    P_mix = 0.1 * pl.col("High") + 0.9 * vwap

    # 6. 简化实现：使用时序排名代替横截面排名
    rank_p_mix = Ops.rolling_rank(P_mix, zscore_window)

    # 7. 计算Z-score标准化成交量
    vol_mean = Ops.rolling_mean("Volume", zscore_window)
    vol_std = Ops.rolling_std("Volume", zscore_window)
    zscore_vol = (pl.col("Volume") - vol_mean) / (vol_std + eps)

    # 8. 简化实现：使用时序排名代替横截面排名
    rank_vol = Ops.rolling_rank(zscore_vol, zscore_window)

    # 9. 计算RankP_mix与RankVol的相关系数
    corr_2 = Ops.rolling_corr(rank_p_mix, rank_vol, correlation_window2)

    # 10. 简化实现：使用时序排名代替横截面排名
    R_2 = Ops.rolling_rank(corr_2, correlation_window2)

    # 11. 最终因子值
    factor_result = -((R_1 < R_2).cast(pl.Int32))

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_192")
