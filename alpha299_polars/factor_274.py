# Alpha299因子 - factor_274 (Polars版本)
# 原始因子编号: 274
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_274(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha223因子：基于VWAP差分与amount排名最大值的最大值与VWAP-amount相关系数最小值的标准差（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'ts_max_window': 5,      # TS_MAX窗口
        'ts_corr_window': 5,     # TS_CORR窗口
        'std_window': 12,        # TS_STD窗口
        'delta_window': 26       # DELTA窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    ts_max_window = window_sizes['ts_max_window']
    ts_corr_window = window_sizes['ts_corr_window']
    std_window = window_sizes['std_window']
    delta_window = window_sizes['delta_window']

    # 步骤1: DELTA(VWAP, delta_window)
    delta_vwap = pl.col("Vwap") - pl.col("Vwap").shift(delta_window)

    # 步骤2: 简化实现：使用时序排名代替横截面排名
    rank_amount = Ops.rolling_rank("Amount", ts_corr_window)

    # 步骤3: GP_MAX(DELTA_VWAP, RANK_AMOUNT)
    gp_max = pl.max_horizontal([delta_vwap, rank_amount])

    # 步骤4: TS_MAX(步骤3结果, ts_max_window)
    ts_max = Ops.rolling_max(gp_max, ts_max_window)

    # 步骤5: TS_CORR(VWAP, AMOUNT, ts_corr_window)
    ts_corr = Ops.rolling_corr("Vwap", "Amount", ts_corr_window)

    # 步骤6: GP_MIN(步骤4结果, 步骤5结果)
    gp_min = pl.min_horizontal([ts_max, ts_corr])

    # 步骤7: TS_STD(步骤6结果, std_window)
    factor_result = Ops.rolling_std(gp_min, std_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_274")
