# Alpha299因子 - factor_392 (Polars版本)
# 原始因子编号: 392
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_392(w: int | None = 2, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha75因子：基于amount-vwap协方差与amount-low回归贝塔均值最小值的Z-score截面排名乘以amount差分除以close标准差的最小值（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 11,         # 协方差窗口
        'regbeta_window': 2,      # 回归贝塔窗口
        'mean_window': 3,         # 均值窗口
        'zscore_window': 12,      # Z-score窗口
        'delta_window': 3,        # 差分窗口
        'std_window': 6,          # 标准差窗口
        'min_window': 7,          # 最小值窗口
        'rank_window': 20         # 时序排名窗口（简化实现）
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    cov_window = window_sizes['cov_window']
    regbeta_window = window_sizes['regbeta_window']
    mean_window = window_sizes['mean_window']
    zscore_window = window_sizes['zscore_window']
    delta_window = window_sizes['delta_window']
    std_window = window_sizes['std_window']
    min_window = window_sizes['min_window']
    rank_window = window_sizes['rank_window']

    # 使用别名避免重复字段问题
    # 1. 计算amount与vwap的协方差
    T1 = Ops.rolling_cov("Amount", "Vwap", cov_window).alias("_T1")

    # 2. 计算amount对low的回归贝塔系数
    T2 = Ops.rolling_regbeta("Amount", "Low", regbeta_window).alias("_T2")

    # 3. 计算T2的滚动均值
    T3 = Ops.rolling_mean(T2, mean_window).alias("_T3")

    # 4. 计算T1和T3的最小值
    T4 = pl.min_horizontal([T1, T3]).alias("_T4")

    # 5. 对T4进行滚动Z-score标准化
    T4_mean = Ops.rolling_mean(T4, zscore_window).alias("_T4_mean")
    T4_std = Ops.rolling_std(T4, zscore_window).alias("_T4_std")
    T4_standardized = ((T4 - T4_mean) / (T4_std + eps)).alias("_T4_standardized")

    # 6. 简化实现：使用时序排名代替横截面排名
    X1 = Ops.rolling_rank(T4_standardized, rank_window).alias("_X1")

    # 7. 计算amount的差分
    T5 = (pl.col("Amount") - pl.col("Amount").shift(delta_window)).alias("_T5")

    # 8. 计算close的滚动标准差
    T6 = Ops.rolling_std("Close", std_window).alias("_T6")

    # 9. 计算T5除以T6
    T7 = (T5 / (T6 + eps)).alias("_T7")

    # 10. 计算T7的滚动最小值
    X2 = Ops.rolling_min(T7, min_window).alias("_X2")

    # 11. 计算最终因子值
    factor_result = X1 * X2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_392")
