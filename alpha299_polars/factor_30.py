# Alpha299因子 - factor_30 (Polars版本)
# 原始因子编号: 30
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_30(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    修复后的Alpha53因子：
    1. 计算 (CLOSE-LOW)-(HIGH-CLOSE) / (CLOSE-LOW)
    2. 计算9期差分
    3. 取差分结果的累积排名并取负（避免未来数据泄露）
    
    参数:
        w: 差分周期参数，默认为9天
        uni_col: 未使用单一基础列，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 9        # DELTA差分窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delta_window = window_sizes['delta_window']

    # 计算分子部分 (CLOSE-LOW)-(HIGH-CLOSE)
    numerator = (pl.col("Close") - pl.col("Low")) - (pl.col("High") - pl.col("Close"))
    
    # 计算分母部分 (CLOSE-LOW)
    denominator = pl.col("Close") - pl.col("Low")

    # 计算比值（处理分母为0的情况）
    ratio = numerator / (denominator + eps)

    # 计算delta_window期差分（当前值 - delta_window天前的值）
    diff_ratio = ratio - ratio.shift(delta_window)

    # 使用累积排名替代全局rankdata，避免未来数据泄露
    # 使用row_number()来模拟累积排名
    cumulative_rank = diff_ratio.rank(method="dense", descending=True)

    # 取负，只在最后使用一次.over()
    factor_result = -1 * cumulative_rank

    return factor_result.over("symbol").cast(pl.Float32).alias("factor_30")
