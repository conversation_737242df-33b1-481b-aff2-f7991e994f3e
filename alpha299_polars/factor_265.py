# Alpha299因子 - factor_265 (Polars版本)
# 原始因子编号: 265
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_265(w: int | None = 10, uni_col: str | None = 'close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha208因子：基于VWAP、CLOSE和HIGH的组合逻辑，包含sigmoid变换
    
    参数:
        w: 时间窗口参数(天)，用于TS_PCTCHG计算，默认10
        uni_col: 单一基础数据列，此处使用'close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'pctchg_window': 10     # 百分比变化计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    pctchg_window = window_sizes['pctchg_window']

    # 1. 计算GP_MIN(VWAP, CLOSE) - 这里实际是取最小值
    gp_min = pl.min_horizontal([pl.col("Vwap"), pl.col("Close")])

    # 2. 应用SIGMOID函数
    sigmoid_gp_min = 1.0 / (1.0 + (-gp_min).exp())

    # 3. 计算VWAP/HIGH
    vwap_high_ratio = pl.col("Vwap") / (pl.col("High") + eps)

    # 4. 计算两者的差值
    term1 = sigmoid_gp_min - vwap_high_ratio

    # 5. 计算TS_PCTCHG(CLOSE, pctchg_window)
    close_shifted = pl.col("Close").shift(pctchg_window)
    ts_pctchg = (pl.col("Close") - close_shifted) / (close_shifted + eps)

    # 6. 计算GP_MAX(term1, ts_pctchg)
    factor_result = pl.max_horizontal([term1, ts_pctchg])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_265")
