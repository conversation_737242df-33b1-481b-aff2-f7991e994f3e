# Alpha299因子 - factor_453 (Polars版本)
# 原始因子编号: 453
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def ht_inphase_numba(price, window):
    """
    使用Numba优化的希尔伯特变换瞬时相位计算
    """
    n = len(price)
    if n < window:
        return np.full(n, np.nan)
    
    # 希尔伯特变换的常数参数
    a = 0.0962
    b = 0.5769
    alpha_smooth = 0.2
    
    # 计算弧度到角度的转换系数
    rad2deg = 180.0 / (4.0 * np.arctan(1.0))
    
    # 初始化输出数组和中间变量
    smoothed_price = np.zeros(n)
    detrender = np.zeros(n)
    ht_inphase = np.zeros(n)
    ht_quad = np.zeros(n)
    ji = np.zeros(n)
    jq = np.zeros(n)
    
    # 初始化状态变量
    period = np.ones(n) * 20  # 初始周期设为20
    i2 = np.zeros(n)
    q2 = np.zeros(n)
    re = np.zeros(n)
    im = np.zeros(n)
    
    # 计算4周期加权移动平均
    for i in range(3, n):
        # 价格平滑: 4周期加权移动平均
        smoothed_price[i] = 0.1 * (4 * price[i] + 3 * price[i-1] + 2 * price[i-2] + price[i-3])
    
    # 主循环：计算希尔伯特变换和相关组件
    for i in range(6, n):
        # 动态调整因子
        adjusted_period_factor = 0.075 * period[i-1] + 0.54
        
        # 计算去趋势分量
        detrender[i] = (a * smoothed_price[i] + b * smoothed_price[i-2] -
                       b * smoothed_price[i-4] - a * smoothed_price[i-6]) * adjusted_period_factor
        
        # 计算瞬时相位分量 (HT_INPHASE)
        if i >= 9:  # 确保有足够的历史数据
            ht_inphase[i] = detrender[i-3]
        
        # 计算正交分量 (HT_QUAD)
        if i >= 12:  # 确保有足够的历史数据
            ht_quad[i] = (a * detrender[i] + b * detrender[i-2] -
                         b * detrender[i-4] - a * detrender[i-6]) * adjusted_period_factor
        
        # 计算jI分量
        if i >= 18:  # 确保有足够的历史数据
            ji[i] = (a * ht_inphase[i] + b * ht_inphase[i-2] -
                    b * ht_inphase[i-4] - a * ht_inphase[i-6]) * adjusted_period_factor
        
        # 计算jQ分量
        if i >= 18:  # 确保有足够的历史数据
            jq[i] = (a * ht_quad[i] + b * ht_quad[i-2] -
                    b * ht_quad[i-4] - a * ht_quad[i-6]) * adjusted_period_factor
        
        # 平滑相位分量 I2, Q2
        if i >= 18:  # 确保有足够的历史数据
            i2[i] = alpha_smooth * (ht_inphase[i] - jq[i]) + (1 - alpha_smooth) * i2[i-1]
            q2[i] = alpha_smooth * (ht_quad[i] + ji[i]) + (1 - alpha_smooth) * q2[i-1]
        
        # 计算实部和虚部
        if i >= 19:  # 确保有足够的历史数据
            re[i] = alpha_smooth * (i2[i] * i2[i-1] + q2[i] * q2[i-1]) + (1 - alpha_smooth) * re[i-1]
            im[i] = alpha_smooth * (i2[i] * q2[i-1] - q2[i] * i2[i-1]) + (1 - alpha_smooth) * im[i-1]
        
        # 计算主周期
        if i >= 19:  # 确保有足够的历史数据
            # 避免除以零或接近零的情况
            denominator = (re[i] + 1e-8) * (rad2deg + 1e-8)
            if denominator != 0:
                period_raw = 360.0 / (np.arctan(im[i] / (re[i] + 1e-8)) * (rad2deg + 1e-8))
            else:
                period_raw = period[i-1]  # 保持前一个周期值
            
            # 周期边界限制与调整
            upper_limit = 1.5 * period[i-1]
            lower_limit = 0.67 * period[i-1]
            period_candidate = min(max(period_raw, lower_limit), upper_limit)
            period_candidate = min(max(period_candidate, 6), 50)  # 绝对上下限
            
            # 周期平滑
            period[i] = alpha_smooth * period_candidate + (1 - alpha_smooth) * period[i-1]
    
    # 将前面的值设为NaN
    result = np.full(n, np.nan)
    result[window-1:] = ht_inphase[window-1:]
    
    return result

def factor_453(w: int | None = 60, uni_col: str | None = 'Close') -> pl.Expr:
    """
    计算希尔伯特变换瞬时相位 (Hilbert Transform InPhase Component, HT_INPHASE)
    
    参数:
        w: 初始化窗口期，确保滤波器和平滑计算趋于稳定，默认为60
        uni_col: 用于计算的价格列，默认为'Close'
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'hilbert_window': 60
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    hilbert_window = window_sizes['hilbert_window']
    
    def apply_ht_inphase(price_values):
        return ht_inphase_numba(price_values, hilbert_window)
    
    return pl.map_batches(
        exprs=[pl.col(uni_col)],
        function=lambda x: pl.Series(apply_ht_inphase(x[0].to_numpy()))
    ).over("symbol").alias("factor_453")
