# Alpha299因子 - factor_190 (Polars版本)
# 原始因子编号: 190
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_190(w: int | None = 3, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha98因子：条件价格偏离因子
    
    参数:
        w: 核心时间窗口参数，默认3（原始为100，限制为30）
        uni_col: 单一基础数据列，默认'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'ma_window': 30,       # w，移动平均窗口（限制为30）
        'delta_window': 30,    # w，差分窗口（限制为30）
        'tsmin_window': 30,    # w，滚动最小值窗口（限制为30）
        'delta_3_window': 3    # 固定，3期差分窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    ma_window = window_sizes['ma_window']
    delta_window = window_sizes['delta_window']
    tsmin_window = window_sizes['tsmin_window']
    delta_3_window = window_sizes['delta_3_window']

    # 1. 计算移动平均
    ma = Ops.rolling_mean(uni_col, ma_window)

    # 2. 计算MA的差分
    delta_ma = ma - ma.shift(delta_window)

    # 3. 获取滞后的收盘价
    close_lag = pl.col(uni_col).shift(delta_window)

    # 4. 计算增长率
    growth_rate = delta_ma / (close_lag + eps)

    # 5. 计算滚动最小值
    tsmin = Ops.rolling_min(uni_col, tsmin_window)

    # 6. 计算3期差分
    delta_3 = pl.col(uni_col) - pl.col(uni_col).shift(delta_3_window)

    # 7. 应用条件计算因子值
    factor_result = pl.when(growth_rate <= 0.05).then(
        -(pl.col(uni_col) - tsmin)
    ).otherwise(
        -delta_3
    )

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_190")
