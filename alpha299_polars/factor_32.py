# Alpha299因子 - factor_32 (Polars版本)
# 原始因子编号: 32
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_32(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于价格位置、成交量排名相关性的复合因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 窗口配置
    window_configs = {
        'main_window': 12.0,        # w，主要计算窗口
        'correlation_window': 6.0   # w//2 = 12//2 = 6，相关系数窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        """
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    main_w = window_sizes['main_window']
    correlation_window = window_sizes['correlation_window']

    # 计算ts_min(LOW, main_w)
    low_min_12 = Ops.rolling_min("Low", main_w)

    # 计算ts_max(HIGH, main_w)
    high_max_12 = Ops.rolling_max("High", main_w)

    # 计算temp1 = (close - low_min_12) / (high_max_12 - low_min_12)
    denominator = high_max_12 - low_min_12
    temp1 = (pl.col("Close") - low_min_12) / (denominator + eps)

    # 对temp1进行滚动窗口排名（百分位）
    rank_temp1 = Ops.rolling_rank(temp1, main_w)

    # 对volume进行滚动窗口排名（百分位）
    rank_volume = Ops.rolling_rank("Volume", main_w)

    # 计算相关系数
    corr = Ops.rolling_corr(rank_temp1, rank_volume, correlation_window)

    # 对相关系数进行滚动窗口排名（百分位）
    rank_corr = Ops.rolling_rank(corr, correlation_window)

    # 最终因子值为 -rank_corr，只在最后使用一次.over()
    factor_result = -rank_corr

    return factor_result.over("symbol").cast(pl.Float32).alias("factor_32")
