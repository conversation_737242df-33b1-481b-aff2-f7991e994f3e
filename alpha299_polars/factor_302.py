# Alpha299因子 - factor_302 (Polars版本)
# 原始因子编号: 302
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_302(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha60因子：基于volume平方根和open的Z-score最小值与volume最大值的负相关系数减去low和volume的Z-score最小值差分
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 20,     # ts_zscore窗口
        'max_window': 5,         # ts_max窗口
        'corr_window': 5,        # ts_corr窗口
        'delta_window': 5        # delta窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    zscore_window = window_sizes['zscore_window']
    max_window = window_sizes['max_window']
    corr_window = window_sizes['corr_window']
    delta_window = window_sizes['delta_window']
    
    # ---- 1. zscore( sqrt(Volume) ) ----
    sqrt_volume        = (pl.col("Volume").abs() + eps).sqrt().alias("_sqrt_vol")
    sqrt_volume_mean   = Ops.rolling_mean(sqrt_volume, zscore_window).alias("_sqrt_vol_mean")
    sqrt_volume_std    = Ops.rolling_std(sqrt_volume, zscore_window).alias("_sqrt_vol_std")
    zscore_sqrt_volume = ((sqrt_volume - sqrt_volume_mean) / (sqrt_volume_std + eps)).alias("_zsv")

    # ---- 2. zscore(Open) ----
    open_mean  = Ops.rolling_mean("Open", zscore_window).alias("_open_mean")
    open_std   = Ops.rolling_std("Open", zscore_window).alias("_open_std")
    zscore_open = ((pl.col("Open") - open_mean) / (open_std + eps)).alias("_zopen")

    # ---- 3. min_horizontal 两个已命名表达式 -> 再 alias ----
    gp_min_1 = pl.min_horizontal([zscore_sqrt_volume, zscore_open]).alias("_gp_min1")

    max_volume = Ops.rolling_max("Volume", max_window).alias("_max_vol")

    # ---- 4. 相关系数：确保两个输入名字不同 ----
    corr_result = Ops.rolling_corr(gp_min_1, max_volume, corr_window).alias("_corr_gp_min1_maxvol")
    neg_corr = (-corr_result).alias("_neg_corr")

    # ---- 5. zscore(Low) 与 zscore(Volume) ----
    low_mean    = Ops.rolling_mean("Low", zscore_window).alias("_low_mean")
    low_std     = Ops.rolling_std("Low", zscore_window).alias("_low_std")
    zscore_low  = ((pl.col("Low") - low_mean) / (low_std + eps)).alias("_zlow")

    volume_mean = Ops.rolling_mean("Volume", zscore_window).alias("_vol_mean")
    volume_std  = Ops.rolling_std("Volume", zscore_window).alias("_vol_std")
    zscore_volume = ((pl.col("Volume") - volume_mean) / (volume_std + eps)).alias("_zvol")

    gp_min_2 = pl.min_horizontal([zscore_low, zscore_volume]).alias("_gp_min2")

    delta_result = (gp_min_2 - gp_min_2.shift(delta_window)).alias("_delta_gp_min2")

    factor_result = (neg_corr - delta_result)

    return factor_result.over("symbol").cast(pl.Float32).alias("factor_302")