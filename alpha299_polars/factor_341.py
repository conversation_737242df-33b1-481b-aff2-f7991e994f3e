# Alpha299因子 - factor_341 (Polars版本)
# 原始因子编号: 341
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_341(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha145因子：基于close-amount回归贝塔最大值的Z-score加上log(amount)的Z-score
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'window_regbeta': 18,  # 回归贝塔系数窗口
        'window_max': 9,       # 滚动最大值窗口
        'window_zscore': 20    # 标准化窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    window_regbeta = window_sizes['window_regbeta']
    window_max = window_sizes['window_max']
    window_zscore = window_sizes['window_zscore']

    # 使用别名避免重复字段问题
    # 1. 计算close对amount的回归贝塔系数
    ts_regbeta = Ops.rolling_regbeta("Close", "Amount", window_regbeta).alias("_ts_regbeta")

    # 2. 计算ts_regbeta的滚动最大值
    ts_max = Ops.rolling_max(ts_regbeta, window_max).alias("_ts_max")

    # 3. 对ts_max进行滚动标准化
    ts_max_mean = Ops.rolling_mean(ts_max, window_zscore).alias("_ts_max_mean")
    ts_max_std = Ops.rolling_std(ts_max, window_zscore).alias("_ts_max_std")
    ts_zscore_max = ((ts_max - ts_max_mean) / (ts_max_std + eps)).alias("_ts_zscore_max")

    # 4. 计算log(amount)
    log_amount = (pl.col("Amount").abs() + eps).log().alias("_log_amount")

    # 5. 对log_amount进行滚动标准化
    log_amount_mean = Ops.rolling_mean(log_amount, window_zscore).alias("_log_amount_mean")
    log_amount_std = Ops.rolling_std(log_amount, window_zscore).alias("_log_amount_std")
    ts_zscore_log = ((log_amount - log_amount_mean) / (log_amount_std + eps)).alias("_ts_zscore_log")

    # 6. 计算Alpha145 = ts_zscore_max + ts_zscore_log
    factor_result = ts_zscore_max + ts_zscore_log

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_341")
