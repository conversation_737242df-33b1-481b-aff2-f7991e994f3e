# Alpha299因子 - factor_183 (Polars版本)
# 原始因子编号: 183
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_183(w: int | None = 4, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于VWAP差分和价格结构的复合因子（简化版）
    
    参数:
        w: 核心可调参数，默认为4
        uni_col: 单一基础数据列参数（本因子使用多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_vwap_window': 4,      # Delta4 VWAP的滞后窗口
        'decay_linear_7': 7,         # DecayLinear窗口7
        'decay_linear_11': 11,       # DecayLinear窗口11
        'ts_rank_7': 7               # ts_rank窗口7
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delta_vwap_window = window_sizes['delta_vwap_window']
    decay_linear_7 = window_sizes['decay_linear_7']
    decay_linear_11 = window_sizes['decay_linear_11']
    ts_rank_7 = window_sizes['ts_rank_7']

    # 1. 计算VWAP
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. 计算VWAP的差分
    delta_vwap = vwap - vwap.shift(delta_vwap_window)

    # 3. 应用线性衰减
    dl1 = Ops.decaylinear(delta_vwap, decay_linear_7)

    # 4. 简化实现：使用时序排名代替横截面排名
    r1 = Ops.rolling_rank(dl1, decay_linear_7)

    # 5. 计算Term_t
    denominator = pl.col("Open") - (pl.col("High") + pl.col("Low")) / 2
    term_t = (pl.col("Low") - vwap) / (denominator + eps)

    # 6. 应用线性衰减到Term_t
    dl2 = Ops.decaylinear(term_t, decay_linear_11)

    # 7. 计算时序排名 R2
    r2 = Ops.rolling_rank(dl2, ts_rank_7)

    # 8. 计算最终因子
    factor_result = -(r1 + r2)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_183")
