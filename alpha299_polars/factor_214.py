# Alpha299因子 - factor_214 (Polars版本)
# 原始因子编号: 214
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_214(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha131因子：基于volume-open回归贝塔的Z-score排名与low-close回归贝塔负值的和（简化版）
    
    参数:
        w: 核心窗口参数（单位为天），默认9
        uni_col: 单一基础列参数（本因子不适用，故设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 9,   # w，第一个窗口参数
        'n2': 16   # int((16/9)*w) = int((16/9)*9) = 16，第二个窗口参数
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']

    # 第一部分计算
    # 1. 计算T1: ts_regbeta(volume, open, n1)
    T1 = Ops.rolling_regbeta("Volume", "Open", n1)

    # 2. T1_std: ts_zscore(T1, n1)
    T1_mean = Ops.rolling_mean(T1, n1)
    T1_std_val = Ops.rolling_std(T1, n1)
    T1_std = (T1 - T1_mean) / (T1_std_val + eps)

    # 3. X1: rank(T1_std) - 简化实现：使用时序排名代替横截面排名
    X1 = Ops.rolling_rank(T1_std, n1)

    # 第二部分计算
    # 4. T2: ts_regbeta(low, close, n2)
    T2 = Ops.rolling_regbeta("Low", "Close", n2)

    # 5. X2: neg(T2)
    X2 = -T2

    # 最终因子计算
    factor_result = X1 + X2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_214")
