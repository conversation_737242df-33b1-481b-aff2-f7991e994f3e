# Alpha299因子 - factor_162 (Polars版本)
# 原始因子编号: 162
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_162(w: int | None = 8, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha61因子：VWAP动量与最低价成交量相关性排名的最大值因子（简化版）
    
    参数:
        w: 核心可调参数（天数），默认为8
        uni_col: 单一基础数据列参数（本因子使用多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n_decay1': 12,      # w
        'n_decay2': 17,      # int(17 * w / 12)
        'n_corr_window': 8,  # int(8 * w / 12)
        'n_ma_window': 30    # 限制为30，原本是80
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n_decay1 = window_sizes['n_decay1']
    n_decay2 = window_sizes['n_decay2']
    n_corr_window = window_sizes['n_corr_window']
    n_ma_window = window_sizes['n_ma_window']

    # 1. 计算VWAP
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. 计算VWAP的一期差分
    delta_vwap = vwap - vwap.shift(1)

    # 3. 对delta_vwap应用线性衰减加权求和
    dl1 = Ops.decaylinear(delta_vwap, n_decay1)

    # 4. 简化实现：使用时序排名代替横截面排名
    r1 = Ops.rolling_rank(dl1, n_decay1)

    # 5. 计算最低价与成交量滚动均值的滚动相关系数
    ma_volume = Ops.rolling_mean("Volume", n_ma_window)
    corr_lv = Ops.rolling_corr("Low", ma_volume, n_corr_window)

    # 6. 简化实现：使用时序排名代替横截面排名
    rank_corr_lv = Ops.rolling_rank(corr_lv, n_corr_window)

    # 7. 对rank_corr_lv应用线性衰减加权求和
    dl2 = Ops.decaylinear(rank_corr_lv, n_decay2)

    # 8. 简化实现：使用时序排名代替横截面排名
    r2 = Ops.rolling_rank(dl2, n_decay2)

    # 9. 取R1和R2的最大值并取负
    factor_result = -pl.max_horizontal([r1, r2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_162")
