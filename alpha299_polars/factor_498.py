# Alpha299因子 - factor_498 (Polars版本)
# 原始因子编号: 498
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def minus_di_numba(high, low, close, window):
    """
    使用Numba优化的下降方向线(-DI)计算
    """
    n = len(high)
    if n < window:
        return np.full(n, np.nan)
    
    minus_di = np.full(n, np.nan)
    
    # 计算真实波幅(TR)和下降动向值(-DM)
    tr = np.zeros(n)
    minus_dm = np.zeros(n)
    
    for i in range(1, n):
        # 计算真实波幅
        hl = high[i] - low[i]
        hc = abs(high[i] - close[i-1])
        lc = abs(low[i] - close[i-1])
        tr[i] = max(hl, hc, lc)
        
        # 计算上升和下降移动
        up_move = high[i] - high[i-1]
        down_move = low[i-1] - low[i]
        
        # 计算-DM
        if down_move > up_move and down_move > 0:
            minus_dm[i] = down_move
        else:
            minus_dm[i] = 0.0
    
    # 计算平滑的TR和-DM
    tr_n = np.zeros(n)
    minus_dm_n = np.zeros(n)
    
    # 初始化：前window个值的总和
    if n >= window:
        tr_n[window-1] = np.sum(tr[:window])
        minus_dm_n[window-1] = np.sum(minus_dm[:window])
        
        # 使用Wilder平滑法计算后续值
        for i in range(window, n):
            tr_n[i] = tr_n[i-1] - (tr_n[i-1] / window) + tr[i]
            minus_dm_n[i] = minus_dm_n[i-1] - (minus_dm_n[i-1] / window) + minus_dm[i]
    
    # 计算-DI
    for i in range(window-1, n):
        if tr_n[i] > 1e-8:
            minus_di[i] = 100.0 * minus_dm_n[i] / tr_n[i]
        else:
            minus_di[i] = 0.0
    
    return minus_di

def factor_498(w: int | None = 14, uni_col: str | None = None) -> pl.Expr:
    """
    计算下降方向线 (Minus Directional Indicator, -DI)
    
    参数:
        w: 时间周期参数，默认为14
        uni_col: 此因子不使用单一基础列，设为None
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'minus_di_window': 14
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    minus_di_window = window_sizes['minus_di_window']
    
    def apply_minus_di(high_values, low_values, close_values):
        return minus_di_numba(high_values, low_values, close_values, minus_di_window)
    
    return pl.map_batches(
        exprs=[pl.col("High"), pl.col("Low"), pl.col("Close")],
        function=lambda x: pl.Series(apply_minus_di(x[0].to_numpy(), x[1].to_numpy(), x[2].to_numpy()))
    ).over("symbol").alias("factor_498")
