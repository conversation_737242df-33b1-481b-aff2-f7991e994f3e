# Alpha299因子 - factor_328 (Polars版本)
# 原始因子编号: 328
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_328(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha109因子：基于延迟amount的对数与延迟vwap对vwap+high+close回归残差Z-score的最大值
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delay_amount_period': 5,   # amount延迟周期
        'delay_vwap_period': 10,    # vwap延迟周期
        'regres_period': 16,        # 回归周期
        'zscore_period': 20         # Z-score周期
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delay_amount_period = window_sizes['delay_amount_period']
    delay_vwap_period = window_sizes['delay_vwap_period']
    regres_period = window_sizes['regres_period']
    zscore_period = window_sizes['zscore_period']

    # 使用别名避免重复字段问题
    # 1. 计算T1 = delay(amount, delay_amount_period)
    T1 = pl.col("Amount").shift(delay_amount_period).alias("_T1")

    # 2. 计算X1 = log(T1)
    X1 = (T1.abs() + eps).log().alias("_X1")

    # 3. 计算T2 = delay(vwap, delay_vwap_period)
    T2 = pl.col("Vwap").shift(delay_vwap_period).alias("_T2")

    # 4. 计算T3 = add(vwap, high)
    T3 = (pl.col("Vwap") + pl.col("High")).alias("_T3")

    # 5. 计算T4 = add(T3, close)
    T4 = (T3 + pl.col("Close")).alias("_T4")

    # 6. 计算T5 = ts_regres(T2, T4, regres_period)
    T5 = Ops.rolling_regres(T2, T4, regres_period).alias("_T5")

    # 7. 计算X2 = ts_zscore(T5, zscore_period)
    T5_mean = Ops.rolling_mean(T5, zscore_period).alias("_T5_mean")
    T5_std = Ops.rolling_std(T5, zscore_period).alias("_T5_std")
    X2 = ((T5 - T5_mean) / (T5_std + eps)).alias("_X2")

    # 8. 计算gp_max(X1, X2)
    factor_result = pl.max_horizontal([X1, X2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_328")
