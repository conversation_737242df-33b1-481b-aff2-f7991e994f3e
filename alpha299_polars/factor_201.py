# Alpha299因子 - factor_201 (Polars版本)
# 原始因子编号: 201
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_201(w: int | None = 10, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha112因子：基于volume和high的Z-score差值与low+close对数的滚动回归贝塔因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 10,    # Z-score计算窗口
        'regression_window': 10 # 滚动回归窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    zscore_window = window_sizes['zscore_window']
    regression_window = window_sizes['regression_window']

    # 1. 计算log(volume)的Z-score
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    log_volume = volume_protected.log()
    
    log_vol_mean = Ops.rolling_mean(log_volume, zscore_window)
    log_vol_std = Ops.rolling_std(log_volume, zscore_window)
    z_log_volume = (log_volume - log_vol_mean) / (log_vol_std + eps)

    # 2. 计算high的Z-score
    high_mean = Ops.rolling_mean("High", zscore_window)
    high_std = Ops.rolling_std("High", zscore_window)
    z_high = (pl.col("High") - high_mean) / (high_std + eps)

    # 3. 构建X1 = Z(log(volume)) - Z(high)
    X1 = z_log_volume - z_high

    # 4. 计算log(low + close)作为Y
    low_close_sum = pl.col("Low") + pl.col("Close")
    log_low_close = (low_close_sum + eps).log()

    # 5. 计算滚动回归贝塔系数
    factor_result = Ops.rolling_regbeta(X1, log_low_close, regression_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_201")
