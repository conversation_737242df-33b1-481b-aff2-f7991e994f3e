# Alpha299因子 - factor_400 (Polars版本)
# 原始因子编号: 400
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_400(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha92因子：基于复杂的low-open最小值与low标准差相关系数、amount Z-score、volume反正切等计算
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 20,  # low标准差窗口
        'n2': 5,   # 相关系数窗口
        'n3': 8,   # T2标准差窗口
        'n4': 12   # Z-score窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']

    # 使用别名避免重复字段问题
    # 1. 计算low和open的最小值
    T1 = pl.min_horizontal([pl.col("Low"), pl.col("Open")]).alias("_T1")

    # 2. 计算low的滚动标准差
    S1 = Ops.rolling_std("Low", n1).alias("_S1")

    # 3. 计算T1和S1的滚动相关系数
    T2 = Ops.rolling_corr(T1, S1, n2).alias("_T2")

    # 4. 计算T2的滚动标准差
    N1 = Ops.rolling_std(T2, n3).alias("_N1")

    # 5. 计算amount的滚动Z-score
    amount_mean = Ops.rolling_mean("Amount", n4).alias("_amount_mean")
    amount_std = Ops.rolling_std("Amount", n4).alias("_amount_std")
    T3 = ((pl.col("Amount") - amount_mean) / (amount_std + eps)).alias("_T3")

    # 6. 计算T3的滚动Z-score
    T3_mean = Ops.rolling_mean(T3, n4).alias("_T3_mean")
    T3_std = Ops.rolling_std(T3, n4).alias("_T3_std")
    T5 = ((T3 - T3_mean) / (T3_std + eps)).alias("_T5")

    # 7. 计算volume的反正切值
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    T4 = (volume_protected + eps).arctan().alias("_T4")

    # 8. 计算T5除以T4
    N2 = (T5 / (T4 + eps)).alias("_N2")

    # 9. 计算N1与N2的差值
    N3 = (N1 - N2).alias("_N3")

    # 10. 计算low的滚动Z-score
    low_mean = Ops.rolling_mean("Low", n4).alias("_low_mean")
    low_std = Ops.rolling_std("Low", n4).alias("_low_std")
    L1 = ((pl.col("Low") - low_mean) / (low_std + eps)).alias("_L1")

    # 11. 计算L1与N3的差值
    X1 = (L1 - N3).alias("_X1")

    # 12. 计算X1除以S1得到Alpha92
    factor_result = X1 / (S1 + eps)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_400")
