# Alpha299因子 - factor_493 (Polars版本)
# 原始因子编号: 493
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def minindex_numba(data, window):
    """
    使用Numba优化的最小值索引计算
    """
    n = len(data)
    if n < window:
        return np.full(n, np.nan)
    
    minindex = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        # 获取窗口数据
        start_idx = i - window + 1
        window_data = data[start_idx:i+1]
        
        # 找到最小值
        min_value = np.min(window_data)
        
        # 从后往前找最小值的位置（如果有多个相同最小值，取最后一个）
        min_pos_in_window = -1
        for j in range(window-1, -1, -1):
            if abs(window_data[j] - min_value) < 1e-9:
                min_pos_in_window = j
                break
        
        if min_pos_in_window >= 0:
            # 计算在原始数组中的索引
            minindex[i] = start_idx + min_pos_in_window
        else:
            minindex[i] = np.nan
    
    return minindex

def factor_493(w: int | None = 30, uni_col: str | None = 'Close') -> pl.Expr:
    """
    计算周期内最小值索引 (MININDEX) 因子
    
    参数:
        w: 周期长度，默认为30
        uni_col: 用于计算的数据列，默认为'Close'
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'minindex_window': 30
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    minindex_window = window_sizes['minindex_window']
    
    def apply_minindex(data_values):
        return minindex_numba(data_values, minindex_window)
    
    return pl.map_batches(
        exprs=[pl.col(uni_col)],
        function=lambda x: pl.Series(apply_minindex(x[0].to_numpy()))
    ).over("symbol").alias("factor_493")
