{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2d768ae6", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "\n", "df = pl.read_parquet(\"/disk4/shared/intern/laiyc/preprocess/y1.parquet\")"]}, {"cell_type": "code", "execution_count": 2, "id": "0189a994", "metadata": {}, "outputs": [], "source": ["df.write_parquet(\"/home/<USER>/polarsAlpha/diskBig/y1.parquet\")"]}, {"cell_type": "code", "execution_count": 4, "id": "6bca061e", "metadata": {}, "outputs": [], "source": ["DSH = pl.read_parquet(\"/home/<USER>/polarsAlpha/diskBig/OHLCVA_vwap.parquet\", columns=[\"datetime\"])"]}, {"cell_type": "code", "execution_count": 5, "id": "90a68c10", "metadata": {}, "outputs": [], "source": ["df = df.hstack(DSH)"]}, {"cell_type": "code", "execution_count": 6, "id": "8b22bd9c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (44_220_825, 2)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>y1</th><th>datetime</th></tr><tr><td>f32</td><td>datetime[μs]</td></tr></thead><tbody><tr><td>0.009516</td><td>2023-06-01 09:25:00</td></tr><tr><td>0.007779</td><td>2023-06-01 09:30:00</td></tr><tr><td>0.006034</td><td>2023-06-01 09:31:00</td></tr><tr><td>0.006024</td><td>2023-06-01 09:32:00</td></tr><tr><td>0.010354</td><td>2023-06-01 09:33:00</td></tr><tr><td>&hellip;</td><td>&hellip;</td></tr><tr><td>0.004848</td><td>2023-07-31 14:56:00</td></tr><tr><td>0.004848</td><td>2023-07-31 14:57:00</td></tr><tr><td>0.004848</td><td>2023-07-31 14:58:00</td></tr><tr><td>0.004622</td><td>2023-07-31 14:59:00</td></tr><tr><td>null</td><td>2023-07-31 15:00:00</td></tr></tbody></table></div>"], "text/plain": ["shape: (44_220_825, 2)\n", "┌──────────┬─────────────────────┐\n", "│ y1       ┆ datetime            │\n", "│ ---      ┆ ---                 │\n", "│ f32      ┆ datetime[μs]        │\n", "╞══════════╪═════════════════════╡\n", "│ 0.009516 ┆ 2023-06-01 09:25:00 │\n", "│ 0.007779 ┆ 2023-06-01 09:30:00 │\n", "│ 0.006034 ┆ 2023-06-01 09:31:00 │\n", "│ 0.006024 ┆ 2023-06-01 09:32:00 │\n", "│ 0.010354 ┆ 2023-06-01 09:33:00 │\n", "│ …        ┆ …                   │\n", "│ 0.004848 ┆ 2023-07-31 14:56:00 │\n", "│ 0.004848 ┆ 2023-07-31 14:57:00 │\n", "│ 0.004848 ┆ 2023-07-31 14:58:00 │\n", "│ 0.004622 ┆ 2023-07-31 14:59:00 │\n", "│ null     ┆ 2023-07-31 15:00:00 │\n", "└──────────┴─────────────────────┘"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["small_df = df.filter((pl.col(\"datetime\") >= pl.datetime(2023, 6, 1, 9, 25)) \n", "                     & (pl.col(\"datetime\") <= pl.datetime(2023, 7, 31, 15, 0)))\n", "small_df"]}, {"cell_type": "code", "execution_count": 7, "id": "e66adc70", "metadata": {}, "outputs": [], "source": ["small_df.select(pl.col(\"y1\")).write_parquet(\"disk/y1.parquet\")"]}], "metadata": {"kernelspec": {"display_name": "lyc", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}