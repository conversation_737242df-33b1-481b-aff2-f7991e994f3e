# Alpha299因子Polars转写指南

## 项目背景

polarsAlpha项目旨在将Alpha299.py中的299个因子函数从pandas实现转换为高性能的Polars表达式，处理4400万行分钟级股票数据，通过使用Numba加速的自定义算子实现10-17倍性能提升。

### 数据格式
- **原始数据**: `/home/<USER>/polarsAlpha/disk/OHLCVA_vwap.parquet`
- **数据规模**: 4555只股票，2个月分钟级数据
- **列结构**: `date`(str), `symbol`(str), `hhmm`(str), `Open`(f32), `High`(f32), `Low`(f32), `Close`(f32), `Volume`(f32), `Amount`(f32), `Vwap`(f32), `datetime`(datetime); 这与pandas版本的原始因子所用的字段不一致，请留意
- **排序**: 数据已按`[datetime, symbol]`排序
- **务必使用真实数据测试**


### 目录结构
```
polarsAlpha/
├── alpha299_original/     # 原始pandas实现的因子
├── alpha299_polars/       # 转写后的Polars版本
├── ops/                   # 高性能算子库
│   ├── operators/         # 算子实现
│   ├── tests/            # 测试脚本
│   └── examples/         # 使用示例
└── disk/                 # 数据文件
```

## 核心设计原则

### 1. 绝对不允许近似实现
- **严格按照原始因子逻辑实现**，不得因为认为Polars表达式有限制而妥协
- 充分利用ops算子如`rolling_rank`、`rolling_corr`等
- 如果遇到表达式限制，寻找正确的实现方式而非近似
- 你不应该使用任何pandas相关，允许使用numba和polars

### 2. ⚠️ 关键约束：.over()只能在最后使用一次
- **绝对不能嵌套使用.over()操作**，这会导致"window expression not allowed in aggregation"错误
- 所有算子调用都不带.over()，只在最终表达式使用一次`.over("symbol")`
- 正确示例：`(expr1 * expr2).over("symbol")`
- 错误示例：`expr1.over("symbol") * expr2.over("symbol")`

### 3. 统一的API设计

- 保持原来w, uni_col的设计

```python
def factor_X(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    因子描述
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期
        uni_col: 单一基础列参数（如果因子不使用则设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式，别名为"factor_X"
    """
```

### 3. 数据类型规范
- **统一返回Float32类型**（除rolling_cov返回Float64保证精度）
- **保持输出行数不变**，绝对不使用`.drop_nulls()`
- **统计null值和nan值数量**，但不删除它们

## 算子库设计

### 重构后的算子特点
- **每个算子都针对单一时序**，不包含`by_group`参数
- **避免窗口表达式嵌套问题**，需要手动使用`.over("symbol")`进行分组
- **支持col_or_expr参数**，可以接受字符串列名或表达式

### 可用算子列表
```python
# 单列算子
Ops.rolling_rank(col_or_expr, window)
Ops.decaylinear(col_or_expr, window)
Ops.rolling_mean(col_or_expr, window)
Ops.rolling_std(col_or_expr, window)
Ops.rolling_sum(col_or_expr, window)
Ops.rolling_max(col_or_expr, window)
Ops.rolling_min(col_or_expr, window)
Ops.rolling_var(col_or_expr, window)

# 双列算子
Ops.rolling_cov(col_x_or_expr, col_y_or_expr, window)
Ops.rolling_corr(col_x_or_expr, col_y_or_expr, window)
Ops.rolling_regbeta(col_x_or_expr, col_y_or_expr, window)
Ops.rolling_regres(col_x_or_expr, col_y_or_expr, window)
```

## 转写流程步骤

### 0. 安装所需依赖
根据requirements创建一个新的conda，并安装所需依赖


### 1. 分析原始因子
```python
# 查看原始实现
view alpha299_original/factor_X.py

# 关键信息提取：
# - 因子逻辑（忽略数据处理部分）
# - 参数w和uni_col的使用
# - calculate_window_sizes函数（完全保留）
# - 涉及的数据列（注意大小写转换：close -> Close）
```

### 2. 创建Polars版本
```python
# 文件模板
# Alpha299因子 - factor_X (Polars版本)
# 原始因子编号: X
# 转写时间: YYYY-MM-DD

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_X(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    # 保留原始的calculate_window_sizes函数
    # 实现因子逻辑
    # 返回表达式
```

### 3. 处理窗口计算
```python
# 完全保留原始的calculate_window_sizes函数
window_sizes = calculate_window_sizes(w)
rolling_window = window_sizes['rolling_window']
```

### 4. 实现因子逻辑
```python
# 简单因子示例
return (
    (pl.col("Close") - pl.col("Open")) / 
    (pl.col("High") - pl.col("Low") + 0.001 + eps)
).cast(pl.Float32).alias("factor_X")

# 复杂因子示例（需要算子）
open_rank = Ops.rolling_rank("Open", rolling_window)
volume_rank = Ops.rolling_rank("Volume", rolling_window)
return (-Ops.rolling_corr(open_rank, volume_rank, rolling_window).over("symbol")).alias("factor_X")
```

## 踩过的坑与解决方案

### 1. 窗口表达式嵌套问题 ⚠️ 最重要的坑
**问题**: `window expression not allowed in aggregation`
```python
# ❌ 错误写法 - 多次使用.over()
expr1 = some_calculation().over("symbol")
expr2 = other_calculation().over("symbol")
return (expr1 * expr2).alias("factor_X")  # 这会报错！

# ❌ 另一种错误写法
return Ops.rolling_corr(
    Ops.rolling_rank("Open", window).over("symbol"),
    Ops.rolling_rank("Volume", window).over("symbol"),
    window
).over("symbol")
```

**解决方案**: 只在最后使用一次.over()
```python
# ✅ 正确写法 - 只在最后使用一次.over()
expr1 = some_calculation()  # 不使用.over()
expr2 = other_calculation()  # 不使用.over()
return (expr1 * expr2).over("symbol").alias("factor_X")

# ✅ 复杂表达式的正确写法
open_rank = Ops.rolling_rank("Open", window)
volume_rank = Ops.rolling_rank("Volume", window)
return (-Ops.rolling_corr(open_rank, volume_rank, window)).over("symbol").alias("factor_X")
```

### 2. 精度问题
**问题**: rolling_cov准确性测试失败
**解决方案**: 
- 使用Float64保证协方差计算精度
- 确保Numba实现与numpy.cov完全一致

### 3. 数据类型不匹配
**问题**: 算子只接受字符串列名，不支持表达式
**解决方案**: 修改算子支持col_or_expr参数
```python
if isinstance(col_or_expr, str):
    expr = pl.col(col_or_expr)
else:
    expr = col_or_expr
```

## 测试与验证

### 1. 单因子测试
```python
# 使用示例
df = load_data().head(100000)
result = df.select([
    pl.col("date"),
    pl.col("symbol"),
    pl.col("hhmm"),
    factor_X(w=10)
])
```

### 2. 统计分析
```python
# 检查结果质量
factor_stats = result.select([
    pl.col("factor_X").count().alias("total_count"),
    pl.col("factor_X").null_count().alias("null_count"),
    pl.col("factor_X").is_nan().sum().alias("nan_count"),
    pl.col("factor_X").filter(~pl.col("factor_X").is_null() & ~pl.col("factor_X").is_nan()).mean().alias("mean"),
    pl.col("factor_X").filter(~pl.col("factor_X").is_null() & ~pl.col("factor_X").is_nan()).std().alias("std")
])
```

### 3. 算子库测试
```python
# 运行完整测试
python ops/tests/test_final.py

# 期望结果：准确性通过率100%
```

## 注意事项

### 1. 数据字段映射
- 原始代码：小写字段名（close, open, high, low）
- 新数据格式：大写字段名（Close, Open, High, Low）

### 2. 分组操作
- 数据已按`[datetime, symbol]`排序，无需重新排序
- 使用`.over("symbol")`进行分组操作

### 3. 空值处理
- **绝对不使用`.drop_nulls()`**
- 保持输出行数与输入一致
- 统计但不删除null和nan值

### 4. 性能考虑
- 优先使用Numba加速的算子
- 避免不必要的数据类型转换
- 合理使用批量操作

## 成功案例

### factor_1: 简单算术因子
```python
def factor_1(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    return (
        (pl.col("Close") - pl.col("Open")) / 
        (pl.col("High") - pl.col("Low") + 0.001 + eps)
    ).cast(pl.Float32).alias("factor_1")
```

### factor_18: 复杂算子组合
```python
def factor_18(w: int | None = 10, uni_col: str | None = None) -> pl.Expr:
    window_sizes = calculate_window_sizes(w)
    rolling_window = window_sizes['rolling_window']
    
    open_rank = Ops.rolling_rank("Open", rolling_window)
    volume_rank = Ops.rolling_rank("Volume", rolling_window)
    return (-Ops.rolling_corr(open_rank, volume_rank, rolling_window).over("symbol")).alias("factor_18")
```

## 因子测试工具

### 命令行测试工具

项目提供了强大的命令行测试工具 `alpha299_polars/test_factors.py`，支持灵活的因子测试：

#### 基本用法

```bash
# 测试指定因子
python test_factors.py --i=18                    # 测试factor_18
python test_factors.py --factor=1                # 测试factor_1

# 列出所有可用因子
python test_factors.py --list

# 测试所有因子
python test_factors.py --all
```

#### 高级参数

```bash
# 指定样本数据大小
python test_factors.py --i=18 --sample=100000    # 使用10万行样本数据

# 指定因子参数
python test_factors.py --i=18 --w=20             # 指定窗口参数w
python test_factors.py --i=18 --uni_col=Close    # 指定单列参数
python test_factors.py --i=18 --eps=1e-6         # 指定防除零参数

# 组合使用
python test_factors.py --i=18 --w=20 --sample=50000
```

#### 输出示例

```
=== 加载数据 ===
✓ 数据加载成功
  - 数据形状: (44220825, 11)
  - 数据列: ['date', 'symbol', 'hhmm', 'Open', 'High', 'Low', 'Close', 'Volume', 'Amount', 'Vwap', 'datetime']
  - 使用样本数据: 50000行

=== 测试 factor_18 ===
✓ factor_18 计算成功
  - 计算时间: 2.47秒
  - 结果形状: (50000, 5)
  - 结果列: ['date', 'symbol', 'hhmm', 'datetime', 'factor_18']
  - 因子统计:
    * 总行数: 50000
    * 有效值数量: 50000.0
    * 空值数量: 0.0
    * NaN值数量: 1004.0
    * 均值: -0.015732
    * 标准差: 0.378334
    * 最小值: -1.000000
    * 最大值: 0.993441
  - 前5行结果:
    date    symbol hhmm            datetime  factor_18
20230601 000001.SZ 0925 2023-06-01 09:25:00        NaN
20230601 000001.SZ 0930 2023-06-01 09:30:00        NaN
20230601 000001.SZ 0931 2023-06-01 09:31:00  -1.000000
20230601 000001.SZ 0932 2023-06-01 09:32:00  -1.000000
20230601 000001.SZ 0933 2023-06-01 09:33:00  -0.217335

✓ factor_18 测试完成
```

#### 工具特性

1. **动态因子加载** - 自动发现和加载alpha299_polars目录下的所有因子文件
2. **灵活参数控制** - 支持指定w、uni_col、eps等因子参数
3. **样本数据测试** - 可指定样本大小，快速验证因子逻辑
4. **详细统计信息** - 提供完整的因子计算统计和数据质量分析
5. **批量测试支持** - 支持一次性测试所有可用因子

### 开发建议

1. **转写完成后立即测试**：
   ```bash
   python test_factors.py --i=新因子编号 --sample=10000
   ```

2. **参数调优测试**：
   ```bash
   python test_factors.py --i=18 --w=10 --sample=50000
   python test_factors.py --i=18 --w=20 --sample=50000
   ```

3. **性能基准测试**：
   ```bash
   python test_factors.py --i=18 --sample=1000000  # 100万行数据
   ```

4. **批量验证**：
   ```bash
   python test_factors.py --all --sample=100000    # 验证所有因子
   ```

## 总结

通过严格遵循这套转写指南，我们成功实现了高性能、高精度的Polars因子库，为处理大规模金融数据提供了强有力的工具。关键在于：

1. **绝不妥协的准确性要求**
2. **合理的算子库设计**
3. **统一的API规范**
4. **充分的测试验证**
5. **便捷的测试工具**

这套方法论可以推广到更多因子的转写工作中。
