#!/usr/bin/env python3
"""
批量回测所有Alpha299因子
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from minute_factor_backtest import MinuteFactorBacktest

def main():
    """主函数"""
    print("=" * 60)
    print("Alpha299因子批量回测")
    print("=" * 60)
    
    # 初始化回测类
    backtest = MinuteFactorBacktest(data_dir="diskBig")
    
    # 批量回测所有因子
    results = backtest.batch_backtest_factors(
        factor_dir="alpha299_polars",
        output_dir="icBacktestLog/alpha299"
    )
    
    print("\n" + "=" * 60)
    print("批量回测完成!")
    print(f"成功因子数: {results['successful_factors']}")
    print(f"失败因子数: {results['failed_factors']}")
    
    if results['successful_factors'] > 0:
        summary = results['summary_stats']
        if 'error' not in summary:
            print(f"\n汇总统计:")
            print(f"平均IC: {summary['mean_ic_stats']['mean']:.4f} ± {summary['mean_ic_stats']['std']:.4f}")
            print(f"平均ICIR: {summary['icir_stats']['mean']:.4f} ± {summary['icir_stats']['std']:.4f}")
            print(f"平均IC>0比例: {summary['ic_positive_ratio_stats']['mean']:.2%}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
