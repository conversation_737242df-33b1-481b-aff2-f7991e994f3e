因子编号,i,类型,备注,公式
factor_1,1,基础字段,简单算术因子，无窗口操作,"计算 (CLOSE - OPEN) 与 (HIGH - LOW) 的比值，为防止分母为0，在分母上加上0.001。<br/>$$
\text{Alpha101} = \frac{\text{CLOSE}-\text{OPEN}}{(\text{HIGH}-\text{LOW})+0.001}.
$$"
factor_2,2,时序,差分+符号函数，使用shift操作,"利用 VOLUME 与 CLOSE 的 1 期差分及符号函数构造。<br/>$$
\text{Alpha12} = \operatorname{sign}\Bigl(\Delta(\text{VOLUME},1)\Bigr) \times \Bigl(-\Delta(\text{CLOSE},1)\Bigr).
$$"
factor_3,3,时序,收益率差分排名与相关性乘积,"先对 RETURNS 进行 3 期差分后排名取负，再乘以 OPEN 与 VOLUME 在 10 期内的相关性。<br/>$$
\text{Alpha14} = -\operatorname{rank}\Bigl(\Delta(\text{RETURNS},3)\Bigr) \times \operatorname{correlation}(\text{OPEN},\text{VOLUME},10).
$$"
factor_4,4,时序,排名协方差的负排名，5万行测试通过,"类似于因子 Alpha13，但以 HIGH 替代 CLOSE。<br/>$$
\text{Alpha16} = -\operatorname{rank}\Bigl(\operatorname{covariance}\bigl(\operatorname{rank}(\text{HIGH}),\,\operatorname{rank}(\text{VOLUME}),5\bigr)\Bigr).
$$"
factor_5,5,时序,收益率累计和排名复合因子,"根据 CLOSE 的 7 期延迟与差分之和的符号，乘以 (1+RETURNS 250 期累和排名) 后取负。<br/>$$
\text{Alpha19} = -\operatorname{sign}\Bigl[(\text{CLOSE}-\operatorname{delay}(\text{CLOSE},7))+\Delta(\text{CLOSE},7)\Bigr]\times \Bigl(1+\operatorname{rank}\Bigl(1+\operatorname{sum}(\text{RETURNS},250)\Bigr)\Bigr).
$$"
factor_10,10,截面，有问题,基于收益率、成交量、VWAP复合因子,"将 -RETURNS、ADV20、VWAP 与 (HIGH - CLOSE) 的乘积进行排名。<br/>$$
\text{Alpha25} = \operatorname{rank}\Bigl[-\text{RETURNS}\times \text{ADV20}\times \text{VWAP}\times (\text{HIGH}-\text{CLOSE})\Bigr].
$$"
factor_11,11,时序,成交量与价格相关性组合因子,"将 ADV20 与 LOW 的5期相关性，加上 (HIGH+LOW)/2 后减去 CLOSE，然后进行标准化处理。<br/>$$
\text{Alpha28} = \operatorname{scale}\Bigl(\operatorname{correlation}(\text{ADV20},\text{LOW},5)+\frac{\text{HIGH}+\text{LOW}}{2}-\text{CLOSE}\Bigr).
$$"
factor_12,12,时序,复杂时间序列嵌套运算因子,"该因子较为复杂，包含嵌套的排名、缩放、对数、累和、极小值、乘积、延迟和时间序列排名运算。<br/>$$
\begin{aligned}
\text{Alpha29} &= \min\Bigl(\operatorname{product}\Bigl(\operatorname{rank}\Bigl(\operatorname{rank}\bigl(\operatorname{scale}\Bigl(\log\Bigl(\operatorname{sum}\Bigl(\operatorname{ts\_min}\bigl(\operatorname{rank}\bigl(\operatorname{rank}\bigl(-\operatorname{rank}(\Delta(\text{CLOSE}-1,5)\bigr)\bigr)\bigr),2\bigr),1\Bigr)\Bigr)\bigr)\Bigr),1\Bigr)\Bigr),5\Bigr)\\[1mm]
&\quad +\, \operatorname{ts\_rank}\Bigl(\operatorname{delay}\bigl(-\text{RETURNS},6\bigr),5\Bigr).
\end{aligned}
$$"
factor_13,13,时序,成交量变化与价格变动相关性因子,"计算对数成交量差分排名与价格变动比率排名之间的相关性，并取负。<br/>$$
\text{Alpha2} = -\operatorname{correlation}\Biggl(\operatorname{rank}\Bigl(\Delta\bigl(\log(\text{VOLUME}),2\bigr)\Bigr),\,\operatorname{rank}\Bigl(\frac{\text{CLOSE}-\text{OPEN}}{\text{OPEN}}\Bigr),\,6\Biggr).
$$"
factor_15,15,时序,收益率标准差比值和价格差分排名因子,"由两部分构成，分别为 1 减去 RETURNS 2期与5期标准差比值的排名，加上 1 减去 CLOSE 1期差分的排名，最后对总和进行排名。<br/>$$
\text{Alpha34} = \operatorname{rank}\Bigl[\Bigl(1-\operatorname{rank}\Bigl(\frac{\operatorname{stddev}(\text{RETURNS},2)}{\operatorname{stddev}(\text{RETURNS},5)}\Bigr)\Bigr)+\Bigl(1-\operatorname{rank}\Bigl(\Delta(\text{CLOSE},1)\Bigr)\Bigr)\Bigr].
$$"
factor_16,16,时序,成交量、价格组合和收益率排名乘积因子,"利用 VOLUME 的32期时间序列排名乘以 (1 - ts_rank(CLOSE+HIGH-LOW,16)) 乘以 (1 - ts_rank(RETURNS,32)) 得到最终值。<br/>$$
\text{Alpha35} = \operatorname{ts\_rank}(\text{VOLUME},32) \times \Bigl(1-\operatorname{ts\_rank}(\text{CLOSE}+\text{HIGH}-\text{LOW},16)\Bigr) \times \Bigl(1-\operatorname{ts\_rank}(\text{RETURNS},32)\Bigr).
$$"
factor_17,17,时序,CLOSE排名和CLOSE/OPEN比值排名乘积因子,"计算 CLOSE 的10期 ts_rank取负后排名，与 CLOSE/OPEN 比值的排名相乘。<br/>$$
\text{Alpha38} = \Bigl(-\operatorname{rank}\bigl(\operatorname{ts\_rank}(\text{CLOSE},10)\bigr)\Bigr) \times \operatorname{rank}\Bigl(\frac{\text{CLOSE}}{\text{OPEN}}\Bigr).
$$"
factor_18,18,时序,排名相关系数，使用算子组合,"计算开盘价与成交量的排名相关性，并取负。<br/>$$
\text{Alpha3} = -\operatorname{correlation}\Bigl(\operatorname{rank}(\text{OPEN}),\,\operatorname{rank}(\text{VOLUME}),\,10\Bigr).
$$"
factor_19,19,时序,HIGH标准差排名与HIGH-VOLUME相关系数负乘积因子,"计算 HIGH 的10期标准差排名与 HIGH 与 VOLUME 的10期相关性的乘积，并取负。<br/>$$
\text{Alpha40} = -\operatorname{rank}\Bigl(\operatorname{stddev}(\text{HIGH},10)\Bigr) \times \operatorname{correlation}(\text{HIGH},\text{VOLUME},10).
$$"
factor_21,21,截面,VWAP与CLOSE差值和和的排名比值因子,"计算 VWAP 与 CLOSE 的差值排名除以 VWAP 与 CLOSE 的和的排名。<br/>$$
\text{Alpha42} = \frac{\operatorname{rank}(\text{VWAP}-\text{CLOSE})}{\operatorname{rank}(\text{VWAP}+\text{CLOSE})}.
$$"
factor_22,22,时序,VOLUME/ADV20与CLOSE差分排名乘积因子,"计算 (VOLUME/ADV20) 的20期时间序列排名与 $-\Delta(\text{CLOSE},7)$ 的8期时间序列排名的乘积。<br/>$$
\text{Alpha43} = \operatorname{ts\_rank}\Bigl(\frac{\text{VOLUME}}{\text{ADV20}},20\Bigr) \times \operatorname{ts\_rank}\Bigl(-\Delta(\text{CLOSE},7),8\Bigr).
$$"
factor_23,23,时序,VOLUME排名与HIGH相关系数负值因子,"计算 HIGH 与 VOLUME 排名在5期内的相关性，并取负。<br/>$$
\text{Alpha44} = -\operatorname{correlation}\Bigl(\text{HIGH},\,\operatorname{rank}(\text{VOLUME}),\,5\Bigr).
$$"
factor_25,25,时序,基于价格差分条件判断的因子,"若 $\frac{\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10)}{10} - \frac{\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE}}{10}$ 小于 -0.1，则返回1；否则返回 $\operatorname{delay}(\text{CLOSE},1)-\text{CLOSE}$。<br/>$$
\text{Alpha49} =
\begin{cases}
1, & \frac{\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10)}{10} - \frac{\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE}}{10} < -0.1,\\[1mm]
\operatorname{delay}(\text{CLOSE},1)-\text{CLOSE}, & \text{otherwise.}
\end{cases}
$$"
factor_30,30,时序，有问题,价格位置比值差分累积排名因子,"计算 $\frac{(\text{CLOSE}-\text{LOW}) - (\text{HIGH}-\text{CLOSE})}{\text{CLOSE}-\text{LOW}}$ 的9期差分，取其排名后再取负。<br/>$$
\text{Alpha53} = -\operatorname{rank}\Bigl(\Delta\Bigl(\frac{(\text{CLOSE}-\text{LOW})-(\text{HIGH}-\text{CLOSE})}{\text{CLOSE}-\text{LOW}},9\Bigr)\Bigr).
$$"
factor_32,32,时序,价格位置与成交量排名相关性因子,"计算 $\frac{\text{CLOSE}-\operatorname{ts\_min}(\text{LOW},12)}{\operatorname{ts\_max}(\text{HIGH},12)-\operatorname{ts\_min}(\text{LOW},12)}$ 的排名与 VOLUME 排名的相关性在6期内的排名，并取负。<br/>$$
\text{Alpha55} = -\operatorname{rank}\Bigl(\operatorname{correlation}\Bigl(\operatorname{rank}\Bigl(\frac{\text{CLOSE}-\operatorname{ts\_min}(\text{LOW},12)}{\operatorname{ts\_max}(\text{HIGH},12)-\operatorname{ts\_min}(\text{LOW},12)}\Bigr),\,\operatorname{rank}(\text{VOLUME}),6\Bigr)\Bigr).
$$"
factor_33,33,时序,VWAP与ADV相关性排名比较因子,"比较 VWAP 与其16期内最小值之差的排名与 VWAP 与 ADV180 在17期内相关性的排名，返回二者比较结果（布尔值转换为数值）。<br/>$$
\text{Alpha61} = \operatorname{rank}\Bigl(\text{VWAP} - \operatorname{ts\_min}(\text{VWAP},16)\Bigr) < \operatorname{rank}\Bigl(\operatorname{correlation}(\text{VWAP},\text{ADV180},17)\Bigr).
$$"
factor_34,34,简化为时序,复杂因子：基于VWAP行业中性化、截面排名、相关性等的复合计算，简化了行业中性化和截面排名,"由两部分构成，分别为：<br/>  - 第一部分：对 indneutralize 后的 VWAP 计算2.72412期差分，再取其4.79344期内的 ts_max，并排名；<br/>  - 第二部分：计算加权 (CLOSE×0.490655 + VWAP×(1-0.490655)) 与 ADV20 在4.92416期内的相关性，经9.0615期 ts_rank 处理；<br/>二者以幂运算组合后取负。<br/>$$
\text{Alpha69} = -\Bigl[\operatorname{rank}\Bigl(\operatorname{ts\_max}\bigl(\Delta(\operatorname{indneutralize}(\text{VWAP},\text{indclass}),2.72412),4.79344\bigr)\Bigr)^{\operatorname{ts\_rank}\Bigl(\operatorname{correlation}\bigl((\text{CLOSE}\times 0.490655)+(\text{VWAP}\times (1-0.490655)),\text{ADV20},4.92416\bigr),9.0615\Bigr)}\Bigr].
$$"
factor_36,36,时序,价格排名相关性和价格差值复合因子,"由两部分构成，取两部分经过衰减及排名的值的最大值。<br/>$$
\text{Alpha71} = \max\Biggl\{\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl(\operatorname{ts\_rank}(\text{CLOSE},3.43976),\,\operatorname{ts\_rank}(\text{ADV180},12.0647),18.0175\Bigr),4.20501\bigr),15.6948\Bigr),\,\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\Bigl(\operatorname{rank}\Bigl((\text{LOW}+\text{OPEN})-(\text{VWAP}+\text{VWAP})\Bigr)^2,16.4662\bigr),4.4388\Bigr)\Biggr\}.
$$"
factor_37,37,时序,价格位置与成交量相关性复合因子,"将 (HIGH+LOW)/2 与 ADV40 之间的相关性经过衰减线性处理后排名，除以 VWAP 与 VOLUME 的 ts_rank相关性经过衰减线性处理后的排名。<br/>$$
\text{Alpha72} = \frac{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl(\frac{\text{HIGH}+\text{LOW}}{2},\text{ADV40},8.93345\Bigr),10.1519\bigr)\Bigr)}{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl(\operatorname{ts\_rank}(\text{VWAP},3.72469),\operatorname{ts\_rank}(\text{VOLUME},18.5188),6.86671\Bigr),2.95011\bigr)\Bigr)}.
$$"
factor_38,38,时序,价格位置与成交量相关性最小排名因子,"取两部分中较小者的排名。第一部分为 ((HIGH+LOW)/2+HIGH) 与 (VWAP+HIGH) 之差经20.0451期衰减处理后的排名；第二部分为 (HIGH+LOW)/2 与 ADV40 的相关性经5.64125期衰减处理后的排名。<br/>$$
\text{Alpha77} = \min\Bigl\{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(((\text{HIGH}+\text{LOW})/2+\text{HIGH}) - (\text{VWAP}+\text{HIGH}),20.0451\bigr)\Bigr),\,\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl(\frac{\text{HIGH}+\text{LOW}}{2},\text{ADV40},3.1614\Bigr),5.64125\bigr)\Bigr)\Bigr\}.
$$"
factor_39,39,时序,价格变化排名与成交量条件因子,"根据成交量与20日平均成交量（ADV20）的比较，若成交量较大，则计算收盘价7期差分绝对值的时间序列排名乘以其符号；否则固定返回 -1。<br/>$$
\text{Alpha7} =
\begin{cases}
-\operatorname{ts\_rank}\Bigl(\lvert\Delta(\text{CLOSE},7)\rvert,\,60\Bigr)\times \operatorname{sign}\Bigl(\Delta(\text{CLOSE},7)\Bigr), & \text{if } \text{ADV20} < \text{VOLUME},\\[1mm]
-1, & \text{otherwise.}
\end{cases}
$$"
factor_40,40,时序,开盘价和收益率乘积标准化排名因子,"计算 OPEN 与 RETURNS 的5期累和乘积与其10期延迟值之差的排名，并取负。<br/>$$
\text{Alpha8} = -\operatorname{rank}\Bigl[\Bigl(\operatorname{sum}(\text{OPEN},5)\cdot \operatorname{sum}(\text{RETURNS},5)\Bigr) - \operatorname{delay}\Bigl(\operatorname{sum}(\text{OPEN},5)\cdot \operatorname{sum}(\text{RETURNS},5),10\Bigr)\Bigr].
$$"
factor_42,42,时序,价格条件和排名相关性复合因子,"取两部分中较小者的 ts_rank。第一部分为 (((HIGH+LOW)/2+CLOSE) 与 (LOW+OPEN)) 的比较，经14.7221期衰减处理后计算 ts_rank（窗口期18.8683）；第二部分为 LOW 与 ADV30 排名相关性经过6.94024期衰减处理后计算 ts_rank（窗口期6.80584）。<br/>$$
\text{Alpha92} = \min\Bigl\{\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\Bigl(((\frac{\text{HIGH}+\text{LOW}}{2}+\text{CLOSE}) < (\text{LOW}+\text{OPEN})),14.7221\Bigr),18.8683\Bigr),\,\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}(\operatorname{rank}(\text{LOW}),\operatorname{rank}(\text{ADV30}),7.58555),6.94024\bigr),6.80584\Bigr)\Bigr\}.
$$"
factor_43,43,时序,VWAP差值排名与相关性排名幂运算因子,"计算 VWAP 与其 11.5783期最小值排名的幂次，与 VWAP 与 ADV60 在18.0926期内相关性经过衰减处理后的 ts_rank（窗口期2.70756）的乘积取负。<br/>$$
\text{Alpha94} = -\Bigl[\operatorname{rank}\Bigl(\text{VWAP}-\operatorname{ts\_min}(\text{VWAP},11.5783)\Bigr)^{\operatorname{ts\_rank}\Bigl(\operatorname{correlation}(\operatorname{ts\_rank}(\text{VWAP},19.6462),\operatorname{ts\_rank}(\text{ADV60},4.02992),18.0926),2.70756\Bigr)}\Bigr].
$$"
factor_44,44,时序,价格变化条件符号调整因子,"根据 CLOSE 的1期差分在5期内的最小值与最大值情况，返回差分或其负值。<br/>$$
\text{Alpha9} =
\begin{cases}
\Delta(\text{CLOSE},1), & \text{if } 0 < \operatorname{ts\_min}\bigl(\Delta(\text{CLOSE},1),5\bigr),\\[1mm]
\Delta(\text{CLOSE},1), & \text{if } \operatorname{ts\_max}\bigl(\Delta(\text{CLOSE},1),5\bigr) < 0,\\[1mm]
-\Delta(\text{CLOSE},1), & \text{otherwise.}
\end{cases}
$$"
factor_45,45,简化为时序,开盘价和成交量Z-score排名相关性负值因子，简化了横截面排名,"Let $R_{O,t} = rank_{cs}(Open_t)$.<br/>Let $R_{V,t} = rank_{cs}(Volume_t)$.<br/>$$\alpha_{105} = -corr(R_{O,t}, R_{V,t}, 10)$$"
factor_45_xs,45,截面+时序,开盘价和成交量Z-score排名相关性负值因子，简化了横截面排名,"Let $R_{O,t} = rank_{cs}(Open_t)$.<br/>Let $R_{V,t} = rank_{cs}(Volume_t)$.<br/>$$\alpha_{105} = -corr(R_{O,t}, R_{V,t}, 10)$$"
factor_46,46,简化为时序,开盘价与前日价格差值Z-score乘积因子，简化了横截面排名,Let $R_1 = -rank_{cs}(Open_t - High_{t-1})$.<br/>Let $R_2 = rank_{cs}(Open_t - Close_{t-1})$.<br/>Let $R_3 = rank_{cs}(Open_t - Low_{t-1})$.<br/>$$\alpha_{107} = R_1 \cdot R_2 \cdot R_3$$
factor_46_xs,46,截面+时序,开盘价与前日价格差值Z-score乘积因子，简化了横截面排名,Let $R_1 = -rank_{cs}(Open_t - High_{t-1})$.<br/>Let $R_2 = rank_{cs}(Open_t - Close_{t-1})$.<br/>Let $R_3 = rank_{cs}(Open_t - Low_{t-1})$.<br/>$$\alpha_{107} = R_1 \cdot R_2 \cdot R_3$$
factor_49,49,简化为时序,价格波动率、成交量Z-score和VWAP复合因子，简化了横截面排名,Let $Term_t = \frac{High_t - Low_t}{MA_5(Close_t)}$.<br/>Let $R_1 = rank_{cs}(Term_{t-2})$.<br/>Let $RankRankVol_t = rank_{cs}(rank_{cs}(Volume_t))$.<br/>Let $Denom_t = \frac{Term_t}{VWAP_t - Close_t}$.<br/>$$\alpha_{114} = \frac{R_1 \cdot RankRankVol_t}{Denom_t}$$
factor_49_xs,49,截面+时序,价格波动率、成交量Z-score和VWAP复合因子，简化了横截面排名,Let $Term_t = \frac{High_t - Low_t}{MA_5(Close_t)}$.<br/>Let $R_1 = rank_{cs}(Term_{t-2})$.<br/>Let $RankRankVol_t = rank_{cs}(rank_{cs}(Volume_t))$.<br/>Let $Denom_t = \frac{Term_t}{VWAP_t - Close_t}$.<br/>$$\alpha_{114} = \frac{R_1 \cdot RankRankVol_t}{Denom_t}$$
factor_54_xs,54,截面+时序,高价排名与成交量Z-score排名相关性负值因子，简化了横截面排名,"Let $RH_t = rank_{cs}(High_t)$.<br/>Let $RMAV_{15,t} = rank_{cs}(MA_{15}(Volume_t))$.<br/>Let $Corr_t = corr(RH_t, RMAV_{15,t}, 9)$.<br/>$$\alpha_{141} = -rank_{cs}(Corr_t)$$"
factor_54,54,简化为时序,高价排名与成交量Z-score排名相关性负值因子，简化了横截面排名,"Let $RH_t = rank_{cs}(High_t)$.<br/>Let $RMAV_{15,t} = rank_{cs}(MA_{15}(Volume_t))$.<br/>Let $Corr_t = corr(RH_t, RMAV_{15,t}, 9)$.<br/>$$\alpha_{141} = -rank_{cs}(Corr_t)$$"
factor_55_xs,55,截面+时序,三重排名乘积因子，简化了横截面排名,"Let $TSR_{C,10,t} = ts\_rank(Close_t, 10, pct=True)$.<br/>Let $R_1 = -rank_{cs}(TSR_{C,10,t})$.<br/><br/>Let $\Delta_1 C_t = Close_t - Close_{t-1}$.<br/>Let $\Delta_1\Delta_1 C_t = \Delta_1 C_t - \Delta_1 C_{t-1} = (Close_t - Close_{t-1}) - (Close_{t-1} - Close_{t-2})$.<br/>Let $R_2 = rank_{cs}(\Delta_1\Delta_1 C_t)$.<br/><br/>Let $VolRatio_t = Volume_t / MA_{20}(Volume_t)$.<br/>Let $TSR_{VR,5,t} = ts\_rank(VolRatio_t, 5, pct=True)$.<br/>Let $R_3 = rank_{cs}(TSR_{VR,5,t})$.<br/>$$\alpha_{142} = R_1 \cdot R_2 \cdot R_3$$"
factor_55,55,简化为时序,三重排名乘积因子，简化了横截面排名,"Let $TSR_{C,10,t} = ts\_rank(Close_t, 10, pct=True)$.<br/>Let $R_1 = -rank_{cs}(TSR_{C,10,t})$.<br/><br/>Let $\Delta_1 C_t = Close_t - Close_{t-1}$.<br/>Let $\Delta_1\Delta_1 C_t = \Delta_1 C_t - \Delta_1 C_{t-1} = (Close_t - Close_{t-1}) - (Close_{t-1} - Close_{t-2})$.<br/>Let $R_2 = rank_{cs}(\Delta_1\Delta_1 C_t)$.<br/><br/>Let $VolRatio_t = Volume_t / MA_{20}(Volume_t)$.<br/>Let $TSR_{VR,5,t} = ts\_rank(VolRatio_t, 5, pct=True)$.<br/>Let $R_3 = rank_{cs}(TSR_{VR,5,t})$.<br/>$$\alpha_{142} = R_1 \cdot R_2 \cdot R_3$$"
factor_56,56,时序,20日价格动量EMA因子,"Let $\Delta_{20} Close_t = Close_t - Close_{t-20}$.<br/>$$\alpha_{151} = EMA(\Delta_{20} Close_t, span=39)$$"
factor_57,57,时序,VWAP条件比较因子,"Let $VWAP_{min16,t} = \min(VWAP_t, 16)$ (16-period rolling min of VWAP).<br/>Let $CorrVWAPVol_t = corr(VWAP_t, MA_{180}(Volume_t), 18)$.<br/>$$\alpha_{154} = I((VWAP_t - VWAP_{min16,t}) < CorrVWAPVol_t)$$<br/>where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false)."
factor_58_xs,58,截面+时序,双重衰减排名最大值因子，简化了横截面排名,"Let $\Delta_5 VWAP_t = VWAP_t - VWAP_{t-5}$.<br/>Let $DL_1 = DecayLinear(\Delta_5 VWAP_t, 3)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $P_{mix,t} = 0.15 \cdot Open_t + 0.85 \cdot Low_t$.<br/>Let $ROC_{Pmix,t} = \frac{P_{mix,t} - P_{mix,t-2}}{P_{mix,t}}$. (Note: Denominator is current $P_{mix,t}$ as per code).<br/>Let $DL_2 = DecayLinear(-ROC_{Pmix,t}, 3)$.<br/>Let $R_2 = rank_{cs}(DL_2)$.<br/>$$\alpha_{156} = -\max(R_1, R_2)$$ (Using `bimax` for NaN-safe max)."
factor_58,58,简化为时序,双重衰减排名最大值因子，简化了横截面排名,"Let $\Delta_5 VWAP_t = VWAP_t - VWAP_{t-5}$.<br/>Let $DL_1 = DecayLinear(\Delta_5 VWAP_t, 3)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $P_{mix,t} = 0.15 \cdot Open_t + 0.85 \cdot Low_t$.<br/>Let $ROC_{Pmix,t} = \frac{P_{mix,t} - P_{mix,t-2}}{P_{mix,t}}$. (Note: Denominator is current $P_{mix,t}$ as per code).<br/>Let $DL_2 = DecayLinear(-ROC_{Pmix,t}, 3)$.<br/>Let $R_2 = rank_{cs}(DL_2)$.<br/>$$\alpha_{156} = -\max(R_1, R_2)$$ (Using `bimax` for NaN-safe max)."
factor_61,61,时序,价格排名与成交量排名最大值因子,"Let $R_V = rank_{cs}(Volume_t)$.<br/>Let $R_{VWAP} = rank_{cs}(VWAP_t)$.<br/>$$\alpha_{16} = - \max(rank_{cs}(\text{corr}(R_V, R_{VWAP}, 5)), 5)$$"
factor_62,62,时序,高价与成交量相关性排名负值因子,"Let $Cond_t = (MA_{20}(Volume_t) < Volume_t)$.<br/>Let $\Delta_7 C_t = Close_t - Close_{t-7}$.<br/>Let $TermA_t = -ts\_rank(|\Delta_7 C_t|, 60, pct=True) \cdot sign(\Delta_7 C_t)$.<br/>Let $TermB_t = -Volume_t$.<br/>$$\alpha_{180} = \begin{cases} TermA_t & \text{if } Cond_t \text{ is true} \\ TermB_t & \text{if } Cond_t \text{ is false} \end{cases}$$"
factor_64,64,时序,价格变化排名因子,"令 $Range_t = High_t - Low_t$。<br/>令 $EWMA(Range, N)_{t}$ 为 $Range_t$ 的 $N$周期指数加权移动平均。<br/>$$\alpha_{188,t} = \frac{Range_t - EWMA(Range, 10)_t}{EWMA(Range, 10)_t} \times 100$$"
factor_65,65,时序,收盘价标准差排名因子,"令 $Volume_t$ 为 $t$ 时刻的成交量，$Low_t$ 为最低价，$High_t$ 为最高价，$Close_t$ 为收盘价。<br/>令 $SMA(Volume, 20)_t$ 为 $Volume_t$ 的20周期简单移动平均。<br/>令 $Corr(X, Y, N)_t$ 为时间序列 $X$ 和 $Y$ 在过去 $N$ 个周期的皮尔逊相关系数。<br/>令 $MidPoint_t = (High_t + Low_t) / 2$。<br/>$$\alpha_{191,t} = Corr(SMA(Volume, 20), Low, 5)_t + MidPoint_t - Close_t$$"
factor_73,73,时序,线性衰减价格排名因子,"Let $Diff_{CO,t} = Close_t - Open_t$.<br/>Let $AbsDiff_{CO,t} = |Close_t - Open_t|$.<br/>Let $StdGlobalAbsDiff_{CO}$ be the standard deviation of $AbsDiff_{CO,t}$ for each stock over its entire available history up to time $t$. (This interpretation matches `part1.std()` in pandas for a column).<br/>Let $CorrCO_{10,t} = corr(Close_t, Open_t, 10)$.<br/>Let $SumTerms_t = StdGlobalAbsDiff_{CO} + Diff_{CO,t} + CorrCO_{10,t}$. (Note: $StdGlobalAbsDiff_{CO}$ is a single value per stock per day if calculated as expanding std, or a constant if std over all time for a stock, or a series if std over all stocks on a given day. The code `part1=(self.close-self.open_price).abs(); part1=part1.std()` calculates std over time for each stock for the whole dataset. This means `part1` is a Series indexed by stock ID. When added to DataFrames `part2` and `part3`, it broadcasts. The ranking is then cross-sectional.)<br/>So, $StdCol(X)$ refers to the standard deviation of X for that stock over the entire period available in the DataFrame construction.<br/>Let $StdColAbsDiff_{CO, stock\_i} = std( |Close_{s, stock\_i} - Open_{s, stock\_i}| \text{ for all } s \text{ in history})$.<br/>Then for a given day $t$, $SumTerms_{t, stock\_i} = StdColAbsDiff_{CO, stock\_i} + (Close_{t, stock\_i} - Open_{t, stock\_i}) + corr(Close_{s, stock\_i}, Open_{s, stock\_i}, 10)_t$.<br/>$$\alpha_{54} = -rank_{cs}(SumTerms_t)$$"
factor_74,74,时序,收盘价与成交量相关性因子,"Let $P_{mix,t} = 0.35 \cdot Low_t + 0.65 \cdot VWAP_t$.<br/>Let $SumP_{mix,20,t} = \sum_{i=0}^{19} P_{mix,t-i}$.<br/>Let $MAVol_{40,t} = MA_{40}(Volume_t)$.<br/>Let $SumMAVol_{20,t} = \sum_{i=0}^{19} MAVol_{40,t-i}$.<br/>Let $Corr1_t = corr(SumP_{mix,20,t}, SumMAVol_{20,t}, 7)$.<br/>Let $R_1 = rank_{cs}(Corr1_t)$.<br/><br/>Let $R_{VWAP,t} = rank_{cs}(VWAP_t)$.<br/>Let $R_{Vol,t} = rank_{cs}(Volume_t)$.<br/>Let $Corr2_t = corr(R_{VWAP,t}, R_{Vol,t}, 6)$.<br/>Let $R_2 = rank_{cs}(Corr2_t)$.<br/>$$\alpha_{74} = R_1 + R_2$$"
factor_77,77,时序,价格最小值与成交量最大值因子,"Let $R_{VWAP,t} = rank_{cs}(VWAP_t)$.<br/>Let $R_{Vol,t} = rank_{cs}(Volume_t)$.<br/>Let $Corr_t = corr(R_{VWAP,t}, R_{Vol,t}, 5)$.<br/>$$\alpha_{90} = -rank_{cs}(Corr_t)$$"
factor_78,78,时序,高价与低价差值排名因子,"Let $Term1_t = Close_t - \max(Close_t, 5.0 \text{ [scalar]})$. (This term is $\le 0$).<br/>Let $R_1 = rank_{cs}(Term1_t)$.<br/><br/>Let $MAVol_{40,t} = MA_{40}(Volume_t)$.<br/>Let $CorrLV_t = corr(MAVol_{40,t}, Low_t, 5)$.<br/>Let $R_2 = rank_{cs}(CorrLV_t)$.<br/>$$\alpha_{91} = -(R_1 \cdot R_2)$$"
factor_79,79,时序,收盘价标准差因子,"Let $P_{mix,t} = 0.35 \cdot Close_t + 0.65 \cdot VWAP_t$.<br/>Let $\Delta_2 P_{mix,t} = P_{mix,t} - P_{mix,t-2}$.<br/>Let $DL_1 = DecayLinear(\Delta_2 P_{mix,t}, 3)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $CorrVC_t = corr(MA_{180}(Volume_t), Close_t, 13)$.<br/>Let $DL_2 = DecayLinear(|CorrVC_t|, 5)$.<br/>Let $R_2 = ts\_rank(DL_2, 15, pct=True)$.<br/>$$\alpha_{92} = -\max(R_1, R_2)$$"
factor_81_xs,81,截面+时序,收盘价排名与成交量Z-score排名协方差负值因子，简化了横截面排名,"Let $R_{C,t} = rank_{cs}(Close_t)$.<br/>Let $R_{V,t} = rank_{cs}(Volume_t)$.<br/>Let $Cov_{RCRV,t} = cov(R_{C,t}, R_{V,t}, 5)$ (5-period rolling covariance).<br/>$$\alpha_{99} = -rank_{cs}(Cov_{RCRV,t})$$"
factor_81,81,简化为时序,收盘价排名与成交量Z-score排名协方差负值因子，简化了横截面排名,"Let $R_{C,t} = rank_{cs}(Close_t)$.<br/>Let $R_{V,t} = rank_{cs}(Volume_t)$.<br/>Let $Cov_{RCRV,t} = cov(R_{C,t}, R_{V,t}, 5)$ (5-period rolling covariance).<br/>$$\alpha_{99} = -rank_{cs}(Cov_{RCRV,t})$$"
factor_82,82,时序,最低价位置LOWDAY指标因子,"Let $DaysSinceLow_{20,t} = \text{lowday}(Low_t, 20)$, representing the number of days from the end of the 20-day window to (and including) the day the minimum low occurred. If min is today, $DaysSinceLow_{20,t}=1$. If min was 19 days ago (first day of window), $DaysSinceLow_{20,t}=20$.<br/>$$\alpha_{103} = \frac{20 - DaysSinceLow_{20,t}}{20} \times 100$$"
factor_83,83,简化为时序,高价与成交量相关性差分与收盘价标准差排名乘积负值因子，简化了横截面排名,"Let $CorrHV_t = corr(High_t, Volume_t, 5)$.<br/>Let $\Delta_5 CorrHV_t = CorrHV_t - CorrHV_{t-5}$.<br/>Let $StdC_{20,t} = std(Close_t, 20)$.<br/>Let $RankStdC_{20,t} = rank_{cs}(StdC_{20,t})$.<br/>$$\alpha_{104} = -(\Delta_5 CorrHV_t \cdot RankStdC_{20,t})$$"
factor_83_xs,83,截面+时序,高价与成交量相关性差分与收盘价标准差排名乘积负值因子，简化了横截面排名,"Let $CorrHV_t = corr(High_t, Volume_t, 5)$.<br/>Let $\Delta_5 CorrHV_t = CorrHV_t - CorrHV_{t-5}$.<br/>Let $StdC_{20,t} = std(Close_t, 20)$.<br/>Let $RankStdC_{20,t} = rank_{cs}(StdC_{20,t})$.<br/>$$\alpha_{104} = -(\Delta_5 CorrHV_t \cdot RankStdC_{20,t})$$"
factor_85,85,时序,成交量与价格相关性因子,"Let $MFM_t = \frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t}$ (Money Flow Multiplier).<br/>Let $MFV_t = MFM_t \cdot Volume_t$ (Money Flow Volume).<br/><br/>Let $EMA_{short}(X) = EMA(X, span=3)$ (corresponds to SMA(X,4,2) -> $\alpha=2/4$, span=$2/(2/4)-1=3$).<br/>Let $EMA_{long}(X) = EMA(X, span=10)$ (corresponds to SMA(X,11,2) -> $\alpha=2/11$, span=$2/(2/11)-1=10$).<br/>$$\alpha_{111} = EMA_{long}(MFV_t) - EMA_{short}(MFV_t)$$"
factor_86,86,时序,收盘价延迟差分因子,$$X_t = \frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t} \cdot Volume_t$$<br/>$$\alpha_{11} = \sum_{i=0}^{5} X_{t-i}$$<br/>(Code: `result.rolling(6).sum()`)
factor_90,90,时序,VWAP与成交量相关性排名因子,Let $UpShadow_t = High_t - Open_t$.<br/>Let $LowShadow_t = Open_t - Low_t$.<br/>$$\alpha_{118} = \frac{\sum_{i=0}^{19} UpShadow_{t-i}}{\sum_{i=0}^{19} LowShadow_{t-i}} \times 100$$
factor_92,92,时序,价格最大值与最小值因子,"Let $Mid_t = (High_t + Low_t)/2$.<br/>Let $SumMid_{20,t} = \sum_{i=0}^{19} Mid_{t-i}$.<br/>Let $SumMAVol_{20,t} = \sum_{i=0}^{19} (MA_{60}(Volume_{t-i}))$.<br/>Let $Corr_1 = corr(SumMid_{20,t}, SumMAVol_{20,t}, 9)$.<br/>Let $R_1 = rank_{cs}(Corr_1)$.<br/><br/>Let $Corr_2 = corr(Low_t, Volume_t, 6)$.<br/>Let $R_2 = rank_{cs}(Corr_2)$.<br/>$$\alpha_{123} = -I(R_1 < R_2)$$<br/>where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false)."
factor_93,93,时序,开盘价与成交量相关性因子,"Let $MA(VWAP_t, 10)$ be the 10-period moving average of VWAP.<br/>$$P_1 = rank_{cs}(Open_t - MA(VWAP_t, 10))$$<br/>$$P_2 = -rank_{cs}(|Close_t - VWAP_t|)$$<br/>$$\alpha_{12} = P_1 \cdot P_2$$"
factor_96,96,时序,价格变化标准差因子,"Let $Mid_t = (High_t + Low_t)/2$.<br/>Let $Corr_1 = corr(Mid_t, MA_{40}(Volume_t), 9)$.<br/>Let $DL_1 = DecayLinear(Corr_1, 10)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $RankVWAP_t = rank_{cs}(VWAP_t)$.<br/>Let $RankVol_t = rank_{cs}(Volume_t)$.<br/>Let $Corr_2 = corr(RankVWAP_t, RankVol_t, 7)$.<br/>Let $DL_2 = DecayLinear(Corr_2, 3)$.<br/>Let $R_2 = rank_{cs}(DL_2)$.<br/>$$\alpha_{130} = \frac{R_1}{R_2}$$"
factor_98,98,时序,ADV与价格相关性因子,"Let $DaysSinceHigh_{20,t} = \text{highday}(High_t, 20)$. (Number of days from window end to max high; 1 if high is today, 20 if high was 19 days ago).<br/>Let $DaysSinceLow_{20,t} = \text{lowday}(Low_t, 20)$. (Number of days from window end to min low; 1 if low is today, 20 if low was 19 days ago).<br/>Let $AroonUp_t = \frac{20 - DaysSinceHigh_{20,t}}{20} \times 100$.<br/>Let $AroonDown_t = \frac{20 - DaysSinceLow_{20,t}}{20} \times 100$.<br/>$$\alpha_{133} = AroonUp_t - AroonDown_t$$"
factor_100,100,时序,成交量标准差因子,"Let $RelStr_{20,t} = \frac{Close_t}{Close_{t-20}}$.<br/>$$\alpha_{135} = EMA(RelStr_{20,t-1}, span=39)$$"
factor_101,101,时序,价格移动和波动幅度复合因子,"Let $C_t, O_t, H_t, L_t$ be Close, Open, High, Low at time $t$.<br/>Let $C_{t-1}, O_{t-1}, L_{t-1}$ be their values at $t-1$.<br/>Numerator term: $N_t = 16 \times (C_t - C_{t-1} + \frac{C_t - O_t}{2} + C_{t-1} - O_{t-1}) \times \max(|H_t - C_{t-1}|, |L_t - C_{t-1}|)$.<br/>Denominator term $D_t$:<br/>Let $TermA = |H_t - C_{t-1}|$.<br/>Let $TermB = |L_t - C_{t-1}|$.<br/>Let $TermC = |H_t - L_{t-1}|$.<br/>Let $TermD = |C_{t-1} - O_{t-1}|$.<br/>If $TermA > TermB$ AND $TermA > TermC$:<br/>  $D_t = TermA + TermB/2 + TermD/4$.<br/>Else if $TermB > TermC$ AND $TermB > TermA$:<br/>  $D_t = TermB + TermA/2 + TermD/4$.<br/>Else:<br/>  $D_t = TermC + TermD/4$.<br/><br/>Let $X_t = \frac{N_t}{D_t}$.<br/>$$\alpha_{137} = X_t$$<br/>(Note: Alpha 55 sums this $X_t$ over 20 periods. Alpha 137 in the code does *not* sum, it returns the daily $X_t$. This makes it different from Alpha 55 despite the core term being the same)."
factor_102,102,简化为时序,多价格Z-score和相关性复合因子，简化了复杂逻辑,"Let $Term_1 = rank_{cs}(Open_t) + rank_{cs}(Low_t) - rank_{cs}(High_t) - rank_{cs}(Close_t)$.<br/>Let $DL_1 = DecayLinear(Term_1, 8)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $TSR_{C,8,t} = ts\_rank(Close_t, 8, pct=True)$.<br/>Let $TSR_{MAV,20,t} = ts\_rank(MA_{60}(Volume_t), 20, pct=True)$.<br/>Let $Corr_2 = corr(TSR_{C,8,t}, TSR_{MAV,20,t}, 8)$.<br/>Let $DL_2 = DecayLinear(Corr_2, 7)$.<br/>Let $R_2 = ts\_rank(DL_2, 3, pct=True)$.<br/>$$\alpha_{140} = \min(R_1, R_2)$$"
factor_102_xs,102,截面+时序,多价格Z-score和相关性复合因子，简化了复杂逻辑,"Let $Term_1 = rank_{cs}(Open_t) + rank_{cs}(Low_t) - rank_{cs}(High_t) - rank_{cs}(Close_t)$.<br/>Let $DL_1 = DecayLinear(Term_1, 8)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $TSR_{C,8,t} = ts\_rank(Close_t, 8, pct=True)$.<br/>Let $TSR_{MAV,20,t} = ts\_rank(MA_{60}(Volume_t), 20, pct=True)$.<br/>Let $Corr_2 = corr(TSR_{C,8,t}, TSR_{MAV,20,t}, 8)$.<br/>Let $DL_2 = DecayLinear(Corr_2, 7)$.<br/>Let $R_2 = ts\_rank(DL_2, 3, pct=True)$.<br/>$$\alpha_{140} = \min(R_1, R_2)$$"
factor_103,103,时序,低价标准差因子,"Let $Ret_t = (Close_t / Close_{t-1}) - 1$.<br/>Let $Term_t = \begin{cases} Ret_t & \text{if } Close_t > Close_{t-1} \\ 1 & \text{otherwise} \end{cases}$<br/>$$\alpha_{143,t} = \prod_{k=start\_date}^{t} Term_k$$<br/>(Assuming $\alpha_{143}$ is initialized appropriately or the product starts from the beginning of the series)."
factor_104,104,时序,高价与成交量相关性负值因子,Let $Ret_t = (Close_t/Close_{t-1}) - 1$.<br/>Let $Term_t = \frac{|Ret_t|}{Amount_t}$.<br/>Let $Cond_t = (Close_t < Close_{t-1})$.<br/>Let $Numerator_t = \sum_{i=0}^{19} (Term_{t-i} \cdot I(Cond_{t-i}))$.<br/>Let $Denominator_t = \sum_{i=0}^{19} I(Cond_{t-i})$.<br/>$$\alpha_{144} = \frac{Numerator_t}{Denominator_t}$$<br/>where $I(\cdot)$ is the Iverson bracket.
factor_105,105,时序,开盘价与成交量Z-score排名相关性负值因子,"$$\alpha_{14} = Close_t - Close_{t-5}$$<br/>(This is $\Delta(Close_t, 5)$)"
factor_106,106,时序,收盘价1日差分因子,"Let $Ret_t = (Close_t / Close_{t-1}) - 1$.<br/>Let $EMA_{Ret,60,t} = EMA(Ret_t, span=60)$.<br/>Let $Dev_t = Ret_t - EMA_{Ret,60,t}$. (Deviation of return from its EMA)<br/>Let $MA_{Dev,20,t} = MA(Dev_t, 20)$.<br/>Let $DenomTerm_t = EMA((EMA_{Ret,60,t})^2, span=60)$.<br/>$$\alpha_{146} = \frac{MA_{Dev,20,t} \cdot Dev_t}{DenomTerm_t}$$"
factor_107,107,时序,开盘价与高价差值排名负值因子,"Let $MA_{12}(Close_t)$ be the 12-period simple moving average of $Close_t$.<br/>Let $Y_k = MA_{12}(Close_{t-12+k})$ for $k=1, \dots, 12$.<br/>Let $T = (1, 2, \dots, 12)$.<br/>$$\alpha_{147,t} = Slope(Y \text{ vs } T)$$<br/>This means for each day $t$, $\alpha_{147,t}$ is the slope of the linear regression of the past 12 values of $MA_{12}(Close)$ against the sequence $(1,2,\dots,12)$."
factor_108,108,时序,高价与低价差值排名因子,"Let $SumMAVol_t = \sum_{k=0}^{8} (MA_{60}(Volume_{t-k}))$.<br/>Let $Corr_1 = corr(Open_t, SumMAVol_t, 6)$.<br/>Let $R_1 = rank_{cs}(Corr_1)$.<br/><br/>Let $MinO_{14,t} = \min(Open_t, 14)$ (14-period rolling min of Open).<br/>Let $R_2 = rank_{cs}(Open_t - MinO_{14,t})$.<br/>$$\alpha_{148} = -I(R_1 < R_2)$$<br/>where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false)."
factor_109,109,时序,收盘价与开盘价差值标准差因子,Let $TP_t = (Close_t + High_t + Low_t) / 3$.<br/>$$\alpha_{150} = TP_t \cdot Volume_t$$
factor_110,110,时序,收盘价与低价差值最大值因子,"Let $RelStr9_t = Close_t / Close_{t-9}$.<br/>Let $LagRS_t = RelStr9_{t-1}$.<br/>Let $EMA1_{LRS,t} = EMA(LagRS_t, span=17)$ (SMA(X,9,1) -> span 17).<br/>Let $LagEMA1_{LRS,t} = EMA1_{LRS, t-1}$.<br/>Let $MA12_{LagEMA1,t} = MA(LagEMA1_{LRS,t}, 12)$.<br/>Let $MA26_{LagEMA1,t} = MA(LagEMA1_{LRS,t}, 26)$.<br/>Let $DiffMA_t = MA12_{LagEMA1,t} - MA26_{LagEMA1,t}$.<br/>$$\alpha_{152} = EMA(DiffMA_t, span=17)$$ (SMA(X,9,1) -> span 17 for outer smoothing)"
factor_111,111,时序,四个不同周期均线平均值因子,$$\alpha_{153} = \frac{MA_3(Close_t) + MA_6(Close_t) + MA_{12}(Close_t) + MA_{24}(Close_t)}{4}$$
factor_112,112,时序,Alpha15隔夜跳空因子,$$\alpha_{15} = \frac{Open_t}{Close_{t-1}} - 1$$
factor_113,113,时序,Alpha155成交量MACD因子,"Let $EMA_{V,short} = EMA(Volume_t, span=12)$ (corresponds to SMA(Volume,13,2)).<br/>Let $EMA_{V,long} = EMA(Volume_t, span=26)$ (corresponds to SMA(Volume,27,2)).<br/>Let $MACD_{Vol,t} = EMA_{V,short} - EMA_{V,long}$.<br/>Let $Signal_{Vol,t} = EMA(MACD_{Vol,t}, span=9)$ (corresponds to SMA(MACD_Vol,10,2)).<br/>$$\alpha_{155} = MACD_{Vol,t} - Signal_{Vol,t}$$"
factor_114,114,基础字段,Alpha158价格振幅与收盘价比率因子,$$\alpha_{158} = \frac{High_t - Low_t}{Close_t}$$
factor_115,115,时序,Alpha157基于修正价格范围的加权指标,"Let $L'_t = \min(Low_t, Close_{t-1})$.<br/>Let $H'_{Market,t} = \max(High_t, Close_{t-1})$. (Note: code uses $H'_{Market,t}$ only for the sum range $H'_{Market,t} - L'_t$. The first term of each component uses $L'_t$ as defined.)<br/>Let $Range'_t = H'_{Market,t} - L'_t$.<br/><br/>Let $Term(N) = \frac{Close_t - \sum_{i=0}^{N-1} L'_{t-i}}{\sum_{i=0}^{N-1} Range'_{t-i}}$.<br/>Let $X = Term(6) \cdot 12 \cdot 24$.<br/>Let $Y = Term(12) \cdot 6 \cdot 24$.<br/>Let $Z = Term(24) \cdot 6 \cdot 24$.<br/>Let $TotalWeight = (6 \cdot 12) + (6 \cdot 24) + (12 \cdot 24) = 72 + 144 + 288 = 504$.<br/>$$\alpha_{159} = \frac{X + Y + Z}{TotalWeight} \times 100$$"
factor_116,116,时序,Alpha160条件波动率EMA因子,"Let $CondStd_t = std(Close_t, 20) \cdot I(Close_t \le Close_{t-1})$.<br/>(If $Close_t > Close_{t-1}$, then $CondStd_t = 0$)<br/>$$\alpha_{160} = EMA(CondStd_t, span=39)$$"
factor_117,117,简化为时序,收益率、成交量和价格复合指标，简化了横截面排名,Let $Term_t = (-Ret_t) \cdot MA_{20}(Volume_t) \cdot VWAP_t \cdot (High_t - Close_t)$.<br/>$$\alpha_{163} = rank_{cs}(Term_t)$$
factor_117_xs,117,截面+时序,收益率、成交量和价格复合指标，简化了横截面排名,Let $Term_t = (-Ret_t) \cdot MA_{20}(Volume_t) \cdot VWAP_t \cdot (High_t - Close_t)$.<br/>$$\alpha_{163} = rank_{cs}(Term_t)$$
factor_118,118,时序,Alpha166收益率分布偏度近似因子,"Let $Ret_t = (Close_t/Close_{t-1}) - 1$.<br/>Let $MARet_{20,t} = MA(Ret_t, 20)$.<br/>Let $SumDevRet_t = \sum_{i=0}^{19} (Ret_{t-i} - MARet_{20,t-i})$. (Note: code seems to use $MA(Ret_t,20)$ at time $t$ for all terms in sum).<br/>Let $SumSqRelStr_t = \sum_{i=0}^{19} ((Ret_{t-i}+1)^2)$.<br/><br/>Let $Num_t = (-20) \cdot (19^{1.5}) \cdot SumDevRet_t$.<br/>Let $Denom_t = (19 \cdot 18) \cdot (SumSqRelStr_t)^{1.5}$.<br/>$$\alpha_{166} = \frac{Num_t}{Denom_t}$$<br/>(Code for $SumDevRet_t$: `(self.pct_chg - self.pct_chg.rolling(20).mean()).rolling(20).sum()`. This sums $(Ret_k - MA(Ret_k,20))$ over the window $k \in [t-19, t]$)."
factor_119,119,时序,Alpha167累积上涨幅度因子,"Let $UpMove_t = (Close_t - Close_{t-1}) \cdot I(Close_t > Close_{t-1})$. (If $Close_t \le Close_{t-1}$, then 0)<br/>$$\alpha_{167} = \sum_{i=0}^{11} UpMove_{t-i}$$"
factor_120,120,时序,简单因子：-volume/移动平均volume,$$\alpha_{168} = - \frac{Volume_t}{MA_{20}(Volume_t)}$$
factor_122,122,简化为时序,VWAP、成交量和高价复合因子，简化了横截面排名,"Let $R_{1/C,t} = rank_{cs}(1/Close_t)$.<br/>Let $TermA_t = \frac{R_{1/C,t} \cdot Volume_t}{MA_{20}(Volume_t)}$.<br/><br/>Let $R_{HC,t} = rank_{cs}(High_t - Close_t)$. (Upper shadow component)<br/>Let $TermB_t = \frac{High_t \cdot R_{HC,t}}{MA_5(High_t)}$.<br/><br/>Let $TermC_t = rank_{cs}(VWAP_t - VWAP_{t-5})$.<br/>$$\alpha_{170} = TermA_t \cdot TermB_t - TermC_t$$"
factor_122_xs,122,截面+时序,VWAP、成交量和高价复合因子，简化了横截面排名,"Let $R_{1/C,t} = rank_{cs}(1/Close_t)$.<br/>Let $TermA_t = \frac{R_{1/C,t} \cdot Volume_t}{MA_{20}(Volume_t)}$.<br/><br/>Let $R_{HC,t} = rank_{cs}(High_t - Close_t)$. (Upper shadow component)<br/>Let $TermB_t = \frac{High_t \cdot R_{HC,t}}{MA_5(High_t)}$.<br/><br/>Let $TermC_t = rank_{cs}(VWAP_t - VWAP_{t-5})$.<br/>$$\alpha_{170} = TermA_t \cdot TermB_t - TermC_t$$"
factor_123,123,截面,Alpha171价格相对位置与动能比率因子,$$\alpha_{171} = \frac{-(Low_t - Close_t) \cdot (Open_t^5)}{(Close_t - High_t) \cdot (Close_t^5)} = \frac{(Close_t - Low_t) \cdot Open_t^5}{(High_t - Close_t) \cdot Close_t^5}$$
factor_124,124,时序,ADX指标技术分析因子,"Let $LD_t = Low_{t-1} - Low_t$. (Lagged Low Diff)<br/>Let $HD_t = High_t - High_{t-1}$. (High Diff)<br/>Let $TR_t = \max(High_t - Low_t, |High_t - Close_{t-1}|, |Low_t - Close_{t-1}|)$ (True Range).<br/><br/>Let $+DM_t = LD_t \cdot I(LD_t > 0 \land LD_t > HD_t)$.<br/>Let $-DM_t = HD_t \cdot I(HD_t > 0 \land HD_t > LD_t)$.<br/>(Note: This definition of +DM and -DM is specific to this alpha. Standard DMI uses $H_t - H_{t-1}$ for +DM ideas and $L_{t-1} - L_t$ for -DM ideas.)<br/><br/>Let $S_{+DM14} = \sum_{i=0}^{13} +DM_{t-i}$.<br/>Let $S_{-DM14} = \sum_{i=0}^{13} -DM_{t-i}$.<br/>Let $S_{TR14} = \sum_{i=0}^{13} TR_{t-i}$.<br/><br/>Let $+DI_{14,t} = (S_{+DM14} / S_{TR14}) \times 100$.<br/>Let $-DI_{14,t} = (S_{-DM14} / S_{TR14}) \times 100$.<br/>(Handle $S_{TR14}=0$ case).<br/><br/>Let $DX_t = \frac{|+DI_{14,t} - (-DI_{14,t})|}{|+DI_{14,t} + (-DI_{14,t})|} \times 100$. (Note: the formula in comment seems to use $(-DI_{14})$ as defined from HD. If $-DI_{14}$ is positive, then this is $|+DI - (-DI)| / (|+DI + (-DI)|)$ which is $|+DI+DI| / (|+DI-DI|)$ if DI are of opposite conceptual sign. The Python code calculates `part1` and `part2` which correspond to $+DI_{14}$ and $-DI_{14}$ respectively using its definition. Then `result1 = (part1 - part2).abs()`, `result2 = part1 + part2`. So $DX_t = \frac{|+DI_{14} - (-DI_{14})|}{+DI_{14} + (-DI_{14})} \times 100$ assuming DIs are positive.)<br/>Let $Term_t = \frac{|+DI_{14,t} - (-DI_{14,t})|}{+DI_{14,t} + (-DI_{14,t})} \times 100$. (Handle denominator = 0)<br/>$$\alpha_{172} = MA(Term_t, 6)$$"
factor_125,125,时序,DEMA和TEMA组合Z-score标准化因子,"Let $EMA1_C = EMA(Close_t, span=12)$.<br/>Let $EMA2_C = EMA(EMA1_C, span=12)$.<br/><br/>Let $LNC_t = \ln(Close_t)$.<br/>Let $EMA1_{LNC} = EMA(LNC_t, span=12)$.<br/>Let $EMA2_{LNC} = EMA(EMA1_{LNC}, span=12)$.<br/>Let $EMA3_{LNC} = EMA(EMA2_{LNC}, span=12)$.<br/>$$\alpha_{173} = 3 \cdot EMA1_C - 2 \cdot EMA2_C + EMA3_{LNC}$$"
factor_128_xs,128,截面+时序,随机震荡值排名与成交量排名相关性因子，简化了横截面排名,"Let $L_{12,t} = \min(Low_t, 12)$ (12-period rolling min of Low).<br/>Let $H_{12,t} = \max(High_t, 12)$ (12-period rolling max of High).<br/>Let $StochK_{12,t} = \frac{Close_t - L_{12,t}}{H_{12,t} - L_{12,t}}$. (Stochastic %K value, normalized to 0-1 if $L_{12} \le C_t \le H_{12}$)<br/>Let $R_K = rank_{cs}(StochK_{12,t})$.<br/>Let $R_V = rank_{cs}(Volume_t)$.<br/>$$\alpha_{176} = corr(R_K, R_V, 6)$$"
factor_128,128,简化为时序,随机震荡值排名与成交量排名相关性因子，简化了横截面排名,"Let $L_{12,t} = \min(Low_t, 12)$ (12-period rolling min of Low).<br/>Let $H_{12,t} = \max(High_t, 12)$ (12-period rolling max of High).<br/>Let $StochK_{12,t} = \frac{Close_t - L_{12,t}}{H_{12,t} - L_{12,t}}$. (Stochastic %K value, normalized to 0-1 if $L_{12} \le C_t \le H_{12}$)<br/>Let $R_K = rank_{cs}(StochK_{12,t})$.<br/>Let $R_V = rank_{cs}(Volume_t)$.<br/>$$\alpha_{176} = corr(R_K, R_V, 6)$$"
factor_129,129,时序,Alpha178成交量加权1日价格变化率因子,"Let $ROC_{1,t} = \frac{Close_t - Close_{t-1}}{Close_{t-1}}$.<br/>$$\alpha_{178} = ROC_{1,t} \cdot Volume_t$$"
factor_130_xs,130,截面+时序,VWAP、成交量相关性和价格低点复合因子，简化了横截面排名,"Let $CorrVWAPVol_t = corr(VWAP_t, Volume_t, 4)$.<br/>Let $R_1 = rank_{cs}(CorrVWAPVol_t)$.<br/><br/>Let $RankLow_t = rank_{cs}(Low_t)$.<br/>Let $RankMAVol50_t = rank_{cs}(MA_{50}(Volume_t))$.<br/>Let $CorrLowMAVol_t = corr(RankLow_t, RankMAVol50_t, 12)$.<br/>Let $R_2 = rank_{cs}(CorrLowMAVol_t)$.<br/>$$\alpha_{179} = R_1 \cdot R_2$$"
factor_130,130,简化为时序,VWAP、成交量相关性和价格低点复合因子，简化了横截面排名,"Let $CorrVWAPVol_t = corr(VWAP_t, Volume_t, 4)$.<br/>Let $R_1 = rank_{cs}(CorrVWAPVol_t)$.<br/><br/>Let $RankLow_t = rank_{cs}(Low_t)$.<br/>Let $RankMAVol50_t = rank_{cs}(MA_{50}(Volume_t))$.<br/>Let $CorrLowMAVol_t = corr(RankLow_t, RankMAVol50_t, 12)$.<br/>Let $R_2 = rank_{cs}(CorrLowMAVol_t)$.<br/>$$\alpha_{179} = R_1 \cdot R_2$$"
factor_131_xs,131,截面+时序,Alpha185开盘收盘价比率平方负排名因子，简化了横截面排名,$$\alpha_{185} = rank_{cs}\left(-\left(1 - \frac{Open_t}{Close_t}\right)^2\right)$$
factor_131,131,简化为时序,Alpha185开盘收盘价比率平方负排名因子，简化了横截面排名,$$\alpha_{185} = rank_{cs}\left(-\left(1 - \frac{Open_t}{Close_t}\right)^2\right)$$
factor_132,132,时序,Alpha189基于收盘价SMA绝对偏差因子,"令 $Close_t$ 为 $t$ 时刻的收盘价。<br/>令 $SMA(Close, N)_t$ 为 $Close_t$ 的 $N$周期简单移动平均。<br/>$$\alpha_{189,t} = SMA( |Close_t - SMA(Close, 6)_t|, 6 )_t$$"
factor_133,133,简化为时序,成交量变化排名与收益率排名滚动相关性因子，简化了横截面排名,"$$\alpha_1 = - \text{corr}(rank_{cs}(\Delta(\ln(Volume_t), 1)), rank_{cs}\left(\frac{Close_t - Open_t}{Open_t}\right), 6)$$"
factor_133_xs,133,截面+时序,成交量变化排名与收益率排名滚动相关性因子，简化了横截面排名,"$$\alpha_1 = - \text{corr}(rank_{cs}(\Delta(\ln(Volume_t), 1)), rank_{cs}\left(\frac{Close_t - Open_t}{Open_t}\right), 6)$$"
factor_134,134,时序,Alpha23条件波动率强度因子,"Let $Std_{20}(Close_t)$ be the 20-period rolling standard deviation of $Close_t$.<br/>Let $UpCond_t = I(Close_t > Close_{t-1})$, where $I(\cdot)$ is the Iverson bracket.<br/>Let $VolUp_t = Std_{20}(Close_t) \text{ if } UpCond_t \text{ else } 0$.<br/>Let $VolDown_t = Std_{20}(Close_t) \text{ if } \neg UpCond_t \text{ else } 0$.<br/>$$EMA_{Up} = EMA(VolUp_t, span=39)$$$$EMA_{Down} = EMA(VolDown_t, span=39)$$$$\alpha_{23} = \frac{EMA_{Up}}{EMA_{Up} + EMA_{Down}} \times 100$$"
factor_135_xs,135,截面+时序,Alpha25价格动量、成交量相对强度和长期收益表现组合，简化了横截面排名,"Let $R_1 = rank_{cs}(\Delta(Close_t, 7))$.<br/>Let $V'_t = \frac{Volume_t}{MA_{20}(Volume_t)}$.<br/>Let $R_2 = 1 - rank_{cs}(DecayLinear(V'_t, 9))$.<br/>Let $R_3 = 1 + rank_{cs}(\sum_{i=0}^{249} Ret_{t-i})$.<br/>$$\alpha_{25} = -R_1 \cdot R_2 \cdot R_3$$"
factor_135,135,简化为时序,Alpha25价格动量、成交量相对强度和长期收益表现组合，简化了横截面排名,"Let $R_1 = rank_{cs}(\Delta(Close_t, 7))$.<br/>Let $V'_t = \frac{Volume_t}{MA_{20}(Volume_t)}$.<br/>Let $R_2 = 1 - rank_{cs}(DecayLinear(V'_t, 9))$.<br/>Let $R_3 = 1 + rank_{cs}(\sum_{i=0}^{249} Ret_{t-i})$.<br/>$$\alpha_{25} = -R_1 \cdot R_2 \cdot R_3$$"
factor_138,138,时序,Alpha29成交量加权价格变化率因子,$$\alpha_{29} = \left(\frac{Close_t - Close_{t-6}}{Close_{t-6}}\right) \cdot Volume_t$$
factor_140,140,时序,Alpha2日内价格振幅结构因子,"$$\alpha_2 = - \Delta\left(\frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t}, 1\right)$$<br/>$$\text{Note: The formula from the code is } \alpha_2 = -\frac{((Close_t-Low_t)-(High_t-Close_t))}{(High_t-Low_t)}.\text{diff()}$$<br/>The `.diff()` in pandas applied to the whole expression acts like a time-series difference $\Delta(X,1)$."
factor_142_xs,142,截面+时序,开盘价变化和开盘价-成交量相关性复合因子，简化了横截面排名,"Let $\Delta Open_t = Open_t - Open_{t-1}$.<br/>Let $DL1 = DecayLinear(\Delta Open_t, 15)$.<br/>Let $R_1 = rank_{cs}(DL1)$.<br/>Let $Corr_{OV} = corr(Open_t, Volume_t, 17)$.<br/>Let $DL2 = DecayLinear(Corr_{OV}, 7)$.<br/>Let $R_2 = rank_{cs}(DL2)$.<br/>$$\alpha_{35} = - \min(R_1, R_2)$$"
factor_142,142,简化为时序,开盘价变化和开盘价-成交量相关性复合因子，简化了横截面排名,"Let $\Delta Open_t = Open_t - Open_{t-1}$.<br/>Let $DL1 = DecayLinear(\Delta Open_t, 15)$.<br/>Let $R_1 = rank_{cs}(DL1)$.<br/>Let $Corr_{OV} = corr(Open_t, Volume_t, 17)$.<br/>Let $DL2 = DecayLinear(Corr_{OV}, 7)$.<br/>Let $R_2 = rank_{cs}(DL2)$.<br/>$$\alpha_{35} = - \min(R_1, R_2)$$"
factor_143_xs,143,截面+时序,VWAP和成交量标准化排名相关性因子，简化了横截面排名,"Let $R_V = rank_{cs}(Volume_t)$.<br/>Let $R_{VWAP} = rank_{cs}(VWAP_t)$.<br/>Let $C_t = corr(R_V, R_{VWAP}, 6)$.<br/>Let $S_C = \sum_{i=0}^{1} C_{t-i}$ (Sum over 2 periods).<br/>$$\alpha_{36} = rank_{cs}(S_C)$$"
factor_143,143,简化为时序,VWAP和成交量标准化排名相关性因子，简化了横截面排名,"Let $R_V = rank_{cs}(Volume_t)$.<br/>Let $R_{VWAP} = rank_{cs}(VWAP_t)$.<br/>Let $C_t = corr(R_V, R_{VWAP}, 6)$.<br/>Let $S_C = \sum_{i=0}^{1} C_{t-i}$ (Sum over 2 periods).<br/>$$\alpha_{36} = rank_{cs}(S_C)$$"
factor_144_xs,144,截面+时序,开盘价和收益率滚动求和乘积差分排名因子，简化了横截面排名,"Let $S_{Open,5,t} = \sum_{i=0}^{4} Open_{t-i}$.<br/>Let $S_{Ret,5,t} = \sum_{i=0}^{4} Ret_{t-i}$.<br/>Let $X_t = S_{Open,5,t} \cdot S_{Ret,5,t}$.<br/>$$\alpha_{37} = -rank_{cs}(X_t - X_{t-10})$$"
factor_144,144,简化为时序,开盘价和收益率滚动求和乘积差分排名因子，简化了横截面排名,"Let $S_{Open,5,t} = \sum_{i=0}^{4} Open_{t-i}$.<br/>Let $S_{Ret,5,t} = \sum_{i=0}^{4} Ret_{t-i}$.<br/>Let $X_t = S_{Open,5,t} \cdot S_{Ret,5,t}$.<br/>$$\alpha_{37} = -rank_{cs}(X_t - X_{t-10})$$"
factor_145,145,时序,Alpha38条件高点价格变化因子,"Let $MA_{20}(High_t)$ be the 20-period simple moving average of $High_t$.<br/>Let $\Delta_2 High_t = High_t - High_{t-2}$.<br/>Condition $C_1: MA_{20}(High_t) < High_t$.<br/>$$\alpha_{38} = \begin{cases} - \Delta_2 High_t & \text{if } C_1 \text{ is true} \\ 0 & \text{if } C_1 \text{ is false} \end{cases}$$<br/>This can be written as: $\alpha_{38} = - (High_t - High_{t-2}) \cdot I(MA_{20}(High_t) < High_t)$, where $I(\cdot)$ is the Iverson bracket."
factor_146,146,时序,Alpha40条件成交量比率因子,Let $UpVol_t = Volume_t \text{ if } Close_t > Close_{t-1} \text{ else } 0$.<br/>Let $DownVol_t = Volume_t \text{ if } Close_t \le Close_{t-1} \text{ else } 0$. (Note: code uses `~condition` which means $Close_t \le Close_{t-1}$)<br/>$$\alpha_{40} = \frac{\sum_{i=0}^{25} UpVol_{t-i}}{\sum_{i=0}^{25} DownVol_{t-i}} \times 100$$
factor_147_xs,147,截面+时序,Alpha41 VWAP变化最大值负排名因子，简化了横截面排名,"Let $\Delta_3 VWAP_t = VWAP_t - VWAP_{t-3}$.<br/>Let $Max\Delta VWAP_t = \max(\Delta_3 VWAP_t, 5)$ (5-period rolling maximum).<br/>$$\alpha_{41} = -rank_{cs}(Max\Delta VWAP_t)$$"
factor_147,147,简化为时序,Alpha41 VWAP变化最大值负排名因子，简化了横截面排名,"Let $\Delta_3 VWAP_t = VWAP_t - VWAP_{t-3}$.<br/>Let $Max\Delta VWAP_t = \max(\Delta_3 VWAP_t, 5)$ (5-period rolling maximum).<br/>$$\alpha_{41} = -rank_{cs}(Max\Delta VWAP_t)$$"
factor_149,149,时序,Alpha43 OBV变种累积因子,Let $SignedVol_t = \begin{cases} Volume_t & \text{if } Close_t > Close_{t-1} \\ -Volume_t & \text{if } Close_t < Close_{t-1} \\ 0 & \text{if } Close_t = Close_{t-1} \end{cases}$<br/>$$\alpha_{43} = \sum_{i=0}^{5} SignedVol_{t-i}$$
factor_150_xs,150,截面+时序,Alpha45价格动量与VWAP成交量相关性排名乘积因子，简化了横截面排名,"Let $P_{mix,t} = 0.6 \cdot Close_t + 0.4 \cdot Open_t$.<br/>Let $\Delta P_{mix,t} = P_{mix,t} - P_{mix,t-1}$.<br/>Let $R_1 = rank_{cs}(\Delta P_{mix,t})$.<br/><br/>Let $MAVol_{150,t} = MA(Volume_t, 150)$.<br/>Let $Corr_{VolVWAP,t} = corr(MAVol_{150,t}, VWAP_t, 15)$.<br/>Let $R_2 = rank_{cs}(Corr_{VolVWAP,t})$.<br/>$$\alpha_{45} = R_1 \cdot R_2$$"
factor_150,150,简化为时序,Alpha45价格动量与VWAP成交量相关性排名乘积因子，简化了横截面排名,"Let $P_{mix,t} = 0.6 \cdot Close_t + 0.4 \cdot Open_t$.<br/>Let $\Delta P_{mix,t} = P_{mix,t} - P_{mix,t-1}$.<br/>Let $R_1 = rank_{cs}(\Delta P_{mix,t})$.<br/><br/>Let $MAVol_{150,t} = MA(Volume_t, 150)$.<br/>Let $Corr_{VolVWAP,t} = corr(MAVol_{150,t}, VWAP_t, 15)$.<br/>Let $R_2 = rank_{cs}(Corr_{VolVWAP,t})$.<br/>$$\alpha_{45} = R_1 \cdot R_2$$"
factor_152,152,时序,Alpha49基于趋向指标UOS的一部分,"Let $H_t, L_t$ be current high and low. $H_{t-1}, L_{t-1}$ be previous day's high and low.<br/>Let $Term_t = \max(|H_t - H_{t-1}|, |L_t - L_{t-1}|)$.<br/>Let $P_1 = \sum_{k=0}^{11} (Term_{t-k} \cdot I((H_{t-k} + L_{t-k}) < (H_{t-k-1} + L_{t-k-1})))$. (Sum of $Term_t$ if sum of high and low is decreasing)<br/>Let $P_2 = \sum_{k=0}^{11} (Term_{t-k} \cdot I((H_{t-k} + L_{t-k}) > (H_{t-k-1} + L_{t-k-1})))$. (Sum of $Term_t$ if sum of high and low is increasing)<br/>(Note: Code uses `~condition` structure that implies ""equal to"" is grouped with one side.<br/>`condition1=(self.high+self.low>=delay_high+delay_low)` -> `part1` calc on `~condition1` (i.e., $H+L < dH+dL$).<br/>`condition2=(self.high+self.low<=delay_high+delay_low)` -> `part2` calc on `~condition2` (i.e., $H+L > dH+dL$).<br/>The term is 0 if the condition is not met. )<br/>$$\alpha_{49} = \frac{P_1}{P_1 + P_2}$$"
factor_155,155,时序,基于多重条件判断的因子,"Let $MA(X, N)$ be the N-period moving average of X.<br/>Let $std(X, N)$ be the N-period standard deviation of X.<br/><br/>$C_1: MA(Close_t, 8) + std(Close_t, 8) < MA(Close_t, 2)$<br/>$C_2: MA(Close_t, 2) < MA(Close_t, 8) - std(Close_t, 8)$<br/>$C_3: 1 \le \frac{Volume_t}{MA(Volume_t, 20)}$<br/><br/>$$\alpha_4 = MA(Close_t, 8) + V$$<br/>where<br/>$$V = \begin{cases} -1 & \text{if } C_1 \text{ is true} \\ +1 & \text{if } \neg C_1 \land C_2 \text{ is true} \\ +1 & \text{if } \neg C_1 \land \neg C_2 \land C_3 \text{ is true} \\ -1 & \text{if } \neg C_1 \land \neg C_2 \land \neg C_3 \text{ is true} \\ 0 & \text{otherwise (implicitly, though code structure suggests these are exhaustive if conditions are mutually exclusive based on prior ones)} \end{cases}$$<br/>The code implements this as:<br/>`part0 = MA(Close,8)`<br/>`part1 = -1 \cdot I(C_1)`<br/>`part2 = +1 \cdot I(\neg C_1 \land C_2)`<br/>`part3 = +1 \cdot I(\neg C_1 \land \neg C_2 \land C_3)`<br/>`part4 = -1 \cdot I(\neg C_1 \land \neg C_2 \land \neg C_3)`<br/>`alpha = part0 + part1 + part2 + part3 + part4`<br/>Where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false)."
factor_156,156,时序,Alpha52价格相对位置的累积比率因子,"Let $AvgP_t = (High_t + Low_t + Close_t)/3$.<br/>Let $dAvgP_t = AvgP_{t-1}$.<br/>Let $TermUp_t = \max(0, High_t - dAvgP_t)$.<br/>Let $TermDown_t = \max(0, dAvgP_t - Low_t)$.<br/>$$\alpha_{52} = \frac{\sum_{i=0}^{25} TermUp_{t-i}}{\sum_{i=0}^{25} TermDown_{t-i}} \times 100$$"
factor_158,158,时序,Alpha55复杂条件价格变动累积因子,"Let $C_t, O_t, H_t, L_t$ be Close, Open, High, Low at time $t$.<br/>Let $C_{t-1}, O_{t-1}, L_{t-1}$ be their values at $t-1$.<br/>Numerator term: $N_t = 16 \times (C_t - C_{t-1} + \frac{C_t - O_t}{2} + C_{t-1} - O_{t-1}) \times \max(|H_t - C_{t-1}|, |L_t - C_{t-1}|)$.<br/>Denominator term $D_t$:<br/>Let $TermA = |H_t - C_{t-1}|$.<br/>Let $TermB = |L_t - C_{t-1}|$.<br/>Let $TermC = |H_t - L_{t-1}|$. (Note: code `dlow` is `self.low.shift()`, so $L_{t-1}$)<br/>Let $TermD = |C_{t-1} - O_{t-1}|$.<br/>If $TermA > TermB$ AND $TermA > TermC$:<br/>  $D_t = TermA + TermB/2 + TermD/4$.<br/>Else if $TermB > TermC$ AND $TermB > TermA$: (Code: `condition3 & condition4`)<br/>  $D_t = TermB + TermA/2 + TermD/4$.<br/>Else:<br/>  $D_t = TermC + TermD/4$.<br/><br/>Let $X_t = \frac{N_t}{D_t}$.<br/>$$\alpha_{55} = \sum_{i=0}^{19} X_{t-i}$$"
factor_159,159,时序,Alpha57平滑随机震荡%K因子,"Let $L_9 = \min(Low_t, 9)$ (9-period rolling min of Low).<br/>Let $H_9 = \max(High_t, 9)$ (9-period rolling max of High).<br/>Let $\%K_t = \frac{Close_t - L_9}{H_9 - L_9} \times 100$. (Stochastic Oscillator %K)<br/>$$\alpha_{57} = EMA(\%K_t, span=5)$$"
factor_161,161,时序,Alpha60成交量加权日内价格位置累积因子,Let $Pos_t = \frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t}$. (This is the same as $Pos_t$ in Alpha 11)<br/>Let $X_t = Pos_t \cdot Volume_t$.<br/>$$\alpha_{60} = \sum_{i=0}^{19} X_{t-i}$$
factor_162_xs,162,截面+时序,Alpha61 VWAP动量与最低价成交量相关性排名最大值因子，简化了横截面排名,"Let $\Delta VWAP_t = VWAP_t - VWAP_{t-1}$.<br/>Let $DL_1 = DecayLinear(\Delta VWAP_t, 12)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $Corr_{LV,t} = corr(Low_t, MA_{80}(Volume_t), 8)$.<br/>Let $RankCorr_{LV,t} = rank_{cs}(Corr_{LV,t})$.<br/>Let $DL_2 = DecayLinear(RankCorr_{LV,t}, 17)$.<br/>Let $R_2 = rank_{cs}(DL_2)$.<br/>$$\alpha_{61} = -\max(R_1, R_2)$$"
factor_162,162,简化为时序,Alpha61 VWAP动量与最低价成交量相关性排名最大值因子，简化了横截面排名,"Let $\Delta VWAP_t = VWAP_t - VWAP_{t-1}$.<br/>Let $DL_1 = DecayLinear(\Delta VWAP_t, 12)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $Corr_{LV,t} = corr(Low_t, MA_{80}(Volume_t), 8)$.<br/>Let $RankCorr_{LV,t} = rank_{cs}(Corr_{LV,t})$.<br/>Let $DL_2 = DecayLinear(RankCorr_{LV,t}, 17)$.<br/>Let $R_2 = rank_{cs}(DL_2)$.<br/>$$\alpha_{61} = -\max(R_1, R_2)$$"
factor_163,163,时序,Alpha5高点与成交量时序排名的相关性因子,"Let $ts\_rank(X, N)$ be the rank of $X_t$ in the window $(X_{t-N+1}, ..., X_t)$.<br/>$$R_H = ts\_rank(High_t, 5) \text{ (applied on a rolling basis)}$$<br/>$$R_V = ts\_rank(Volume_t, 5) \text{ (applied on a rolling basis)}$$<br/>$$\alpha_5 = - \max(\text{corr}(R_H, R_V, 5), 3)$$"
factor_168,168,时序,Alpha68平滑日内波动率调整的价格动量因子,"Let $Mid_t = (High_t + Low_t) / 2$.<br/>Let $\Delta Mid_t = Mid_t - Mid_{t-1}$.<br/>Let $Range_t = High_t - Low_t$.<br/>Let $X_t = \frac{\Delta Mid_t \cdot Range_t}{Volume_t}$.<br/>$$\alpha_{68} = EMA(X_t, span=14)$$"
factor_169,169,时序,Alpha69开盘价定向运动比率因子,"Let $O_t = Open_t, H_t = High_t, L_t = Low_t$. $O_{t-1} = Open_{t-1}$.<br/>$DTM_t = \begin{cases} \max(H_t - O_t, O_t - O_{t-1}) & \text{if } O_t > O_{t-1} \\ 0 & \text{otherwise} \end{cases}$<br/>$DBM_t = \begin{cases} \max(O_t - L_t, O_t - O_{t-1}) & \text{if } O_t < O_{t-1} \\ 0 & \text{otherwise} \end{cases}$<br/>(Note: Code uses $O_t - O_{t-1}$ for $DBM_t$ term which means if $O_t < O_{t-1}$, then $O_t - O_{t-1}$ is negative. `np.maximum` with a negative value might not be intended for directional movement. Original DMI usually uses $|O_t - O_{t-1}|$. However, I will follow the code. `np.maximum(positive, negative)` will be positive. But if both terms are for DBM, $O_t-L_t$ (positive) and $O_t-O_{t-1}$ (negative for $O_t<O_{t-1}$ case), the maximum will be $O_t-L_t$. So this is okay.)<br/><br/>Let $S_{DTM} = \sum_{i=0}^{19} DTM_{t-i}$.<br/>Let $S_{DBM} = \sum_{i=0}^{19} DBM_{t-i}$.<br/>$$\alpha_{69} = \begin{cases} (S_{DTM} - S_{DBM}) / S_{DTM} & \text{if } S_{DTM} > S_{DBM} \text{ and } S_{DTM} \neq 0 \\ (S_{DTM} - S_{DBM}) / S_{DBM} & \text{if } S_{DTM} < S_{DBM} \text{ and } S_{DBM} \neq 0 \\ 0 & \text{if } S_{DTM} = S_{DBM} \text{ or relevant denominator is 0} \end{cases}$$"
factor_170,170,时序,Alpha70成交金额6日滚动标准差,"$$\alpha_{70} = std(Amount_t, 6)$$"
factor_172,172,简化为时序,基于加权价格差分信号的横截面排名因子，简化了横截面排名,Let $P_t = 0.85 \cdot Open_t + 0.15 \cdot High_t$.<br/>Let $\Delta P_t = P_t - P_{t-4}$.<br/>$$S_t = \begin{cases} 1 & \text{if } \Delta P_t > 0 \\ 0 & \text{if } \Delta P_t = 0 \\ -1 & \text{if } \Delta P_t < 0 \end{cases}$$<br/>$$\alpha_6 = rank_{cs}(S_t)$$
factor_172_xs,172,截面+时序,基于加权价格差分信号的横截面排名因子，简化了横截面排名,Let $P_t = 0.85 \cdot Open_t + 0.15 \cdot High_t$.<br/>Let $\Delta P_t = P_t - P_{t-4}$.<br/>$$S_t = \begin{cases} 1 & \text{if } \Delta P_t > 0 \\ 0 & \text{if } \Delta P_t = 0 \\ -1 & \text{if } \Delta P_t < 0 \end{cases}$$<br/>$$\alpha_6 = rank_{cs}(S_t)$$
factor_174_xs,174,截面+时序,Alpha77价格与VWAP偏离及价格与成交量相关性衰减排名最小化因子，简化了横截面排名,"Let $Mid_t = (High_t + Low_t) / 2$.<br/>Let $Term1_t = Mid_t - VWAP_t$.<br/>Let $DL_1 = DecayLinear(Term1_t, 20)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $Corr_2 = corr(Mid_t, MA_{40}(Volume_t), 3)$.<br/>Let $DL_2 = DecayLinear(Corr_2, 6)$.<br/>Let $R_2 = rank_{cs}(DL_2)$.<br/>$$\alpha_{77} = \min(R_1, R_2)$$"
factor_174,174,简化为时序,Alpha77价格与VWAP偏离及价格与成交量相关性衰减排名最小化因子，简化了横截面排名,"Let $Mid_t = (High_t + Low_t) / 2$.<br/>Let $Term1_t = Mid_t - VWAP_t$.<br/>Let $DL_1 = DecayLinear(Term1_t, 20)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $Corr_2 = corr(Mid_t, MA_{40}(Volume_t), 3)$.<br/>Let $DL_2 = DecayLinear(Corr_2, 6)$.<br/>Let $R_2 = rank_{cs}(DL_2)$.<br/>$$\alpha_{77} = \min(R_1, R_2)$$"
factor_176,176,时序,Alpha80五日成交量变化率,$$\alpha_{80} = \frac{Volume_t - Volume_{t-5}}{Volume_{t-5}} \times 100$$
factor_177,177,时序,Alpha81成交量指数移动平均因子,"$$\alpha_{81} = EMA(Volume_t, span=40)$$<br/>(Note: The code's `span = 2*21 - 2 = 40` is used. A typical conversion for `SMA(X,N,M)` with $N=21, M=2$ would be $\alpha=M/N=2/21$, leading to $span = (2/\alpha) - 1 = 21 - 1 = 20$. The provided code uses span=40, so this is followed.)"
factor_178_xs,178,截面+时序,Alpha7 VWAP与收盘价偏离及成交量变化因子，简化了横截面排名,"Let $D_t = VWAP_t - Close_t$.<br/>Let $\Delta Vol_t = Volume_t - Volume_{t-3}$.<br/>$$\alpha_7 = (rank_{cs}(\max(D_t, 3)) + rank_{cs}(\min(D_t, 3))) \cdot rank_{cs}(\Delta Vol_t)$$"
factor_178,178,简化为时序,Alpha7 VWAP与收盘价偏离及成交量变化因子，简化了横截面排名,"Let $D_t = VWAP_t - Close_t$.<br/>Let $\Delta Vol_t = Volume_t - Volume_{t-3}$.<br/>$$\alpha_7 = (rank_{cs}(\max(D_t, 3)) + rank_{cs}(\min(D_t, 3))) \cdot rank_{cs}(\Delta Vol_t)$$"
factor_180,180,时序,Alpha84二十日OBV变种累积因子,Let $SignedVol_t = \begin{cases} Volume_t & \text{if } Close_t > Close_{t-1} \\ -Volume_t & \text{if } Close_t < Close_{t-1} \\ 0 & \text{if } Close_t = Close_{t-1} \end{cases}$<br/>$$\alpha_{84} = \sum_{i=0}^{19} SignedVol_{t-i}$$
factor_181,181,时序,Alpha85成交量强度与价格动量时序排名乘积因子,"Let $VolRatio_t = \frac{Volume_t}{MA_{20}(Volume_t)}$.<br/>Let $P_1 = ts\_rank(VolRatio_t, 20, pct=False)$.<br/><br/>Let $\Delta_7 Close_t = Close_t - Close_{t-7}$.<br/>Let $P_2 = ts\_rank(-\Delta_7 Close_t, 8, pct=False)$.<br/>$$\alpha_{85} = P_1 \cdot P_2$$"
factor_182,182,时序,Alpha86条件价格加速因子,Let $C_t = Close_t$.<br/>Let $Acc_t = \frac{C_{t-20} - C_{t-10}}{10} - \frac{C_{t-10} - C_t}{10}$.<br/>$$\alpha_{86} = \begin{cases} -1 & \text{if } Acc_t > 0.25 \\ 1 & \text{if } Acc_t < 0 \text{ (and not previous condition)} \\ -(C_t - C_{t-1}) & \text{otherwise} \end{cases}$$
factor_183,183,简化为时序,基于VWAP差分和价格结构的复合因子，简化了横截面排名,"Let $\Delta_4 VWAP_t = VWAP_t - VWAP_{t-4}$.<br/>Let $DL_1 = DecayLinear(\Delta_4 VWAP_t, 7)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $Term_t = \frac{Low_t - VWAP_t}{Open_t - (High_t + Low_t)/2}$.<br/>Let $DL_2 = DecayLinear(Term_t, 11)$.<br/>Let $R_2 = ts\_rank(DL_2, 7, pct=True)$.<br/>$$\alpha_{87} = -(R_1 + R_2)$$"
factor_183_xs,183,截面+时序,基于VWAP差分和价格结构的复合因子，简化了横截面排名,"Let $\Delta_4 VWAP_t = VWAP_t - VWAP_{t-4}$.<br/>Let $DL_1 = DecayLinear(\Delta_4 VWAP_t, 7)$.<br/>Let $R_1 = rank_{cs}(DL_1)$.<br/><br/>Let $Term_t = \frac{Low_t - VWAP_t}{Open_t - (High_t + Low_t)/2}$.<br/>Let $DL_2 = DecayLinear(Term_t, 11)$.<br/>Let $R_2 = ts\_rank(DL_2, 7, pct=True)$.<br/>$$\alpha_{87} = -(R_1 + R_2)$$"
factor_185,185,时序,Alpha89基于MACD指标变体，应用tanh变换,"Let $EMA_{short} = EMA(Close_t, span=12)$ (corresponds to SMA(Close,13,2)).<br/>Let $EMA_{long} = EMA(Close_t, span=26)$ (corresponds to SMA(Close,27,2)).<br/>Let $MACDLine_t = EMA_{short} - EMA_{long}$.<br/>Let $SignalLine_t = EMA(MACDLine_t, span=9)$ (corresponds to SMA(MACDLine,10,2)).<br/>$$\alpha_{89} = 2 \times (MACDLine_t - SignalLine_t)$$"
factor_186_xs,186,截面+时序,基于加权平均价格差分的横截面排名因子，简化了横截面排名,"Let $P_t = 0.8 \cdot VWAP_t + 0.1 \cdot High_t + 0.1 \cdot Low_t$.<br/>(Code: `(self.high+self.low)*0.2/2+self.vwap*0.8` which is $0.1 \cdot High_t + 0.1 \cdot Low_t + 0.8 \cdot VWAP_t$)<br/>$$\alpha_8 = rank_{cs}(-\Delta(P_t, 4))$$"
factor_186,186,简化为时序,基于加权平均价格差分的横截面排名因子，简化了横截面排名,"Let $P_t = 0.8 \cdot VWAP_t + 0.1 \cdot High_t + 0.1 \cdot Low_t$.<br/>(Code: `(self.high+self.low)*0.2/2+self.vwap*0.8` which is $0.1 \cdot High_t + 0.1 \cdot Low_t + 0.8 \cdot VWAP_t$)<br/>$$\alpha_8 = rank_{cs}(-\Delta(P_t, 4))$$"
factor_187,187,时序,Alpha93条件开盘价差累积因子,"Let $O_t = Open_t, L_t = Low_t, O_{t-1} = Open_{t-1}$.<br/>Let $Term_t = \begin{cases} 0 & \text{if } O_t \ge O_{t-1} \\ \max(O_t - L_t, O_t - O_{t-1}) & \text{if } O_t < O_{t-1} \end{cases}$<br/>$$\alpha_{93} = \sum_{i=0}^{19} Term_{t-i}$$"
factor_190,190,时序,Alpha98条件价格偏离因子,"Let $MA_{100,t} = MA(Close_t, 100)$.<br/>Let $\Delta_{100} MA_{100,t} = MA_{100,t} - MA_{100,t-100}$.<br/>Let $GrowthRate_{MA100,t} = \frac{\Delta_{100} MA_{100,t}}{Close_{t-100}}$.<br/><br/>Condition $C_1: GrowthRate_{MA100,t} \le 0.05$.<br/>If $C_1$ is true:<br/>  $\alpha_{98} = -(Close_t - \min(Close_t, 100))$<br/>Else ($C_1$ is false):<br/>  $\alpha_{98} = -(Close_t - Close_{t-3})$"
factor_192_xs,192,截面+时序,Alpha101基于成交量相关性和价格排名的复合因子，简化了横截面排名,"Let $SumMAVol_t = \sum_{k=0}^{36} (MA_{30}(Volume_{t-k}))$.<br/>Let $Corr_1 = corr(Close_t, SumMAVol_t, 15)$.<br/>Let $R_1 = rank_{cs}(Corr_1)$.<br/><br/>Let $P_{mix,t} = 0.1 \cdot High_t + 0.9 \cdot VWAP_t$.<br/>Let $RankP_{mix,t} = rank_{cs}(P_{mix,t})$.<br/>Let $RankVol_t = rank_{cs}(Volume_t)$.<br/>Let $Corr_2 = corr(RankP_{mix,t}, RankVol_t, 11)$.<br/>Let $R_2 = rank_{cs}(Corr_2)$.<br/>$$\alpha_{101} = -I(R_1 < R_2)$$<br/>where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false)."
factor_192,192,简化为时序,Alpha101基于成交量相关性和价格排名的复合因子，简化了横截面排名,"Let $SumMAVol_t = \sum_{k=0}^{36} (MA_{30}(Volume_{t-k}))$.<br/>Let $Corr_1 = corr(Close_t, SumMAVol_t, 15)$.<br/>Let $R_1 = rank_{cs}(Corr_1)$.<br/><br/>Let $P_{mix,t} = 0.1 \cdot High_t + 0.9 \cdot VWAP_t$.<br/>Let $RankP_{mix,t} = rank_{cs}(P_{mix,t})$.<br/>Let $RankVol_t = rank_{cs}(Volume_t)$.<br/>Let $Corr_2 = corr(RankP_{mix,t}, RankVol_t, 11)$.<br/>Let $R_2 = rank_{cs}(Corr_2)$.<br/>$$\alpha_{101} = -I(R_1 < R_2)$$<br/>where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false)."
factor_194,194,时序,Alpha100 VWAP滚动最大值与开盘价比率因子,"$$\text{Alpha100} = \text{div} \left( \text{ts_max}(\text{vwap}, 8), \text{open_price} \right)$$"
factor_195,195,时序,Alpha101复杂的协方差、回归beta和最小值计算因子,"$$\text{Alpha101} = \text{gp_min} \left( \text{delay} \left( \text{ts_regbeta} \left( \text{mul}(\text{open_price}, \text{volume}), \text{ts_cov} \left( 10, \text{arctan}(\text{low}), \text{add} \left( \text{gp_max}(\text{high}, \text{volume}), \text{vwap} \right) \right), 20 \right), 15 \right), \text{ts_pctchg}(\text{close}, 8) \right)$$"
factor_196,196,时序,Alpha102基于成交金额最大值、VWAP相关性和价格差分的最小值因子,"$$\text{Alpha102} = \text{gp_min} \left( \text{ts_corr} \left( 16, \text{vwap}, \text{sqrt} \left( \text{ts_max}(\text{amount}, 8) \right) \right), \text{delta}(\text{close}, 5) \right)$$"
factor_197,197,时序,Alpha103基于VWAP-成交量相关性和VWAP-成交金额最小值与低价标准差协方差的最小值因子,"$$\text{Alpha103} = \text{gp_min} \left( \text{ts_corr}(16, \text{vwap}, \text{volume}), \text{ts_cov} \left( 18, \text{gp_min}(\text{vwap}, \text{amount}), \text{ts_std}(\text{low}, 10) \right) \right)$$"
factor_198,198,时序,Alpha104基于成交量-低价相关性和成交量最大值变化率的反正切因子,"$$\text{Alpha104} = \text{arctan} \left( \text{add} \left( \text{ts_corr}(16, \text{volume}, \text{low}), \text{ts_pctchg} \left( \text{ts_max}(\text{volume}, 20), 3 \right) \right) \right)$$"
factor_199,199,时序,优化后的Alpha10基于收盘价和成交量的Z-score标准化，然后计算标准差,"$$\text{Alpha10} = \text{ts_std} \left( \text{add} \left( \text{gp_max} \left( \text{ts_std}(\text{close}, 11), \text{ts_max}(\text{volume}, 14) \right), \text{ts_std}(\text{close}, 11) \right), 15 \right)$$"
factor_200,200,时序,Alpha111基于成交量与开盘价/收盘价的相关性、成交金额平方根等的复合计算,"$$\text{Alpha111} = \text{mul} \left( \text{ts_max} \left( \text{ts_corr}(12, \text{volume}, \text{open_price}), 18 \right), \text{delay} \left( \text{gp_max} \left( \text{sqrt}(\text{amount}), \text{ts_corr}(12, \text{volume}, \text{close}) \right), 10 \right) \right)$$"
factor_201,201,时序,Alpha112基于volume和high的Z-score差值与low+close对数的滚动回归贝塔因子,"$$\text{Alpha112} = \text{ts_regbeta} \left( \text{sub}(\text{log}(\text{volume}), \text{high}), \text{log} \left( \text{add}(\text{low}, \text{close}) \right), 10 \right)$$"
factor_202,202,时序,Alpha114基于close排名、vwap对数和回归残差的复合因子,"$$\text{Alpha114} = \text{ts_regres} \left( \text{gp_max} \left( \text{ts_rank}(\text{close}, 6), \text{log}(\text{vwap}) \right), \text{vwap}, 8 \right)$$"
factor_203,203,时序,Alpha115基于volume和close的Z-score与close变化率的相关性因子,"$$\text{Alpha115} = \text{ts_corr} \left( 9, \text{add}(\text{volume}, \text{close}), \text{sqrt} \left( \text{ts_pctchg}(\text{close}, 9) \right) \right)$$"
factor_204,204,时序,Alpha11基于收盘价对数的差分因子,"$$\text{Alpha11} = \text{delta}(\text{log}(\text{close}), 2)$$"
factor_205,205,时序,Alpha120基于成交量对数差分的滚动标准差因子,"$$\text{Alpha120} = \text{ts_std} \left( \text{delta}(\text{log}(\text{volume}), 12), 8 \right)$$"
factor_207_xs,207,截面+时序,Alpha122基于amount和open相关性的截面排名因子，简化了横截面排名,"$$\text{Alpha122} = \text{rank}(\text{ts_corr}(8, \text{amount}, \text{open_price}))$$"
factor_207,207,简化为时序,Alpha122基于amount和open相关性的截面排名因子，简化了横截面排名,"$$\text{Alpha122} = \text{rank}(\text{ts_corr}(8, \text{amount}, \text{open_price}))$$"
factor_208,208,时序,Alpha123基于volume标准差与amount对close回归贝塔乘积的反正切因子,"$$\text{Alpha123} = \text{arctan} \left( \text{mul} \left( \text{ts_std}(\text{volume}, 16), \text{ts_regbeta}(\text{amount}, \text{close}, 8) \right) \right)$$"
factor_209,209,时序,基于VWAP差分与low-volume相关性最小值的因子,"$$\text{Alpha124} = \text{gp_min} \left( \text{delta}(\text{vwap}, 6), \text{ts_corr}(10, \text{low}, \text{volume}) \right)$$"
factor_210,210,时序,基于成交量绝对值与开盘价变化率相关性的因子,"$$\text{Alpha126} = \text{ts_corr}(10, \text{abs}(\text{volume}), \text{ts_pctchg}(\text{open_price}, 16))$$"
factor_211,211,时序,Alpha127基于高价差分的Z-score与成交量-收盘价协方差最小值的反正切之和,"$$\text{Alpha127} = \text{add} \left( \text{delta}(\text{high}, 8), \text{arctan} \left( \text{ts_min} \left( \text{ts_cov}(16, \text{volume}, \text{close}), 6 \right) \right) \right)$$"
factor_212,212,时序,Alpha126基于close差分Z-score和high反正切的复合因子,"$$\text{Alpha128} = \text{add} \left( \text{delta}(\text{close}, 8), \text{arctan}(\text{high}) \right)$$"
factor_213,213,时序,Alpha127基于VWAP、amount和close的复合技术分析因子,"$$\text{Alpha129} = \text{gp_min} \left( \text{div} \left( \text{close}, \text{sub} \left( \text{ts_min}(\text{neg}(\text{vwap}), 15), \text{abs}(\text{amount}) \right) \right), \text{delta}(\text{open_price}, 8) \right)$$"
factor_214,214,简化为时序,Alpha131基于volume-open回归贝塔的Z-score排名与low-close回归贝塔负值的和，简化了横截面排名,"$$\text{Alpha131} = \text{add} \left( \text{rank} \left( \text{ts_regbeta}(\text{volume}, \text{open_price}, 9) \right), \text{neg} \left( \text{ts_regbeta}(\text{close}, \text{low}, 16) \right) \right)$$"
factor_214_xs,214,截面+时序,Alpha131基于volume-open回归贝塔的Z-score排名与low-close回归贝塔负值的和，简化了横截面排名,"$$\text{Alpha131} = \text{add} \left( \text{rank} \left( \text{ts_regbeta}(\text{volume}, \text{open_price}, 9) \right), \text{neg} \left( \text{ts_regbeta}(\text{close}, \text{low}, 16) \right) \right)$$"
factor_215,215,简化为时序,Alpha132基于low-volume最大值排名、volume Z-score排名与high-volume乘积比值和vwap-close和的协方差，简化了横截面排名,"$$\text{Alpha132} = \text{ts_cov} \left( 9, \text{ts_rank} \left( \text{gp_max}(\text{low}, \text{volume}), 6 \right), \text{gp_max} \left( \text{div} \left( \text{rank}(\text{volume}), \text{mul}(\text{high}, \text{volume}) \right), \text{add}(\text{vwap}, \text{close}) \right) \right)$$"
factor_215_xs,215,截面+时序,Alpha132基于low-volume最大值排名、volume Z-score排名与high-volume乘积比值和vwap-close和的协方差，简化了横截面排名,"$$\text{Alpha132} = \text{ts_cov} \left( 9, \text{ts_rank} \left( \text{gp_max}(\text{low}, \text{volume}), 6 \right), \text{gp_max} \left( \text{div} \left( \text{rank}(\text{volume}), \text{mul}(\text{high}, \text{volume}) \right), \text{add}(\text{vwap}, \text{close}) \right) \right)$$"
factor_216,216,时序,Alpha133基于high对amount和volume对close的回归贝塔最大值,"$$\text{Alpha133} = \text{gp_max} \left( \text{ts_regbeta}(\text{high}, \text{amount}, 16), \text{ts_regbeta}(\text{volume}, \text{close}, 8) \right)$$"
factor_218,218,时序,Alpha131基于VWAP与close差值的滚动均值因子,"$$\text{Alpha136} = \text{ts_mean} \left( \text{sub}(\text{vwap}, \text{close}), 5 \right)$$"
factor_219,219,时序,Alpha137基于VWAP的百分比变化率因子,"$$\text{Alpha137} = \text{ts_pctchg}(\text{vwap}, 9)$$"
factor_220,220,时序,Alpha138基于open差分Z-score和low-volume相关性的复合因子,"$$\text{Alpha138} = \text{sub} \left( \text{delta}(\text{open_price}, 9), \text{neg} \left( \text{gp_max} \left( \text{log}(\text{volume}), \text{ts_corr}(12, \text{low}, \text{volume}) \right) \right) \right)$$"
factor_221_xs,221,截面+时序,基于成交量与最高价的协方差排名，简化了横截面排名,"$$\text{Alpha13} = \text{rank}(\text{ts_cov}(9, \text{volume}, \text{high}))$$"
factor_221,221,简化为时序,基于成交量与最高价的协方差排名，简化了横截面排名,"$$\text{Alpha13} = \text{rank}(\text{ts_cov}(9, \text{volume}, \text{high}))$$"
factor_222,222,时序,Alpha141基于延迟成交量与收盘价差分的乘积,"$$\text{Alpha141} = \text{mul} \left( \text{delay}(\text{volume}, 12), \text{delta}(\text{close}, 12) \right)$$"
factor_223,223,时序,基于高低价回归贝塔和回归残差的因子,"$$\text{Alpha143} = \text{ts_regres} \left( \text{div} \left( \text{close}, \text{ts_regbeta}(\text{high}, \text{low}, 18) \right), \text{open_price}, 16 \right)$$"
factor_225,225,时序,Alpha146基于VWAP-amount协方差与low均值乘积和amount绝对值标准差的相关性因子,"$$\text{Alpha146} = \text{ts_corr} \left( 15, \text{mul} \left( \text{ts_cov}(9, \text{vwap}, \text{amount}), \text{ts_mean}(\text{low}, 15) \right), \text{ts_std}(\text{abs}(\text{amount}), 8) \right)$$"
factor_226,226,时序,Alpha147基于Z-score和价格变化的组合，包含sigmoid变换和对数价格变化率,"$$\text{Alpha147} = \text{sub} \left( \text{sigmoid}(\text{sub}(\text{low}, \text{volume})), \text{ts_pctchg}(\text{log}(\text{close}), 9) \right)$$"
factor_227,227,时序,Alpha14基于log(volume)和close的滚动相关系数,"$$\text{Alpha14} = \text{ts_corr}(9, \text{log}(\text{volume}), \text{gp_max}(\text{close}, \text{close}))$$"
factor_228_xs,228,截面+时序,Alpha150基于成交量平方根与收盘价排名-VWAP最大值协方差的Z-score和收盘价Z-score排名的和，简化了横截面排名,"$$\text{Alpha150} = \text{add} \left( \text{ts_cov} \left( 10, \text{sqrt}(\text{volume}), \text{gp_max} \left( \text{ts_rank}(\text{close}, 20), \text{vwap} \right) \right), \text{rank}(\text{close}) \right)$$"
factor_228,228,简化为时序,Alpha150基于成交量平方根与收盘价排名-VWAP最大值协方差的Z-score和收盘价Z-score排名的和，简化了横截面排名,"$$\text{Alpha150} = \text{add} \left( \text{ts_cov} \left( 10, \text{sqrt}(\text{volume}), \text{gp_max} \left( \text{ts_rank}(\text{close}, 20), \text{vwap} \right) \right), \text{rank}(\text{close}) \right)$$"
factor_229,229,时序,Alpha151基于arctan(high)与open百分比变化和volume差分最大值的相关性,"$$\text{Alpha151} = \text{ts_corr} \left( 12, \text{arctan}(\text{high}), \text{gp_max} \left( \text{ts_pctchg}(\text{open_price}, 14), \text{delta}(\text{volume}, 12) \right) \right)$$"
factor_230,230,时序,基于价格差分和成交量相关性的组合,"$$\text{Alpha152} = \text{ts_corr}(6, \text{delta}(\text{low}, 4), \text{neg}(\text{volume}))$$"
factor_231,231,时序,Alpha153基于成交量标准化、开盘价Z-score和高价差分的复合因子,"$$\text{Alpha153} = \text{mul} \left( \text{gp_max} \left( \text{sub}(\text{open_price}, \text{volume}), \text{gp_max}(\text{volume}, \text{vwap}) \right), \text{delta}(\text{high}, 8) \right)$$"
factor_232_xs,232,截面+时序,Alpha154基于low、high、vwap和volume的复合技术分析因子，简化了横截面排名,"$$\text{Alpha154} = \text{div} \left( \text{div} \left( \text{neg}(\text{low}), \text{sub}(\text{high}, \text{vwap}) \right), \text{rank} \left( \text{ts_cov} \left( 12, \text{gp_max}(\text{low}, \text{vwap}), \text{volume} \right) \right) \right)$$"
factor_232,232,简化为时序,Alpha154基于low、high、vwap和volume的复合技术分析因子，简化了横截面排名,"$$\text{Alpha154} = \text{div} \left( \text{div} \left( \text{neg}(\text{low}), \text{sub}(\text{high}, \text{vwap}) \right), \text{rank} \left( \text{ts_cov} \left( 12, \text{gp_max}(\text{low}, \text{vwap}), \text{volume} \right) \right) \right)$$"
factor_233,233,简化为时序,Alpha155基于VWAP负值与amount均值比值和close-vwap最大值与volume协方差排名的比值，简化了横截面排名,"$$\text{Alpha155} = \text{div} \left( \text{div} \left( \text{neg}(\text{vwap}), \text{ts_mean}(\text{amount}, 16) \right), \text{rank} \left( \text{ts_cov} \left( 10, \text{gp_max}(\text{close}, \text{vwap}), \text{volume} \right) \right) \right)$$"
factor_233_xs,233,截面+时序,Alpha155基于VWAP负值与amount均值比值和close-vwap最大值与volume协方差排名的比值，简化了横截面排名,"$$\text{Alpha155} = \text{div} \left( \text{div} \left( \text{neg}(\text{vwap}), \text{ts_mean}(\text{amount}, 16) \right), \text{rank} \left( \text{ts_cov} \left( 10, \text{gp_max}(\text{close}, \text{vwap}), \text{volume} \right) \right) \right)$$"
factor_236,236,时序,Alpha164基于low最大值与amount均值协方差的最大值因子,"$$Alpha_{164} = \text{ts_max}(\text{ts_cov}(10, \text{ts_max}(\text{low}, 7), \text{ts_mean}(\text{amount}, 20)), 16)$$"
factor_237,237,时序,Alpha165基于volume-amount协方差与amount-high回归贝塔均值的比值因子,"$$Alpha_{165} = \text{div}(\text{ts_cov}(12, \text{volume}, \text{amount}), \text{ts_mean}(\text{ts_regbeta}(\text{amount}, \text{high}, 16), 8))$$"
factor_238,238,时序,Alpha167基于volume-high相关系数的滚动最大值因子,"$$Alpha_{167} = \text{ts_max}(\text{ts_corr}(16, \text{volume}, \text{high}), 5)$$"
factor_240,240,时序,Alpha169基于VWAP sigmoid变换、成交量差分最大值与高价百分比变化率最小值的因子,"$$Alpha_{169} = \text{gp_min}(\text{gp_max}(\text{sigmoid}(\text{vwap}), \text{delta}(\text{volume}, 2)), \text{ts_pctchg}(\text{high}, 5))$$"
factor_242,242,时序,Alpha170基于close标准差和volume最大值的复合Z-score标准差因子,"$$Alpha_{170} = \text{ts_std}(\text{add}(\text{gp_max}(\text{ts_std}(\text{close}, 5), \text{ts_max}(\text{volume}, 10)), \text{ts_std}(\text{close}, 5)), 14)$$"
factor_245_xs,245,截面+时序,Alpha173对过去5期成交量和最高价的滚动协方差进行截面排名，简化了横截面排名,"$$Alpha_{173} = \text{rank}(\text{ts_cov}(5, \text{volume}, \text{high}))$$"
factor_245,245,简化为时序,Alpha173对过去5期成交量和最高价的滚动协方差进行截面排名，简化了横截面排名,"$$Alpha_{173} = \text{rank}(\text{ts_cov}(5, \text{volume}, \text{high}))$$"
factor_248,248,时序,Alpha178基于延迟16期的high与close的回归残差,"$$Alpha_{178} = \text{ts_regres}(\text{delay}(\text{high}, 16), \text{close}, 14)$$"
factor_250,250,时序,Alpha181基于VWAP与volume标准差最小值除以low最大值的比值因子,"$$Alpha_{181} = \text{div}(\text{gp_min}(\text{vwap}, \text{ts_std}(\text{volume}, 10)), \text{ts_max}(\text{low}, 10))$$"
factor_251,251,时序,Alpha182基于volume绝对值与close平方根的回归贝塔sigmoid变换，然后与low的回归残差的反正切,"$$Alpha_{182} = \text{arctan}(\text{ts_regres}(\text{sigmoid}(\text{ts_regbeta}(\text{abs}(\text{volume}), \text{sqrt}(\text{close}), 6)), \text{low}, 9))$$"
factor_252,252,时序,Alpha185基于amount-low回归贝塔与close/open比值的Z-score差值绝对值,"$$Alpha_{185} = \text{abs}(\text{sub}(\text{ts_regbeta}(\text{amount}, \text{low}, 8), \text{div}(\text{close}, \text{open_price})))$$"
factor_253,253,时序,Alpha186基于volume和vwap在过去10期的滚动相关系数,"$$Alpha_{186} = \text{ts_corr}(10, \text{volume}, \text{vwap})$$"
factor_255,255,时序,Alpha191基于open-close回归残差与high绝对值的差值，再与low+high的和的差值的百分比变化率,"$$\text{Alpha191} = \text{PCT\_CHG}(\text{LOW} + \text{HIGH} - (\text{ABS}(\text{HIGH}) - \text{TS\_REGRES}(\text{OPEN}, \text{CLOSE}, 16)), 6)$$"
factor_256,256,时序,Alpha192基于VWAP与volume时序排名的协方差,"$$\text{Alpha193} = \text{TS\_COV}(8, \text{VWAP}, \text{TS\_RANK}(\text{VOLUME}, 12))$$"
factor_259_xs,259,截面+时序,Alpha195基于amount-high协方差的Z-score截面排名，简化了横截面排名,"$$\text{Alpha197} = \text{RANK}(\text{TS\_COV}(9, \text{AMOUNT}, \text{HIGH}))$$"
factor_259,259,简化为时序,Alpha195基于amount-high协方差的Z-score截面排名，简化了横截面排名,"$$\text{Alpha197} = \text{RANK}(\text{TS\_COV}(9, \text{AMOUNT}, \text{HIGH}))$$"
factor_260,260,时序,Alpha202基于high差分与负成交量的滚动相关系数,"$$\text{Alpha202} = \text{TS\_CORR}(8, \text{DELTA}(\text{HIGH}, 6), -\text{VOLUME})$$"
factor_261,261,时序,Alpha203基于close、volume、vwap的Z-score组合与high差分的乘积,"$$\text{Alpha203} = (\text{GP\_MIN}(\text{CLOSE} - \text{VOLUME}, \text{GP\_MAX}(\text{VOLUME}, \text{VWAP}))) \cdot \text{DELTA}(\text{HIGH}, 8)$$"
factor_262,262,简化为时序,Alpha204基于负VWAP与amount差分的比值除以low-vwap最大值与volume协方差排名的比值，简化了横截面排名,"$$\text{Alpha205} = \frac{\frac{-\text{VWAP}}{\text{DELTA}(\text{AMOUNT}, 16)}}{\text{RANK}(\text{TS\_COV}(10, \text{GP\_MAX}(\text{LOW}, \text{VWAP}), \text{VOLUME}))}$$"
factor_262_xs,262,截面+时序,Alpha204基于负VWAP与amount差分的比值除以low-vwap最大值与volume协方差排名的比值，简化了横截面排名,"$$\text{Alpha205} = \frac{\frac{-\text{VWAP}}{\text{DELTA}(\text{AMOUNT}, 16)}}{\text{RANK}(\text{TS\_COV}(10, \text{GP\_MAX}(\text{LOW}, \text{VWAP}), \text{VOLUME}))}$$"
factor_264,264,时序,Alpha206基于low和VWAP百分比变化率的最小值,"$$\text{Alpha207} = \text{GP\_MIN}(\text{TS\_PCTCHG}(\text{LOW}, 4), \text{TS\_PCTCHG}(\text{VWAP}, 12))$$"
factor_265,265,时序,Alpha208基于VWAP、CLOSE和HIGH的组合逻辑，包含sigmoid变换,"$$\text{Alpha208} = \text{GP\_MAX}(\text{SIGMOID}(\text{GP\_MIN}(\text{VWAP}, \text{CLOSE})) - \frac{\text{VWAP}}{\text{HIGH}}, \text{TS\_PCTCHG}(\text{CLOSE}, 10))$$"
factor_266,266,简化为时序,Alpha210基于VWAP排名与成交金额排名的相关系数，简化了横截面排名,"$$\text{Alpha210} = \text{TS\_CORR}(12, \text{RANK}(\text{VWAP}), \text{RANK}(\text{AMOUNT}))$$"
factor_266_xs,266,截面+时序,Alpha210基于VWAP排名与成交金额排名的相关系数，简化了横截面排名,"$$\text{Alpha210} = \text{TS\_CORR}(12, \text{RANK}(\text{VWAP}), \text{RANK}(\text{AMOUNT}))$$"
factor_267,267,时序,Alpha211基于volume*close标准差最大值与amount均值协方差的最大值,"$$\text{Alpha211} = \text{TS\_MAX}(\text{TS\_COV}(14, \text{TS\_MAX}(\text{TS\_STD}(\text{VOLUME} \cdot \text{CLOSE}, 14), 8), \text{TS\_MEAN}(\text{AMOUNT}, 20)), 16)$$"
factor_268,268,时序,Alpha216基于每日成交金额和VWAP的12周期协方差,"$$\text{Alpha216} = \text{TS\_COV}(12, \text{AMOUNT}, \text{VWAP})$$"
factor_269,269,时序,Alpha217基于volume和VWAP相关系数的滚动最大值,"$$\text{Alpha217} = \text{TS\_MAX}(\text{TS\_CORR}(16, \text{VOLUME}, \text{VWAP}), 5)$$"
factor_270,270,时序,Alpha218基于arctan(volume)与volume-vwap相关系数乘积的对数绝对值与vwap差分最大值的平方根,"$$\text{Alpha218} = \text{SQRT}(\text{GP\_MAX}(\text{LOG}(\text{ARCTAN}(\text{VOLUME}) \cdot \text{TS\_CORR}(10, \text{VOLUME}, \text{VWAP})), \text{DELTA}(\text{VWAP}, 5)))$$"
factor_271,271,时序,Alpha220基于VWAP标准差Z-score、VOLUME最大值Z-score的最大值与HIGH标准差Z-score的和的标准差,"$$\text{Alpha220} = \text{TS\_STD}(\text{GP\_MAX}(\text{TS\_STD}(\text{VWAP}, 7), \text{TS\_MAX}(\text{VOLUME}, 10)) + \text{TS\_STD}(\text{HIGH}, 5), 14)$$"
factor_272,272,时序,Alpha221基于LOG(VWAP)的差分,"$$\text{Alpha221} = \text{DELTA}(\text{LOG}(\text{VWAP}), 8)$$"
factor_274_xs,274,截面+时序,Alpha223基于VWAP差分与amount排名最大值的最大值与VWAP-amount相关系数最小值的标准差，简化了横截面排名,"$$\text{Alpha225} = \text{TS\_STD}(\text{GP\_MIN}(\text{TS\_MAX}(\text{GP\_MAX}(\text{DELTA}(\text{VWAP}, 26), \text{RANK}(\text{AMOUNT})), 5), \text{TS\_CORR}(5, \text{VWAP}, \text{AMOUNT})), 12)$$"
factor_274,274,简化为时序,Alpha223基于VWAP差分与amount排名最大值的最大值与VWAP-amount相关系数最小值的标准差，简化了横截面排名,"$$\text{Alpha225} = \text{TS\_STD}(\text{GP\_MIN}(\text{TS\_MAX}(\text{GP\_MAX}(\text{DELTA}(\text{VWAP}, 26), \text{RANK}(\text{AMOUNT})), 5), \text{TS\_CORR}(5, \text{VWAP}, \text{AMOUNT})), 12)$$"
factor_277,277,时序,Alpha27基于volume和high的Z-score和与amount反正切乘积的对数绝对值，与close/open比值标准差减去low的Z-score的回归贝塔,"$$\text{Alpha27} = \text{ts_regbeta} \left( \text{log} \left( \text{mul} \left( \text{add}(\text{volume}, \text{high}), \text{arctan}(\text{amount}) \right) \right), \text{sub} \left( \text{ts_std} \left( \text{div}(\text{close}, \text{open_price}), 14 \right), \text{low} \right), 6 \right)$$"
factor_278,278,时序,Alpha28基于close/open比率的滚动均值,"$$\text{Alpha28} = \text{ts_mean} \left( \text{div}(\text{close}, \text{open_price}), 11 \right)$$"
factor_279,279,时序,Alpha30基于low和vwap最小值的差分除以open绝对值平方根与open绝对值的比值,"$$\text{Alpha30} = \text{div} \left( \text{delta}(\text{gp_min}(\text{low}, \text{vwap}), 6), \text{div}(\text{sqrt}(\text{open_price}), \text{abs}(\text{open_price})) \right)$$"
factor_280,280,时序,Alpha31基于uni_col绝对值标准差对uni_col的回归贝塔,"$$\text{Alpha32} = \text{ts_regbeta} \left( \text{close}, \text{ts_std}(\text{abs}(\text{close}), 6), 14 \right)$$"
factor_281,281,时序,Alpha33基于volume的时序排名与vwap的协方差,"$$\text{Alpha33} = \text{ts_cov} \left( 4, \text{vwap}, \text{ts_rank}(\text{volume}, 14) \right)$$"
factor_283,283,时序,Alpha36基于VWAP与volume滚动均值的相关系数,"$$\text{Alpha36} = \text{ts_corr} \left( 12, \text{vwap}, \text{ts_mean}(\text{volume}, 8) \right)$$"
factor_284,284,简化为时序,Alpha37基于amount-close协方差的Z-score截面排名，简化了横截面排名,"$$\text{Alpha37} = \text{rank}(\text{ts_cov}(6, \text{amount}, \text{close}))$$"
factor_284_xs,284,截面+时序,Alpha37基于amount-close协方差的Z-score截面排名，简化了横截面排名,"$$\text{Alpha37} = \text{rank}(\text{ts_cov}(6, \text{amount}, \text{close}))$$"
factor_285,285,时序,Alpha38基于close反正切与open百分比变化和volume差分最大值的相关系数,"$$\text{Alpha41} = \text{ts_corr} \left( 9, \text{arctan}(\text{close}), \text{gp_max} \left( \text{ts_pctchg}(\text{open_price}, 11), \text{delta}(\text{volume}, 9) \right) \right)$$"
factor_286,286,时序,Alpha42基于high差分与负volume的滚动相关系数,"$$\text{Alpha42} = \text{ts_corr}(5, \text{delta}(\text{high}, 4), \text{neg}(\text{volume}))$$"
factor_287,287,时序,Alpha43基于open、volume、vwap的Z-score组合与close差分的乘积,"$$\text{Alpha43} = \text{mul} \left( \text{gp_max} \left( \text{sub}(\text{open_price}, \text{volume}), \text{gp_max}(\text{volume}, \text{vwap}) \right), \text{delta}(\text{close}, 4) \right)$$"
factor_289,289,简化为时序,Alpha45基于负close除以amount均值与high-vwap最大值和volume协方差排名的比值，简化了横截面排名,"$$\text{Alpha45} = \text{div} \left( \text{div} \left( \text{neg}(\text{close}), \text{ts_mean}(\text{amount}, 13) \right), \text{rank} \left( \text{ts_cov} \left( 8, \text{gp_max}(\text{high}, \text{vwap}), \text{volume} \right) \right) \right)$$"
factor_289_xs,289,截面+时序,Alpha45基于负close除以amount均值与high-vwap最大值和volume协方差排名的比值，简化了横截面排名,"$$\text{Alpha45} = \text{div} \left( \text{div} \left( \text{neg}(\text{close}), \text{ts_mean}(\text{amount}, 13) \right), \text{rank} \left( \text{ts_cov} \left( 8, \text{gp_max}(\text{high}, \text{vwap}), \text{volume} \right) \right) \right)$$"
factor_290,290,时序,Alpha48基于low-amount最小值的sigmoid变换与vwap/close比值的差值和close百分比变化的最小值,"$$\text{Alpha48} = \text{gp_min} \left( \text{sub} \left( \text{sigmoid} \left( \text{gp_min}(\text{low}, \text{amount}) \right), \text{div}(\text{vwap}, \text{close}) \right), \text{ts_pctchg}(\text{close}, 5) \right)$$"
factor_295,295,时序,Alpha52基于amount最大值平方根与vwap相关系数和open差分的最小值,"$$\text{Alpha52} = \text{gp_min} \left( \text{ts_corr} \left( 13, \text{vwap}, \text{sqrt} \left( \text{ts_max}(\text{amount}, 6) \right) \right), \text{delta}(\text{open_price}, 2) \right)$$"
factor_298_xs,298,截面+时序,,"$$\text{Alpha55} = \text{ts_pctchg} \left( \text{gp_min} \left( \text{log}(\text{delay}(\text{amount}, 2)), \text{ts_regres} \left( \text{delay}(\text{vwap}, 5), \text{add}(\text{rank}(\text{volume}), \text{close}), 7 \right) \right), 13 \right)$$"
factor_299,299,时序,Alpha57基于low和volume-low最大值的Z-score和与log(vwap)百分比变化的相关系数,"$$\text{Alpha57} = \text{ts_corr} \left( 14, \text{add}(\text{low}, \text{gp_max}(\text{volume}, \text{low})), \text{ts_pctchg}(\text{log}(\text{vwap}), 5) \right)$$"
factor_300_xs,300,截面+时序,Alpha58基于延迟amount的对数与amount排名对high+close回归残差的最小值，简化了横截面排名和回归,"$$\text{Alpha58} = \text{gp_min} \left( \text{log}(\text{delay}(\text{amount}, 2)), \text{ts_regres} \left( \text{rank}(\text{amount}), \text{add}(\text{high}, \text{close}), 7 \right) \right)$$"
factor_300,300,简化为时序,Alpha58基于延迟amount的对数与amount排名对high+close回归残差的最小值，简化了横截面排名和回归,"$$\text{Alpha58} = \text{gp_min} \left( \text{log}(\text{delay}(\text{amount}, 2)), \text{ts_regres} \left( \text{rank}(\text{amount}), \text{add}(\text{high}, \text{close}), 7 \right) \right)$$"
factor_301,301,时序,Alpha5基于volume-amount协方差除以amount-high回归贝塔均值的比值,"$$\text{Alpha5} = \text{div} \left( \text{ts_cov}(9, \text{volume}, \text{amount}), \text{ts_mean} \left( \text{ts_regbeta}(\text{amount}, \text{high}, 13), 8 \right) \right)$$"
factor_302,302,时序,Alpha60基于volume平方根和open的Z-score最小值与volume最大值的负相关系数减去low和volume的Z-score最小值差分,"$$\text{Alpha60} = \text{sub} \left( \text{neg} \left( \text{ts_corr} \left( 11, \text{gp_min} \left( \text{sqrt}(\text{volume}), \text{open_price} \right), \text{ts_max}(\text{volume}, 10) \right) \right), \text{delta} \left( \text{gp_min}(\text{low}, \text{volume}), 5 \right) \right)$$"
factor_303,303,时序,Alpha61基于volume-close相关系数最大值与amount平方根和相关系数最大值延迟的乘积,"$$\text{Alpha61} = \text{mul} \left( \text{ts_max} \left( \text{ts_corr}(9, \text{volume}, \text{close}), 13 \right), \text{delay} \left( \text{gp_max} \left( \text{sqrt}(\text{amount}), \text{ts_corr}(9, \text{volume}, \text{close}) \right), 7 \right) \right)$$"
factor_304,304,时序,Alpha62基于amount最大值与vwap-amount回归贝塔最大值的最小值,"$$\text{Alpha66} = \text{gp_min} \left( \text{ts_max}(\text{amount}, 10), \text{ts_max} \left( \text{ts_regbeta}(\text{vwap}, \text{amount}, 12), 7 \right) \right)$$"
factor_305,305,时序,Alpha69基于vwap-volume相关系数均值减去open-high差值的Z-score,"$$\text{Alpha69} = \text{sub} \left( \text{ts_mean} \left( \text{ts_corr}(11, \text{vwap}, \text{volume}), 15 \right), \text{sub}(\text{open_price}, \text{high}) \right)$$"
factor_309,309,时序,Alpha76基于成交量绝对值与收盘价百分比变化率的滚动相关系数,"$$\text{Alpha76} = \text{ts_corr}(9, \text{abs}(\text{volume}), \text{ts_pctchg}(\text{close}, 14))$$"
factor_310,310,时序,Alpha78基于low差分的Z-score标准化与high反正切的和,"$$\text{Alpha78} = \text{add} \left( \text{delta}(\text{low}, 5), \text{arctan}(\text{high}) \right)$$"
factor_313_xs,313,截面+时序,Alpha80基于volume-close回归贝塔的tanh变换截面排名加上close-low回归贝塔的负值，简化了横截面排名,"$$\text{Alpha81} = \text{add} \left( \text{rank} \left( \text{ts_regbeta}(\text{volume}, \text{close}, 6) \right), \text{neg} \left( \text{ts_regbeta}(\text{close}, \text{low}, 14) \right) \right)$$"
factor_313,313,简化为时序,Alpha80基于volume-close回归贝塔的tanh变换截面排名加上close-low回归贝塔的负值，简化了横截面排名,"$$\text{Alpha81} = \text{add} \left( \text{rank} \left( \text{ts_regbeta}(\text{volume}, \text{close}, 6) \right), \text{neg} \left( \text{ts_regbeta}(\text{close}, \text{low}, 14) \right) \right)$$"
factor_314,314,简化为时序,Alpha82基于复杂的Z-score计算、截面排名和协方差，简化了横截面排名,"$$\text{Alpha82} = \text{ts_cov} \left( 6, \text{ts_rank} \left( \text{gp_max}(\text{low}, \text{volume}), 5 \right), \text{gp_max} \left( \text{div} \left( \text{rank}(\text{volume}), \text{mul}(\text{high}, \text{volume}) \right), \text{add}(\text{vwap}, \text{close}) \right) \right)$$"
factor_314_xs,314,截面+时序,Alpha82基于复杂的Z-score计算、截面排名和协方差，简化了横截面排名,"$$\text{Alpha82} = \text{ts_cov} \left( 6, \text{ts_rank} \left( \text{gp_max}(\text{low}, \text{volume}), 5 \right), \text{gp_max} \left( \text{div} \left( \text{rank}(\text{volume}), \text{mul}(\text{high}, \text{volume}) \right), \text{add}(\text{vwap}, \text{close}) \right) \right)$$"
factor_316,316,时序,Alpha90基于volume排名的sigmoid变换除以low-amount回归贝塔绝对值再乘以vwap,"$$\text{Alpha85} = \text{mul} \left( \text{vwap}, \text{div} \left( \text{sigmoid} \left( \text{ts_rank}(\text{volume}, 4) \right), \text{abs} \left( \text{ts_regbeta}(\text{amount}, \text{low}, 4) \right) \right) \right)$$"
factor_317,317,时序,VWAP百分比变化率因子,"$$\text{Alpha87} = \text{ts_pctchg}(\text{vwap}, 7)$$"
factor_318,318,时序,Alpha92基于close差分的Z-score减去log(volume)和low-volume相关系数最小值的负数,"$$\text{Alpha88} = \text{sub} \left( \text{delta}(\text{close}, 7), \text{neg} \left( \text{gp_min} \left( \text{log}(\text{volume}), \text{ts_corr}(10, \text{low}, \text{volume}) \right) \right) \right)$$"
factor_319_xs,319,截面+时序,Alpha93基于log(volume)乘以close截面排名差分，简化了横截面排名,"$$\text{Alpha89} = \text{mul} \left( \text{log}(\text{abs}(\text{volume})), \text{delta}(\text{rank}(\text{close}), 7) \right)$$<br/>注意：原始代码中 `log(abs(self.volume))`，`log` 函数已包含 `abs` 处理。"
factor_319,319,简化为时序,Alpha93基于log(volume)乘以close截面排名差分，简化了横截面排名,"$$\text{Alpha89} = \text{mul} \left( \text{log}(\text{abs}(\text{volume})), \text{delta}(\text{rank}(\text{close}), 7) \right)$$<br/>注意：原始代码中 `log(abs(self.volume))`，`log` 函数已包含 `abs` 处理。"
factor_321,321,时序,Alpha96基于vwap-amount协方差乘以low均值与amount绝对值标准差的相关系数,"$$\text{Alpha96} = \text{ts_corr} \left( 11, \text{mul} \left( \text{ts_cov}(7, \text{vwap}, \text{amount}), \text{ts_mean}(\text{low}, 12) \right), \text{ts_std}(\text{abs}(\text{amount}), 6) \right)$$"
factor_323,323,时序,Alpha101基于amount和low的Z-score tanh变换和与open Z-score回归贝塔和volume-vwap回归贝塔的最小值,"$$\text{Alpha98} = \text{gp_min} \left( \text{ts_regbeta} \left( \text{add}(\text{amount}, \text{low}), \text{rank}(\text{open_price}), 15 \right), \text{ts_regbeta}(\text{volume}, \text{vwap}, 9) \right)$$"
factor_324,324,时序,Alpha99基于volume平方根与close排名和vwap最大值的协方差和high-vwap协方差对open-close最小值回归贝塔的最小值,"$$\text{Alpha99} = \text{gp_min} \left( \text{ts_cov} \left( 6, \text{sqrt}(\text{volume}), \text{gp_max} \left( \text{ts_rank}(\text{close}, 8), \text{vwap} \right) \right), \text{ts_regbeta} \left( \text{ts_cov}(10, \text{high}, \text{vwap}), \text{gp_min}(\text{open_price}, \text{close}), 14 \right) \right)$$"
factor_325,325,时序,Alpha106基于amount差分与volume-low相关系数和close Z-score和的相关系数,"$$\text{Alpha106} = \text{ts_corr} \left( 15, \text{delta}(\text{amount}, 10), \text{add} \left( \text{ts_corr}(6, \text{volume}, \text{low}), \text{close} \right) \right)$$"
factor_326,326,时序,Alpha107基于volume-high最大值和low的Z-score和与log(vwap)百分比变化的相关系数,"$$\text{Alpha107} = \text{ts_corr} \left( 15, \text{add}(\text{low}, \text{gp_max}(\text{volume}, \text{high})), \text{ts_pctchg}(\text{log}(\text{vwap}), 8) \right)$$"
factor_328,328,时序,Alpha109基于延迟amount的对数与延迟vwap对vwap+high+close回归残差Z-score的最大值,"$$\text{Alpha109} = \text{gp_max} \left( \text{log}(\text{delay}(\text{amount}, 5)), \text{ts_regres} \left( \text{delay}(\text{vwap}, 10), \text{add} \left( \text{add}(\text{vwap}, \text{high}), \text{close} \right), 16 \right) \right)$$"
factor_329,329,时序,Alpha110基于volume平方根与open最小值和volume最大值相关系数的负数加上low-volume最小值差分的Z-score,"$$\text{Alpha110} = \text{add} \left( \text{neg} \left( \text{ts_corr} \left( 18, \text{gp_min} \left( \text{sqrt}(\text{volume}), \text{open_price} \right), \text{ts_max}(\text{volume}, 12) \right) \right), \text{delta} \left( \text{gp_min}(\text{low}, \text{volume}), 6 \right) \right)$$"
factor_330,330,时序,Alpha113基于延迟low对open*close乘积的回归残差,"$$\text{Alpha113} = \text{ts_regres} \left( \text{delay}(\text{low}, 20), \text{mul}(\text{open_price}, \text{close}), 20 \right)$$"
factor_332,332,时序,Alpha117基于vwap-open差值的双重时序排名对vwap平方根反正切的回归残差,"$$\text{Alpha117} = \text{ts_regres} \left( \text{ts_rank} \left( \text{ts_rank} \left( \text{sub}(\text{vwap}, \text{open_price}), 18 \right), 9 \right), \text{arctan} \left( \text{sqrt}(\text{vwap}) \right), 8 \right)$$"
factor_333,333,时序,Alpha118基于high百分比变化率与volume最小值Z-score的最大值乘以low,"$$\text{Alpha118} = \text{mul} \left( \text{low}, \text{gp_max} \left( \text{ts_pctchg}(\text{high}, 9), \text{ts_min}(\text{volume}, 12) \right) \right)$$"
factor_335_xs,335,截面+时序,Alpha125基于amount-vwap协方差与amount-open回归贝塔均值最小值的Z-score截面排名乘以amount差分除以close标准差的最小值，简化了横截面排名,"$$\text{Alpha125} = \text{mul} \left( \text{rank} \left( \text{gp_min} \left( \text{ts_cov}(15, \text{amount}, \text{vwap}), \text{ts_mean} \left( \text{ts_regbeta}(\text{amount}, \text{open_price}, 18), 6 \right) \right) \right), \text{ts_min} \left( \text{div} \left( \text{delta}(\text{amount}, 5), \text{ts_std}(\text{close}, 10) \right), 12 \right) \right)$$"
factor_335,335,简化为时序,Alpha125基于amount-vwap协方差与amount-open回归贝塔均值最小值的Z-score截面排名乘以amount差分除以close标准差的最小值，简化了横截面排名,"$$\text{Alpha125} = \text{mul} \left( \text{rank} \left( \text{gp_min} \left( \text{ts_cov}(15, \text{amount}, \text{vwap}), \text{ts_mean} \left( \text{ts_regbeta}(\text{amount}, \text{open_price}, 18), 6 \right) \right) \right), \text{ts_min} \left( \text{div} \left( \text{delta}(\text{amount}, 5), \text{ts_std}(\text{close}, 10) \right), 12 \right) \right)$$"
factor_337,337,时序,Alpha130基于vwap滚动均值与high-amount滚动相关系数的乘积,"$$\text{Alpha130} = \text{mul} \left( \text{ts_mean}(\text{vwap}, 8), \text{ts_corr}(12, \text{high}, \text{amount}) \right)$$"
factor_338,338,时序,Alpha135基于volume排名的sigmoid变换除以amount-high回归贝塔绝对值再乘以vwap,"$$\text{Alpha135} = \text{mul} \left( \text{vwap}, \text{div} \left( \text{sigmoid} \left( \text{ts_rank}(\text{volume}, 6) \right), \text{abs} \left( \text{ts_regbeta}(\text{amount}, \text{high}, 6) \right) \right) \right)$$"
factor_339_xs,339,截面+时序,Alpha139基于log(abs(volume))乘以open截面排名的差分，简化了横截面排名,"$$\text{Alpha139} = \text{mul} \left( \text{log}(\text{abs}(\text{volume})), \text{delta}(\text{rank}(\text{open_price}), 9) \right)$$<br/>注意：原始代码中 `log(abs(self.volume))`，`log` 函数已包含 `abs` 处理。"
factor_339,339,简化为时序,Alpha139基于log(abs(volume))乘以open截面排名的差分，简化了横截面排名,"$$\text{Alpha139} = \text{mul} \left( \text{log}(\text{abs}(\text{volume})), \text{delta}(\text{rank}(\text{open_price}), 9) \right)$$<br/>注意：原始代码中 `log(abs(self.volume))`，`log` 函数已包含 `abs` 处理。"
factor_340_xs,340,截面+时序,Alpha140基于复杂的延迟、排名、反正切、百分比变化、最小值、回归残差等计算，简化了横截面排名,"$$\text{Alpha140} = \text{ts_regres} \left( \text{arctan} \left( \text{delay} \left( \text{ts_pctchg} \left( \text{gp_min} \left( \text{delay}(\text{open_price}, 16), \text{ts_pctchg} \left( \text{arctan}(\text{rank}(\text{vwap})), 12 \right) \right), 8 \right), 5 \right) \right), \text{neg}(\text{close}), 12 \right)$$"
factor_340,340,简化为时序,Alpha140基于复杂的延迟、排名、反正切、百分比变化、最小值、回归残差等计算，简化了横截面排名,"$$\text{Alpha140} = \text{ts_regres} \left( \text{arctan} \left( \text{delay} \left( \text{ts_pctchg} \left( \text{gp_min} \left( \text{delay}(\text{open_price}, 16), \text{ts_pctchg} \left( \text{arctan}(\text{rank}(\text{vwap})), 12 \right) \right), 8 \right), 5 \right) \right), \text{neg}(\text{close}), 12 \right)$$"
factor_341,341,时序,Alpha145基于close-amount回归贝塔最大值的Z-score加上log(amount)的Z-score,"$$\text{Alpha145} = \text{add} \left( \text{ts_max} \left( \text{ts_regbeta}(\text{close}, \text{amount}, 18), 9 \right), \text{log}(\text{amount}) \right)$$"
factor_342,342,时序,Alpha148基于amount和vwap的Z-score和与open余弦值的回归贝塔和volume-vwap回归贝塔的最大值,"$$\text{Alpha148} = \text{gp_max} \left( \text{ts_regbeta} \left( \text{add}(\text{amount}, \text{vwap}), \text{rank}(\text{open_price}), 18 \right), \text{ts_regbeta}(\text{volume}, \text{vwap}, 12) \right)$$"
factor_345,345,时序,Alpha159基于amount最大值反正切与close-amount回归贝塔双重排名对low的回归残差,"$$\text{Alpha159} = \text{ts_regres} \left( \text{mul} \left( \text{arctan} \left( \text{ts_max}(\text{amount}, 10) \right), \text{ts_rank} \left( \text{ts_rank} \left( \text{ts_regbeta}(\text{close}, \text{amount}, 12), 10 \right), 8 \right) \right), \text{low}, 7 \right)$$"
factor_346_xs,346,截面+时序,Alpha15因子：基于vwap差值标准化、amount排名、协方差等的复合计算，简化了截面排名,"$$\text{Alpha15} = \text{ts_std} \left( \text{gp_max} \left( \text{ts_max} \left( \text{gp_max} \left( \text{delta}(\text{vwap}, 15), \text{rank}(\text{amount}) \right), 9 \right), \text{ts_cov}(9, \text{vwap}, \text{amount}) \right), 14 \right)$$"
factor_346,346,简化为时序,Alpha15因子：基于vwap差值标准化、amount排名、协方差等的复合计算，简化了截面排名,"$$\text{Alpha15} = \text{ts_std} \left( \text{gp_max} \left( \text{ts_max} \left( \text{gp_max} \left( \text{delta}(\text{vwap}, 15), \text{rank}(\text{amount}) \right), 9 \right), \text{ts_cov}(9, \text{vwap}, \text{amount}) \right), 14 \right)$$"
factor_347,347,时序,Alpha179基于close-low回归贝塔对close的回归残差,"$$Alpha_{179} = \text{ts_regres}(\text{ts_regbeta}(\text{close}, \text{low}, 14), \text{close}, 5)$$"
factor_349_xs,349,截面+时序,Alpha183基于close的二阶差分Z-score加上amount截面排名的滚动最大值，简化了横截面排名,"$$Alpha_{183} = \text{add}(\text{delta}(\text{delta}(\text{close}, 16), 5), \text{ts_max}(\text{rank}(\text{amount}), 14))$$"
factor_349,349,简化为时序,Alpha183基于close的二阶差分Z-score加上amount截面排名的滚动最大值，简化了横截面排名,"$$Alpha_{183} = \text{add}(\text{delta}(\text{delta}(\text{close}, 16), 5), \text{ts_max}(\text{rank}(\text{amount}), 14))$$"
factor_350,350,简化为时序,Alpha184基于volume反正切余弦值的截面排名差分加上log(volume)的Z-score，简化了横截面排名,"$$Alpha_{184} = \text{add}(\text{delta}(\text{rank}(\text{arctan}(\text{volume})), 5), \text{ts_std}(\text{log}(\text{volume}), 10))$$"
factor_350_xs,350,截面+时序,Alpha184基于volume反正切余弦值的截面排名差分加上log(volume)的Z-score，简化了横截面排名,"$$Alpha_{184} = \text{add}(\text{delta}(\text{rank}(\text{arctan}(\text{volume})), 5), \text{ts_std}(\text{log}(\text{volume}), 10))$$"
factor_353,353,时序,Alpha189基于vwap-amount回归贝塔的滚动最大值,"$$Alpha_{189} = \text{ts_max}(\text{ts_regbeta}(\text{vwap}, \text{amount}, 6), 12)$$"
factor_354,354,时序,Alpha18基于延迟open对close的回归残差,"$$\text{Alpha18} = \text{ts_regres}(\text{delay}(\text{open_price}, 8), \text{close}, 7)$$"
factor_355,355,时序,Alpha192基于high对close绝对值标准差的回归贝塔,"$$\text{Alpha192} = \text{TS\_REGBETA}(\text{HIGH}, \text{TS\_STD}(\text{ABS}(\text{CLOSE}), 8), 18)$$"
factor_356,356,时序,Alpha196基于close与volume滚动均值的滚动相关系数,"$$\text{Alpha196} = \text{TS\_CORR}(14, \text{CLOSE}, \text{TS\_MEAN}(\text{VOLUME}, 6))$$"
factor_358,358,时序,Alpha199基于volume的Z-score加上amount-vwap协方差均值的Z-score,"$$\text{Alpha199} = \text{VOLUME} + \text{TS\_MEAN}(\text{TS\_COV}(8, \text{AMOUNT}, \text{VWAP}), 6)$$"
factor_359,359,时序,Alpha19基于open-low回归贝塔对close的回归残差,"$$\text{Alpha19} = \text{ts_regres} \left( \text{ts_regbeta}(\text{open_price}, \text{low}, 12), \text{close}, 7 \right)$$"
factor_360,360,时序,Alpha1基于volume*vwap标准差最大值与amount均值的协方差的滚动最大值,"$$\text{Alpha1} = \text{ts_max} \left( \text{ts_cov} \left( 8, \text{ts_max} \left( \text{ts_std} \left( \text{mul}(\text{volume}, \text{vwap}), 10 \right), 5 \right), \text{ts_mean}(\text{amount}, 12) \right), 11 \right)$$"
factor_361,361,时序,Alpha201基于low反正切与open百分比变化和volume差分最小值的滚动相关系数,"$$\text{Alpha201} = \text{TS\_CORR}(16, \text{ARCTAN}(\text{LOW}), \text{GP\_MIN}(\text{TS\_PCTCHG}(\text{OPEN}, 12), \text{DELTA}(\text{VOLUME}, 10)))$$"
factor_366,366,时序,Alpha214基于low最小值与amount均值协方差的滚动最小值,"$$\text{Alpha214} = \text{TS\_MIN}(\text{TS\_COV}(12, \text{TS\_MIN}(\text{LOW}, 7), \text{TS\_MEAN}(\text{AMOUNT}, 26)), 16)$$"
factor_367,367,时序,Alpha215基于volume-amount协方差除以amount-low回归贝塔均值,"$$\text{Alpha215} = \frac{\text{TS\_COV}(12, \text{VOLUME}, \text{AMOUNT})}{\text{TS\_MEAN}(\text{TS\_REGBETA}(\text{AMOUNT}, \text{LOW}, 16), 8)}$$"
factor_368,368,时序,Alpha219基于vwap sigmoid与volume差分最小值和low百分比变化的最大值,"$$\text{Alpha219} = \text{GP\_MAX}(\text{GP\_MIN}(\text{SIGMOID}(\text{VWAP}), \text{DELTA}(\text{VOLUME}, 2)), \text{TS\_PCTCHG}(\text{LOW}, 5))$$"
factor_369,369,时序,Alpha21基于vwap与volume标准差最小值除以low滚动最大值,"$$\text{Alpha21} = \text{div} \left( \text{gp_min} \left( \text{vwap}, \text{ts_std}(\text{volume}, 8) \right), \text{ts_max}(\text{low}, 8) \right)$$"
factor_371,371,时序,Alpha224基于volume对数与open-close最小值的滚动相关系数,"$$\text{Alpha224} = \text{TS\_CORR}(16, \text{LOG}(\text{VOLUME}), \text{GP\_MIN}(\text{OPEN}, \text{CLOSE}))$$"
factor_372_xs,372,截面+时序,Alpha23基于close二阶差分Z-score加上amount截面排名滚动最大值，简化了横截面排名,"$$\text{Alpha23} = \text{add} \left( \text{delta}(\text{delta}(\text{close}, 15), 4), \text{ts_max}(\text{rank}(\text{amount}), 13) \right)$$"
factor_372,372,简化为时序,Alpha23基于close二阶差分Z-score加上amount截面排名滚动最大值，简化了横截面排名,"$$\text{Alpha23} = \text{add} \left( \text{delta}(\text{delta}(\text{close}, 15), 4), \text{ts_max}(\text{rank}(\text{amount}), 13) \right)$$"
factor_373,373,简化为时序,Alpha24基于volume反正切截面排名差分加上log(volume)标准差，简化了横截面排名,"$$\text{Alpha24} = \text{add} \left( \text{delta}(\text{rank}(\text{arctan}(\text{volume})), 6), \text{ts_std}(\text{log}(\text{volume}), 9) \right)$$"
factor_373_xs,373,截面+时序,Alpha24基于volume反正切截面排名差分加上log(volume)标准差，简化了横截面排名,"$$\text{Alpha24} = \text{add} \left( \text{delta}(\text{rank}(\text{arctan}(\text{volume})), 6), \text{ts_std}(\text{log}(\text{volume}), 9) \right)$$"
factor_375,375,时序,Alpha35基于amount-close相关系数滚动最大值的负数,"$$\text{Alpha35} = \text{neg} \left( \text{ts_max} \left( \text{ts_corr}(14, \text{amount}, \text{close}), 13 \right) \right)$$"
factor_376,376,时序,Alpha38基于vwap对log(volume)的回归贝塔系数,"$$\text{Alpha38} = \text{ts_regbeta}(\text{log}(\text{volume}), \text{vwap}, 8)$$"
factor_378,378,时序,Alpha3基于vwap差分Z-score减去负close排名平方根,"$$\text{Alpha3} = \text{sub} \left( \text{delta}(\text{vwap}, 5), \text{sqrt} \left( \text{ts_rank} \left( \text{neg}(\text{close}), 8 \right) \right) \right)$$"
factor_379,379,简化为时序,Alpha40基于vwap截面排名与volume的滚动相关系数，简化了横截面排名,"$$\text{Alpha40} = \text{ts_corr}(9, \text{rank}(\text{vwap}), \text{volume})$$"
factor_379_xs,379,截面+时序,Alpha40基于vwap截面排名与volume的滚动相关系数，简化了横截面排名,"$$\text{Alpha40} = \text{ts_corr}(9, \text{rank}(\text{vwap}), \text{volume})$$"
factor_383,383,时序,Alpha62基于log(volume)-low对log(low+close)的回归贝塔系数,"$$\text{Alpha62} = \text{ts_regbeta} \left( \text{sub}(\text{log}(\text{volume}), \text{low}), \text{log} \left( \text{add}(\text{low}, \text{close}) \right), 6 \right)$$"
factor_384,384,时序,Alpha63基于延迟high对open*close乘积的回归残差,"$$\text{Alpha63} = \text{ts_regres} \left( \text{delay}(\text{high}, 10), \text{mul}(\text{open_price}, \text{close}), 10 \right)$$"
factor_386,386,时序,Alpha65基于volume和close的Z-score和与close百分比变化平方根的滚动相关系数,"$$\text{Alpha65} = \text{ts_corr} \left( 7, \text{add}(\text{volume}, \text{close}), \text{sqrt} \left( \text{ts_pctchg}(\text{close}, 5) \right) \right)$$"
factor_387,387,时序,Alpha67基于vwap-close差值的双重排名对vwap平方根反正切的回归残差,"$$\text{Alpha67} = \text{ts_regres} \left( \text{ts_rank} \left( \text{ts_rank} \left( \text{sub}(\text{vwap}, \text{close}), 14 \right), 7 \right), \text{arctan} \left( \text{sqrt}(\text{vwap}) \right), 6 \right)$$"
factor_390,390,简化为时序,Alpha72基于amount-close滚动相关系数的截面排名，简化了横截面排名,"$$\text{Alpha72} = \text{rank}(\text{ts_corr}(4, \text{amount}, \text{close}))$$"
factor_390_xs,390,截面+时序,Alpha72基于amount-close滚动相关系数的截面排名，简化了横截面排名,"$$\text{Alpha72} = \text{rank}(\text{ts_corr}(4, \text{amount}, \text{close}))$$"
factor_391,391,时序,基于成交量标准差和回归贝塔系数乘积的反正切因子,"$$\text{Alpha73} = \text{arctan} \left( \text{mul} \left( \text{ts_std}(\text{volume}, 14), \text{ts_regbeta}(\text{amount}, \text{vwap}, 10) \right) \right)$$"
factor_392,392,简化为时序,Alpha75基于amount-vwap协方差与amount-low回归贝塔均值最小值的Z-score截面排名乘以amount差分除以close标准差的最小值，简化了横截面排名,"$$\text{Alpha75} = \text{mul} \left( \text{rank} \left( \text{gp_min} \left( \text{ts_cov}(11, \text{amount}, \text{vwap}), \text{ts_mean} \left( \text{ts_regbeta}(\text{amount}, \text{low}, 15), 4 \right) \right) \right), \text{ts_min} \left( \text{div} \left( \text{delta}(\text{amount}, 2), \text{ts_std}(\text{close}, 8) \right), 9 \right) \right)$$"
factor_392_xs,392,截面+时序,Alpha75基于amount-vwap协方差与amount-low回归贝塔均值最小值的Z-score截面排名乘以amount差分除以close标准差的最小值，简化了横截面排名,"$$\text{Alpha75} = \text{mul} \left( \text{rank} \left( \text{gp_min} \left( \text{ts_cov}(11, \text{amount}, \text{vwap}), \text{ts_mean} \left( \text{ts_regbeta}(\text{amount}, \text{low}, 15), 4 \right) \right) \right), \text{ts_min} \left( \text{div} \left( \text{delta}(\text{amount}, 2), \text{ts_std}(\text{close}, 8) \right), 9 \right) \right)$$"
factor_394,394,时序,Alpha79基于复杂的vwap、amount计算的最小值,"$$\text{Alpha79} = \text{gp_min} \left( \text{div} \left( \text{vwap}, \text{sub} \left( \text{ts_min}(\text{neg}(\text{vwap}), 11), \text{abs}(\text{amount}) \right) \right), \text{delta}(\text{vwap}, 5) \right)$$"
factor_399,399,时序,Alpha91基于延迟amount乘以close差分,"$$\text{Alpha91} = \text{mul} \left( \text{delay}(\text{amount}, 7), \text{delta}(\text{close}, 7) \right)$$"
factor_400,400,时序,Alpha92基于复杂的low-open最小值与low标准差相关系数、amount Z-score、volume反正切等计算,"$$\text{Alpha92} = \text{div} \left( \text{sub} \left( \text{low}, \text{sub} \left( \text{ts_std} \left( \text{ts_corr} \left( 7, \text{gp_min}(\text{low}, \text{open_price}), \text{ts_std}(\text{low}, 5) \right), 6 \right), \text{div} \left( \text{sub}(\text{amount}, \text{volume}), \text{arctan}(\text{volume}) \right) \right) \right), \text{ts_std}(\text{low}, 5) \right)$$"
factor_401,401,时序,Alpha93基于high-low回归贝塔系数，然后close除以该贝塔，最后计算对close的回归残差,"$$\text{Alpha93} = \text{ts_regres} \left( \text{div} \left( \text{close}, \text{ts_regbeta}(\text{high}, \text{low}, 14) \right), \text{close}, 10 \right)$$"
factor_402,402,时序,Alpha95基于close-amount回归贝塔最大值的Z-score减去log(amount)的Z-score,"$$\text{Alpha95} = \text{sub} \left( \text{ts_max} \left( \text{ts_regbeta}(\text{close}, \text{amount}, 15), 5 \right), \text{log}(\text{amount}) \right)$$"
factor_403,403,时序,Alpha9基于vwap sigmoid与volume差分最大值和high百分比变化的最小值,"$$\text{Alpha9} = \text{gp_min} \left( \text{gp_max} \left( \text{sigmoid}(\text{vwap}), \text{delta}(\text{volume}, 3) \right), \text{ts_pctchg}(\text{high}, 4) \right)$$"
factor_406,406,时序,SAREXT抛物线转向指标扩展版，使用Numba优化,"SAREXT的计算是迭代的，依赖于前一期的状态。其核心思想是，SAR点会朝着当前趋势的方向移动，移动速度由加速因子(AF)控制。当价格触及或穿透SAR点时，趋势发生反转。<br/><br/>**基本迭代公式:**<br/>1.  **下一期SAR的计算 (无趋势反转时):**<br/>    $SAR_{t+1} = SAR_t + AF_t \times (EP_t - SAR_t)$<br/><br/>    其中：<br/>    *   $SAR_{t+1}$ 是为 $t+1$ 期计算的SAR值。<br/>    *   $SAR_t$ 是当前 $t$ 期的SAR值 (在没有反转的情况下，它是上一期计算出的 $SAR_{t \to current}$；如果发生反转，它是反转SAR值)。<br/>    *   $AF_t$ 是当前 $t$ 期的加速因子。<br/>    *   $EP_t$ 是当前 $t$ 期的极点价格 (Extreme Point)。<br/><br/>2.  **加速因子 (AF) 的更新:**<br/>    *   **上涨趋势中:** 如果当期最高价 $H_t > EP_{t-1}$ (创出新高):<br/>        $AF_t = \min(AF_{t-1} + \text{AFLongStep}, \text{AFMaxLong})$<br/>    *   **下跌趋势中:** 如果当期最低价 $L_t < EP_{t-1}$ (创出新低):<br/>        $AF_t = \min(AF_{t-1} + \text{AFShortStep}, \text{AFMaxShort})$<br/>    *   若未创出新的极点价格，则 $AF_t = AF_{t-1}$。<br/>    *   趋势反转时，AF会重置为对应的初始加速因子 (`AFInitLong` 或 `AFInitShort`)。<br/><br/>3.  **趋势反转时的SAR值:**<br/>    *   **从上涨趋势反转为下跌趋势时:** 初始反转SAR值设为前一上涨趋势的极高点 $EP_{long, t-1}$。<br/>        $SAR_{reversal} = EP_{long, t-1}$<br/>        然后根据参数调整： $SAR_{reversal} = SAR_{reversal} \times (1 + \text{OffsetOnReverse})$<br/>    *   **从下跌趋势反转为上涨趋势时:** 初始反转SAR值设为前一下跌趋势的极低点 $EP_{short, t-1}$。<br/>        $SAR_{reversal} = EP_{short, t-1}$<br/>        然后根据参数调整： $SAR_{reversal} = SAR_{reversal} \times (1 - \text{OffsetOnReverse})$<br/><br/>4.  **SAR值的边界限制:**<br/>    *   **上涨趋势中:** 计算出的 $SAR_{t+1}$ 不能高于 $L_{t-1}$ 和 $L_t$。<br/>        $SAR_{t+1} = \min(SAR_{t+1, calculated}, L_{t-1}, L_t)$<br/>        反转到下跌趋势时，反转SAR值不能低于 $H_{t-1}$ 和 $H_t$。<br/>        $SAR_{reversal} = \max(SAR_{reversal, calculated}, H_{t-1}, H_t)$<br/>    *   **下跌趋势中:** 计算出的 $SAR_{t+1}$ 不能低于 $H_{t-1}$ 和 $H_t$。<br/>        $SAR_{t+1} = \max(SAR_{t+1, calculated}, H_{t-1}, H_t)$<br/>        反转到上涨趋势时，反转SAR值不能高于 $L_{t-1}$ 和 $L_t$。<br/>        $SAR_{reversal} = \min(SAR_{reversal, calculated}, L_{t-1}, L_t)$"
factor_407,407,基础字段,SIN正弦函数因子,对于输入序列中的每一个值 $X_t$，其对应的因子值为：<br/><br/>$SIN_t = \sin(X_t)$<br/><br/>其中：<br/>*   $SIN_t$ 是在时刻 $t$ 的正弦函数因子值。<br/>*   $\sin(\cdot)$ 是标准的三角正弦函数。<br/>*   $X_t$ 是在时刻 $t$ 的输入数据。
factor_413,413,时序,FastK快速随机指标K值，使用Numba优化,对于每一个时间点 $t$：<br/>$ FastK_t = \begin{cases} \frac{C_t - L_n}{H_n - L_n} \times 100 & \text{if } H_n - L_n \neq 0 \\ 0 & \text{if } H_n - L_n = 0 \end{cases} $<br/><br/>其中：<br/>*   $C_t$ 是在时间点 $t$ 的收盘价。<br/>*   $L_n$ 是在过去 $n$ 个周期（包括当前周期 $t$）内的最低价。<br/>*   $H_n$ 是在过去 $n$ 个周期（包括当前周期 $t$）内的最高价。<br/>*   $n$ 是计算 FastK 值所选定的时间窗口长度。
factor_415,415,时序,StochRSI K随机相对强弱指标K值，使用Numba优化,"计算 StochRSIK 分为两步：<br/>1.  计算相对强弱指数 (RSI):<br/>    a.  计算价格变化:<br/>        $ \text{Change}_t = P_t - P_{t-1} $<br/>    b.  分离上涨和下跌:<br/>        $ U_t = \begin{cases} \text{Change}_t & \text{if } \text{Change}_t > 0 \\ 0 & \text{if } \text{Change}_t \le 0 \end{cases} $<br/>        $ D_t = \begin{cases} |\text{Change}_t| & \text{if } \text{Change}_t < 0 \\ 0 & \text{if } \text{Change}_t \ge 0 \end{cases} $<br/>    c.  计算平均上涨和平均下跌 (通常使用 Wilder's 平滑法):<br/>        对于第一个周期 $N_{RSI}$（例如14天）：<br/>        $ \text{AvgU}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} U_i $<br/>        $ \text{AvgD}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} D_i $<br/>        对于后续周期 $t > N_{RSI}$:<br/>        $ \text{AvgU}_t = \frac{(\text{AvgU}_{t-1} \times (N_{RSI}-1)) + U_t}{N_{RSI}} $<br/>        $ \text{AvgD}_t = \frac{(\text{AvgD}_{t-1} \times (N_{RSI}-1)) + D_t}{N_{RSI}} $<br/>    d.  计算相对强度 (RS):<br/>        $ RS_t = \frac{\text{AvgU}_t}{\text{AvgD}_t} $<br/>        (如果 $ \text{AvgD}_t = 0 $, 则 $RS_t$ 视为极大值，通常导致 $RSI_t = 100$)<br/>    e.  计算 RSI:<br/>        $ RSI_t = 100 - \frac{100}{1 + RS_t} $<br/>        (如果 $ \text{AvgD}_t = 0 $, 则 $RSI_t = 100$)<br/><br/>2.  基于 RSI 值计算随机震荡指标的 %K 值 (StochRSIK):<br/>    $ \text{StochRSIK}_t = 100 \times \frac{RSI_t - \min(RSI_{t-N_K+1}, \dots, RSI_t)}{\max(RSI_{t-N_K+1}, \dots, RSI_t) - \min(RSI_{t-N_K+1}, \dots, RSI_t)} $<br/>    (如果分母为0, 即 $ \max(RSI) = \min(RSI) $, 则 $ \text{StochRSIK}_t = 0 $)"
factor_417,417,基础字段,SUB向量算术减法因子，计算high-low,"对于两个输入时间序列 $I_1$ 和 $I_2$，在任意时间点 $t$，输出序列 $O$ 的计算公式如下：<br/><br/>$O_t = I_{1,t} - I_{2,t}$<br/><br/>其中：<br/>*   $O_t$ 是在时间点 $t$ 的输出因子值。<br/>*   $I_{1,t}$ 是第一个输入序列在时间点 $t$ 的值。<br/>*   $I_{2,t}$ 是第二个输入序列在时间点 $t$ 的值。"
factor_420,420,基础字段,TAN正切函数因子,该因子计算输入序列中每个数据点的三角正切值。<br/><br/>数学表达式为：<br/>$Y_t = \tan(X_t)$<br/><br/>其中：<br/>*   $Y_t$ 表示在时刻 $t$ 的因子值。<br/>*   $X_t$ 表示在时刻 $t$ 的输入值。<br/>*   $\tan$ 表示三角函数中的正切函数。
factor_422,422,时序,Aroon Up阿隆上升指标，使用Numba优化,对于阿隆上升 (Aroon Up):<br/>\[ \text{AroonUp}_t = \left( \frac{N - (\text{当前周期位置} - \text{N周期内最高价位置})}{N} \right) \times 100 \]<br/>或者更直观地：<br/>\[ \text{AroonUp}_t = \left( \frac{N - \text{自N周期内最高价出现以来经过的周期数}}{N} \right) \times 100 \]
factor_428,428,时序,ULTOSC终极振荡指标，使用Numba优化,"终极振荡指标 ($ULTOSC_t$) 的计算公式如下：<br/><br/>$ULTOSC_t = 100 \times \frac{ \left( 4 \times \frac{\sum_{i=0}^{P1-1} BP_{t-i}}{\sum_{i=0}^{P1-1} TR_{t-i}} \right) + \left( 2 \times \frac{\sum_{i=0}^{P2-1} BP_{t-i}}{\sum_{i=0}^{P2-1} TR_{t-i}} \right) + \left( 1 \times \frac{\sum_{i=0}^{P3-1} BP_{t-i}}{\sum_{i=0}^{P3-1} TR_{t-i}} \right) }{4+2+1}$<br/><br/>其中：<br/>$BP_k = C_k - TL_k$<br/>$TL_k = \min(L_k, C_{k-1})$<br/>$TR_k = \max( (H_k - L_k), |H_k - C_{k-1}|, |L_k - C_{k-1}| )$<br/><br/>如果某个周期的 $\sum TR_{t-i}$ 为0，则该周期的比率项 $\frac{\sum BP_{t-i}}{\sum TR_{t-i}}$ 在分子加权和中贡献为0。"
factor_429,429,时序,VAR方差因子，使用Numba优化,对于时间序列数据 \(X\)，在给定lookback周期 \(N\) 内，时间点 \(t\) 的方差 \( \text{VAR}_t \) 计算如下：<br/><br/>\[ \text{VAR}_t = \frac{1}{N} \sum_{i=t-N+1}^{t} (X_i - \bar{X}_t)^2 \]<br/><br/>其中 \(\bar{X}_t\) 是周期 \(N\) 内数据 \(X\) 的算术平均值：<br/>\[ \bar{X}_t = \frac{1}{N} \sum_{i=t-N+1}^{t} X_i \]<br/><br/>为了计算效率，通常使用以下等价公式：<br/>\[ \text{VAR}_t = \left( \frac{1}{N} \sum_{i=t-N+1}^{t} X_i^2 \right) - \left( \frac{1}{N} \sum_{i=t-N+1}^{t} X_i \right)^2 \]<br/>或者写作：<br/>\[ \text{VAR}_t = E[X^2]_t - (E[X]_t)^2 \]<br/>其中 \(E[X^2]_t\) 是 \(X^2\) 在周期 \(N\) 内的均值，\(E[X]_t\) 是 \(X\) 在周期 \(N\) 内的均值。
factor_433,433,时序,Aroon Down阿隆下降指标，使用Numba优化,对于阿隆下降 (Aroon Down):<br/>\[ \text{AroonDown}_t = \left( \frac{N - (\text{当前周期位置} - \text{N周期内最低价位置})}{N} \right) \times 100 \]<br/>或者更直观地：<br/>\[ \text{AroonDown}_t = \left( \frac{N - \text{自N周期内最低价出现以来经过的周期数}}{N} \right) \times 100 \]
factor_435,435,时序,ATR平均真实波幅，使用Numba优化,"计算 ATR 分为两个主要步骤：首先计算真实波幅 (True Range, TR)，然后对 TR 进行平滑处理得到 ATR。<br/><br/>1.  **真实波幅 (True Range, $TR_t$)**<br/>    对于每一个时间周期 $t$，真实波幅 $TR_t$ 定义为以下三者中的最大值：<br/>    $TR_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$<br/><br/>2.  **平均真实波幅 (Average True Range, $ATR_t$)**<br/>    $ATR$ 的计算采用威尔德平滑法 (Wilder's Smoothing)，这是一种特殊的指数移动平均。<br/>    计算周期为 $N$。<br/>    *   **初始 $ATR$ 值 ($ATR_N^{initial}$):** 第一个 $ATR$ 值是前 $N$ 个 $TR$ 值的简单移动平均 (SMA)。<br/>        $ATR_N^{initial} = \frac{1}{N} \sum_{i=1}^{N} TR_i$<br/>        这个初始值对应的是第 $N$ 个 $TR$ 值计算完成后的 $ATR$。<br/><br/>    *   **后续 $ATR$ 值 ($ATR_t$ for $t > N$):**<br/>        $ATR_t = \frac{(N-1) \times ATR_{t-1} + TR_t}{N}$<br/><br/>    *   **针对周期为1的特殊情况：** 如果 $N=1$, 则 $ATR_t = TR_t$。"
factor_436,436,时序,AVGDEV平均离差因子，使用Numba优化,给定一个时间序列数据 \(P\)，在时间点 \(t\) 的平均离差 \(AVGDEV_t\) 计算公式如下：<br/><br/>1.  首先，计算周期 \(N\) 内数据的算术平均值 \(\bar{P}_t\):<br/>    \[<br/>    \bar{P}_t = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i}<br/>    \]<br/><br/>2.  然后，计算平均离差 \(AVGDEV_t\):<br/>    \[<br/>    AVGDEV_t = \frac{1}{N} \sum_{i=0}^{N-1} |P_{t-i} - \bar{P}_t|<br/>    \]
factor_446,446,时序,CORR皮尔逊相关系数因子，计算close和volume的相关系数,皮尔逊相关系数 `r` 的计算公式如下：<br/><br/>$r = \frac{n(\sum_{i=1}^{n} X_i Y_i) - (\sum_{i=1}^{n} X_i)(\sum_{i=1}^{n} Y_i)}{\sqrt{[n(\sum_{i=1}^{n} X_i^2) - (\sum_{i=1}^{n} X_i)^2][n(\sum_{i=1}^{n} Y_i^2) - (\sum_{i=1}^{n} Y_i)^2]}}$<br/><br/>其中：<br/>*   分子表示为：$n \times \text{sumXY} - \text{sumX} \times \text{sumY}$<br/>*   分母中的第一部分（X的离差平方和相关项）表示为：$n \times \text{sumX2} - (\text{sumX})^2$<br/>*   分母中的第二部分（Y的离差平方和相关项）表示为：$n \times \text{sumY2} - (\text{sumY})^2$<br/><br/>在实际计算中，为了避免重复除以 `n`，TALib的实现采用了如下等价形式进行计算：<br/>令：<br/>$Num = (\sum_{i=1}^{n} X_i Y_i) - \frac{(\sum_{i=1}^{n} X_i)(\sum_{i=1}^{n} Y_i)}{n}$<br/>$Den_X = (\sum_{i=1}^{n} X_i^2) - \frac{(\sum_{i=1}^{n} X_i)^2}{n}$<br/>$Den_Y = (\sum_{i=1}^{n} Y_i^2) - \frac{(\sum_{i=1}^{n} Y_i)^2}{n}$<br/><br/>则 $r = \frac{Num}{\sqrt{Den_X \times Den_Y}}$<br/><br/>如果 $Den_X \times Den_Y \le 0$，则 $r = 0$。
factor_447,447,基础字段,COS向量三角余弦因子,对于输入序列中的每一个值 \(X_t\)，其对应的余弦因子值 \(\text{COS}_t\) 计算如下：<br/><br/>\[<br/>\text{COS}_t = \cos(X_t)<br/>\]<br/><br/>该公式表明，因子值是输入数据点 \(X_t\) 的标准三角余弦函数值。
factor_450,450,基础字段,DIV向量除法因子，计算close/volume,"**<br/><br/>给定两个长度相同的数值序列（向量） $A = \{a_1, a_2, \ldots, a_n\}$ 和 $B = \{b_1, b_2, \ldots, b_n\}$，该因子计算得到一个新的序列 $C = \{c_1, c_2, \ldots, c_n\}$，其中每个元素 $c_i$ 的计算方式如下：<br/><br/>$c_i = \frac{a_i}{b_i}$<br/><br/>其中：<br/>*   $a_i$ 是序列 $A$ 中的第 $i$ 个元素。<br/>*   $b_i$ 是序列 $B$ 中的第 $i$ 个元素。<br/>*   $c_i$ 是输出序列 $C$ 中的第 $i$ 个元素。<br/><br/>**"
factor_453,453,时序,HT_INPHASE希尔伯特变换瞬时相位，使用Numba优化,关键步骤包括：<br/>A. 价格平滑：<br/>   SmoothedPrice_t = 0.1 × (4×Price_t + 3×Price_{t-1} + 2×Price_{t-2} + 1×Price_{t-3})<br/>B. 动态调整因子：<br/>   AdjustedPeriodFactor_t = 0.075 × Period_{t-1} + 0.54<br/>C. 去趋势分量（Detrender）：<br/>   Detrender_t = (a × SmoothedPrice_t + b × SmoothedPrice_{t-2} - b × SmoothedPrice_{t-4} - a × SmoothedPrice_{t-6}) × AdjustedPeriodFactor_t<br/>D. 瞬时相位分量（HT_INPHASE）：<br/>   HT_INPHASE_t = Detrender_{t-3}
factor_454,454,时序,HT_QUAD希尔伯特变换正交分量，使用Numba优化,关键步骤包括：<br/>A. 价格平滑：<br/>   SmoothedPrice_t = 0.1 × (4×Price_t + 3×Price_{t-1} + 2×Price_{t-2} + 1×Price_{t-3})<br/>B. 动态调整因子：<br/>   AdjustedPeriodFactor_t = 0.075 × Period_{t-1} + 0.54<br/>C. 去趋势分量（Detrender）：<br/>   Detrender_t = (a × SmoothedPrice_t + b × SmoothedPrice_{t-2} - b × SmoothedPrice_{t-4} - a × SmoothedPrice_{t-6}) × AdjustedPeriodFactor_t<br/>D. 正交分量（HT_QUAD）：<br/>   HT_QUAD_t = (a × Detrender_t + b × Detrender_{t-2} - b × Detrender_{t-4} - a × Detrender_{t-6}) × AdjustedPeriodFactor_t
factor_473,473,基础字段,ADL蔡金累积/派发线,"该因子的核心思想是计算每个周期的“资金流量乘数”(Money Flow Multiplier, MFM)，然后将其乘以该周期的成交量得到“资金流量”(Money Flow Volume, MFV)。最后，将这些资金流量进行累积得到累积/派发线 (ADL)。<br/><br/>1.  **资金流量乘数 (Money Flow Multiplier, MFM)**:<br/>    $MFM_t = \frac{(C_t - L_t) - (H_t - C_t)}{H_t - L_t}$<br/>    其中，如果 $H_t - L_t = 0$，则该周期的资金流量贡献视为0（即 $MFM_t \times V_t = 0$ for ADL calculation）。<br/><br/>2.  **资金流量 (Money Flow Volume, MFV)**:<br/>    $MFV_t = MFM_t \times V_t$<br/><br/>3.  **累积/派发线 (Accumulation/Distribution Line, ADL)**:<br/>    $ADL_t = ADL_{t-1} + MFV_t$<br/>    初始值 $ADL_0$ 通常设为0，然后开始累积。所以，在实际编码中，可以理解为 $ADL_t$ 是从序列开始到 $t$ 时刻的 $MFV$ 的累积和。<br/><br/>    $ADL_t = \sum_{i=1}^{t} MFV_i = \sum_{i=1}^{t} \left( \frac{(C_i - L_i) - (H_i - C_i)}{H_i - L_i} \times V_i \right)$<br/>    (对于 $H_i - L_i = 0$ 的情况，该项 $MFV_i$ 为 0)。"
factor_488,488,时序,MFI资金流量指标，使用Numba优化,"资金流量指标 (MFI) 的计算基于特定周期内的正资金流和负资金流。<br/><br/>首先定义几个基础概念：<br/><br/>1.  典型价格 (Typical Price, TP):<br/>    `TP_t = (High_t + Low_t + Close_t) / 3`<br/><br/>2.  原始资金流 (Raw Money Flow, RMF):<br/>    `RMF_t = TP_t \times Volume_t`<br/><br/>3.  正资金流 (Positive Money Flow, PMF) 与 负资金流 (Negative Money Flow, NMF):<br/>    *   如果 `TP_t > TP_{t-1}`，则：<br/>        `PMF_t = RMF_t`<br/>        `NMF_t = 0`<br/>    *   如果 `TP_t < TP_{t-1}`，则：<br/>        `PMF_t = 0`<br/>        `NMF_t = RMF_t`<br/>    *   如果 `TP_t = TP_{t-1}`，则：<br/>        `PMF_t = 0`<br/>        `NMF_t = 0`<br/><br/>然后，计算周期 `N` 内的正负资金流总和：<br/><br/>`TotalPositiveMoneyFlow_N = \sum_{i=t-N+1}^{t} PMF_i`<br/><br/>`TotalNegativeMoneyFlow_N = \sum_{i=t-N+1}^{t} NMF_i`<br/><br/>最后，计算资金流量指标 (MFI)：<br/><br/>`MFI_t = 100 \times \frac{TotalPositiveMoneyFlow_N}{TotalPositiveMoneyFlow_N + TotalNegativeMoneyFlow_N}`<br/><br/>特殊情况处理：<br/>如果 `(TotalPositiveMoneyFlow_N + TotalNegativeMoneyFlow_N)` 非常接近0 (例如小于1.0，如源码中处理)，则 `MFI_t = 0`。<br/>如果 `TotalNegativeMoneyFlow_N = 0` 且 `TotalPositiveMoneyFlow_N > 0`，则 `MFI_t = 100`。"
factor_492,492,时序,ADX平均趋向指数，使用Numba优化（重新准确实现）,"ADX的计算涉及多个步骤和中间指标：<br/><br/>1.  **真实波幅 (True Range, $TR_t$)**:<br/>    $TR_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$<br/><br/>2.  **趋向变动 (+DM 和 -DM)**:<br/>    $UpMove_t = H_t - H_{t-1}$<br/>    $DownMove_t = L_{t-1} - L_t$<br/><br/>    $+DM_{1,t} = \begin{cases} UpMove_t & \text{if } UpMove_t > DownMove_t \text{ and } UpMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$<br/>    $-DM_{1,t} = \begin{cases} DownMove_t & \text{if } DownMove_t > UpMove_t \text{ and } DownMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$<br/>    注意：如果 $UpMove_t$ 和 $DownMove_t$ 相等或都小于等于0，则 $+DM_{1,t}$ 和 $-DM_{1,t}$ 均为0。<br/><br/>3.  **N周期平滑趋向变动 ($+DM_{N,t}$, $-DM_{N,t}$) 和N周期平滑真实波幅 ($TR_{N,t}$)**:<br/>    这些指标使用 Wilder 平滑法（一种特殊的指数移动平均）。对于周期N：<br/>    *   初始值 (例如，第一个 $TR_N$):<br/>        $TR_{N,\text{initial}} = \sum_{i=1}^{N} TR_{1,i}$ (前N个单周期TR的总和)<br/>    *   后续值:<br/>        $CurrentSmoothedValue_t = PreviousSmoothedValue_{t-1} - \frac{PreviousSmoothedValue_{t-1}}{N} + CurrentSinglePeriodValue_t$<br/>        这等价于：<br/>        $CurrentSmoothedValue_t = \frac{(N-1) \times PreviousSmoothedValue_{t-1} + CurrentSinglePeriodValue_t}{N}$<br/><br/>    所以，<br/>    $+DM_{N,t} = \text{WilderSmooth}(+DM_{1,t}, N)$<br/>    $-DM_{N,t} = \text{WilderSmooth}(-DM_{1,t}, N)$<br/>    $TR_{N,t} = \text{WilderSmooth}(TR_{1,t}, N)$<br/><br/>4.  **N周期正趋向指标 ($+DI_{N,t}$) 和N周期负趋向指标 ($-DI_{N,t}$)**:<br/>    $+DI_{N,t} = 100 \times \frac{+DM_{N,t}}{TR_{N,t}}$<br/>    $-DI_{N,t} = 100 \times \frac{-DM_{N,t}}{TR_{N,t}}$<br/>    (如果 $TR_{N,t} = 0$，则 $+DI_{N,t}$ 和 $-DI_{N,t}$ 通常设为0)<br/><br/>5.  **N周期趋向指数 (Directional Movement Index, $DX_t$)**:<br/>    $DX_t = 100 \times \frac{|+DI_{N,t} - (-DI_{N,t})|}{+DI_{N,t} + (-DI_{N,t})}$<br/>    (如果分母 $(+DI_{N,t} + (-DI_{N,t})) = 0$，则 $DX_t$ 通常设为0)<br/><br/>6.  **N周期平均趋向指数 (Average Directional Movement Index, $ADX_{N,t}$)**:<br/>    ADX是DX的Wilder平滑移动平均。<br/>    *   初始值 (第一个 $ADX_N$):<br/>        $ADX_{N,\text{initial}} = \frac{1}{N} \sum_{i=1}^{N} DX_{i}$ (前N个DX值的简单移动平均)<br/>    *   后续值:<br/>        $ADX_{N,t} = \frac{ADX_{N,t-1} \times (N-1) + DX_t}{N}$"
factor_493,493,时序,MININDEX周期内最小值索引，使用Numba优化,"对于时间序列 $Input$ 和给定的周期长度 $P$，在每个时间点 $t$（当 $t \ge P-1$ 时），计算 $MININDEX_t$ 的值：<br/><br/>$MININDEX_t = \underset{k \in [t-P+1, t]}{\arg\min} (Input_k)$<br/><br/>其中：<br/>*   $\arg\min$ 表示取使表达式 $Input_k$ 达到最小值的参数 $k$（即索引）。<br/>*   如果存在多个相同的最小值，则通常取最后一个（即索引最大者，对应于时间点 $t$ 或离 $t$ 最近的那个）最小值对应的索引。在所提供的源码实现中，如果当前值 `tmp` 与已记录的最低值 `lowest` 相等 (`tmp <= lowest`)，则当前索引 `today` 会成为新的 `lowestIdx`，因此它会优先选择时间上更近的索引。"
factor_498,498,时序,MINUS_DI下降方向线，使用Numba优化,"$-DI_{N,t} = \frac{-DM_{N,t}}{TR_{N,t}} \times 100$<br/><br/>其中：<br/>*   $-DI_{N,t}$ 是在时间点 $t$、周期为 $N$ 的下降方向线。<br/>*   $-DM_{N,t}$ 是在时间点 $t$、经过 $N$ 周期平滑的下降动向值。<br/>*   $TR_{N,t}$ 是在时间点 $t$、经过 $N$ 周期平滑的真实波幅。"
factor_504,504,时序,NVI负成交量指标，使用Numba优化,"负成交量指标 (NVI) 的计算基于昨日的NVI值、今日和昨日的收盘价以及今日和昨日的成交量。<br/><br/>初始设定：$NVI_0 = \text{InitialValue}$ (通常为 1000)<br/><br/>对于后续的每一天 $t$ ($t > 0$):<br/>$$
NVI_t =
\begin{cases}
NVI_{t-1} \times \left( 1 + \frac{C_t - C_{t-1}}{C_{t-1}} \right) & \text{如果 } V_t < V_{t-1} \\
NVI_{t-1} & \text{如果 } V_t \ge V_{t-1}
\end{cases}
$$<br/>其中，如果 $C_{t-1}$ 为零，则当日价格变动百分比 $\frac{C_t - C_{t-1}}{C_{t-1}}$ 项的处理需要特别注意，通常这种情况应该避免或认为价格无变化（即百分比为0）。<br/>公式也可以简化为：<br/>$$
NVI_t =
\begin{cases}
NVI_{t-1} \times \frac{C_t}{C_{t-1}} & \text{如果 } V_t < V_{t-1} \text{ 且 } C_{t-1} \neq 0 \\
NVI_{t-1} & \text{如果 } V_t \ge V_{t-1} \text{ 或 } C_{t-1} = 0
\end{cases}
$$"
factor_505,505,时序,OBV能量潮指标,能量潮指标通过累计每日的成交量来构建。当今日价格（通常为收盘价）高于昨日价格时，今日成交量被加到前一日的OBV值上；当今日价格低于昨日价格时，今日成交量从前一日的OBV值中减去；若价格不变，则OBV值亦不变。<br/><br/>其数学表达式如下：<br/><br/>设 $C_t$ 为 $t$ 时刻的价格，$V_t$ 为 $t$ 时刻的成交量，$OBV_t$ 为 $t$ 时刻的能量潮值。<br/><br/>对于计算序列的第一个数据点 (令 $t=0$):<br/>$OBV_0 = V_0$<br/><br/>对于后续的数据点 ($t > 0$):<br/>$OBV_t = OBV_{t-1} + \Delta V_t$<br/>其中，$\Delta V_t$ 的计算逻辑如下：<br/>$\Delta V_t = \begin{cases} +V_t & \text{if } C_t > C_{t-1} \\ -V_t & \text{if } C_t < C_{t-1} \\ 0 & \text{if } C_t = C_{t-1} \end{cases}$<br/><br/>因此，完整公式可以写作：<br/>$OBV_t = \begin{cases} V_0 & \text{if } t = 0 \\ OBV_{t-1} + V_t & \text{if } C_t > C_{t-1} \text{ and } t > 0 \\ OBV_{t-1} - V_t & \text{if } C_t < C_{t-1} \text{ and } t > 0 \\ OBV_{t-1} & \text{if } C_t = C_{t-1} \text{ and } t > 0 \end{cases}$
factor_507,507,时序,PLUS_DM正向趋向移动，使用Numba优化,"首先，定义单周期的向上移动值 (UpMove) 和向下移动值 (DownMove)：<br/>`UpMove_t = H_t - H_{t-1}`<br/>`DownMove_t = L_{t-1} - L_t`<br/><br/>然后，定义单周期的正向趋向移动 (`+DM1_t`)：<br/>`+DM1_t = \\begin{cases} UpMove_t & \\text{if } UpMove_t > DownMove_t \\text{ and } UpMove_t > 0 \\\\ 0 & \\text{otherwise} \\end{cases}`<br/><br/>最后，使用 Wilder 的平滑方法计算 N 周期的正向趋向移动 (`+DM_{N,t}`)：<br/>为了计算第一个 `+DM_{N}` 值 (记为 `+DM_{N, \text{initial}}`) ，通常取最初 N 个周期的 `+DM1` 值的和：<br/>`+DM_{N, \text{initial}} = \\sum_{i=1}^{N} +DM1_i`<br/>(注意: 为了计算第一个`+DM1_i`，需要 `H_i`, `L_i`, `H_{i-1}`, `L_{i-1}`。所以 `+DM1_1` 是使用 `H_1, L_1` 和 `H_0, L_0` 计算的。总共需要 `N+1` 个原始数据点来计算第一个 `+DM_N`。)<br/><br/>对于后续的 `+DM_{N,t}` 值，采用递归公式：<br/>`+DM_{N,t} = +DM_{N,t-1} - \\frac{+DM_{N,t-1}}{N} + +DM1_t`"
factor_509,509,时序,PVI正量指标，使用Numba优化,设 $PVI_t$ 为在时间点 $t$ 的正量指标值。<br/>设 $C_t$ 为在时间点 $t$ 的收盘价。<br/>设 $V_t$ 为在时间点 $t$ 的成交量。<br/><br/>PVI的计算是迭代的，初始值通常设定为100或1000。我们这里以100为例。<br/>令 $PVI_{initial} = 100$ 作为计算序列中有效PVI值的前一个基准值。<br/><br/>对于时间序列中的第一个数据点（$t=0$），其 $C_0$ 和 $V_0$ 将作为计算第二个数据点（$t=1$）PVI的基础。<br/>第一个计算出的PVI值将是 $PVI_1$。<br/><br/>对于 $t \ge 1$:<br/>如果 $V_t > V_{t-1}$ （当日成交量大于昨日成交量）：<br/>\[ PVI_t = PVI_{t-1} + PVI_{t-1} \times \frac{C_t - C_{t-1}}{C_{t-1}} \]<br/>如果 $V_t \le V_{t-1}$ （当日成交量小于或等于昨日成交量）：<br/>\[ PVI_t = PVI_{t-1} \]<br/>其中，$PVI_{t-1}$ 代表上一期的PVI值。对于计算 $PVI_1$ 时，$PVI_0$ 即为 $PVI_{initial}$。
