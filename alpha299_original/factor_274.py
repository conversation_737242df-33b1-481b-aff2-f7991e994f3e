# Alpha299因子 - factor_274
# 原始因子编号: 274
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_274(data_df, w: int | None = 5, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'ts_max_window': 5,      # TS_MAX窗口
        'ts_corr_window': 5,     # TS_CORR窗口
        'std_window': 12,        # TS_STD窗口
        'delta_window': 26       # DELTA窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    ts_max_window = window_sizes['ts_max_window']      # TS_MAX窗口
    ts_corr_window = window_sizes['ts_corr_window']    # TS_CORR窗口
    std_window = window_sizes['std_window']            # TS_STD窗口
    delta_window = window_sizes['delta_window']        # DELTA窗口

    df = data_df.copy()
    df.sort_values(by=['symbol', 'time'], inplace=True)

    # 先计算VWAP（成交量加权平均价）
    # 保护：分母加1e-8防止除以0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
    
    # 步骤1: DELTA(VWAP, delta_window)
    df['delta_vwap'] = df.groupby('symbol')['vwap'].transform(lambda x: x - x.shift(delta_window))

    # 步骤2: RANK(AMOUNT) - 按时间分组排名
    df['rank_amount'] = df.groupby('time')['amount'].transform('rank')

    # 步骤3: GP_MAX(DELTA_VWAP, RANK_AMOUNT)
    df['gp_max'] = df[['delta_vwap', 'rank_amount']].max(axis=1)

    # 步骤4: TS_MAX(步骤3结果, ts_max_window)
    df['ts_max'] = df.groupby('symbol')['gp_max'].transform(
        lambda x: x.rolling(window=ts_max_window, min_periods=w).max()
    )

    # 步骤5: TS_CORR(VWAP, AMOUNT, ts_corr_window)
    def calc_corr(group):
        # 保护：对corr结果中的nan和inf填充0
        corr_result = group['vwap'].rolling(window=ts_corr_window, min_periods=w).corr(group['amount'])
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)
    df['ts_corr'] = df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)

    # 步骤6: GP_MIN(步骤4结果, 步骤5结果)
    df['gp_min'] = df[['ts_max', 'ts_corr']].min(axis=1)

    # 步骤7: TS_STD(步骤6结果, std_window)
    df['ts_std'] = df.groupby('symbol')['gp_min'].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).std()
    )

    # 步骤8: 替换无穷大
    df['ts_std'] = df['ts_std'].replace([np.inf, -np.inf], np.nan)

    # 格式化日期和时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'ts_std']].rename(columns={'ts_std': 'factor'}).dropna()
    return output_df

