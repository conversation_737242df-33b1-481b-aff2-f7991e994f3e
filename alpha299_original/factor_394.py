# Alpha299因子 - factor_394
# 原始因子编号: 394
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_394(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha79因子

    参数:
    data_df: 输入数据DataFrame
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为11天（n2窗口）。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 不适用于此因子，因为使用了多个数据列

    返回:
    包含因子值的DataFrame，列为['trade_date', 'time', 'symbol', 'factor']
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 11,  # vwap的滚动最小值窗口
        'n2': 5,   # vwap差值窗口
        'n3': 20   # 滚动平均值窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']

    # 复制数据以避免修改原始数据
    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 计算vwap (成交量加权平均价)
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 创建一个空的结果DataFrame
    result_columns = ['trade_date', 'time', 'symbol', 'factor']
    result_df = pd.DataFrame(columns=result_columns)

    # 对每个symbol单独计算因子
    for symbol in df['symbol'].unique():
        # 筛选当前symbol的数据
        symbol_df = df[df['symbol'] == symbol].sort_values('time').copy()

        if len(symbol_df) <= 1:
            continue

        # 步骤1: 计算vwap的相反数
        symbol_df['T1'] = -symbol_df['vwap']

        # 步骤2: 计算T1在过去n1个周期内的滚动最小值
        symbol_df['T2'] = symbol_df['T1'].rolling(window=n1, min_periods=w).min()

        # 步骤3: 计算成交额的绝对值
        symbol_df['T3'] = symbol_df['amount'].abs()

        # 步骤4: 计算vwap在过去n3个周期内的滚动平均值
        symbol_df['T4'] = symbol_df['vwap'].rolling(window=n3, min_periods=w).mean()

        # 步骤5: 计算amount在过去n3个周期内的滚动平均值
        symbol_df['T5'] = symbol_df['amount'].rolling(window=n3, min_periods=w).mean()

        # 步骤6: 计算T4除以T5
        symbol_df['T6'] = symbol_df['T4'] / (symbol_df['T5'] + 1e-8)

        # 步骤7: 计算T3乘以T6
        symbol_df['T7'] = symbol_df['T3'] * symbol_df['T6']

        # 步骤8: 计算T2与T7的差值
        symbol_df['T8'] = symbol_df['T2'] - symbol_df['T7']

        # 步骤9: 计算vwap除以T8
        symbol_df['X1'] = symbol_df['vwap'] / (symbol_df['T8'] + 1e-8)

        # 步骤10: 计算vwap在过去n2个周期内的差值
        symbol_df['X2'] = symbol_df['vwap'] - symbol_df['vwap'].shift(n2)

        # 步骤11: 计算X2在过去n3个周期内的滚动平均值
        symbol_df['X3'] = symbol_df['X2'].rolling(window=n3, min_periods=w).mean()

        # 步骤12: 计算X2除以X3
        symbol_df['X4'] = symbol_df['X2'] / (symbol_df['X3'] + 1e-8)

        # 步骤13: 取X1和X4中逐元素的较小值得到Alpha79
        symbol_df['factor'] = np.minimum(symbol_df['X1'], symbol_df['X4'])

        # 步骤14: 将结果中的无穷大值 (inf, -inf) 替换为NaN
        symbol_df['factor'] = symbol_df['factor'].replace([np.inf, -np.inf], np.nan)

        # 添加到结果DataFrame
        result_df = pd.concat([result_df, symbol_df[result_columns].dropna()])

    # Check if we have any results before processing
    if not result_df.empty:
        # 恢复日期和时间格式为字符串
        result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
        result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df

