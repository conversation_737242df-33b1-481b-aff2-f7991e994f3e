# Alpha299因子 - factor_330
# 原始因子编号: 330
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_330(data_df, w: int | None = 20, uni_col: str | None = None):
    """
    计算Alpha113因子
    
    Alpha113 = ts_regres(delay(low, 20), mul(open_price, close), 20)
    
    参数:
    data_df: 输入数据
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 单一列参数，本因子不使用单一列，设为None
    
    返回:
    包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delay_window': 20,     # delay窗口
        'regres_window': 20     # 回归窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delay_window = window_sizes['delay_window']
    regres_window = window_sizes['regres_window']

    # 确保输入为DataFrame
    df = data_df.copy()
    
    # 将日期时间列转换为datetime格式以便后续处理
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')
    
    # 按品种分组计算
    result_dfs = []
    
    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')
        
        # 1. 获取最低价在delay_window个周期前的值: X1 = delay(low, delay_window)
        delayed_low = group['low'].shift(delay_window)
        
        # 2. 计算开盘价与收盘价的乘积: X2 = mul(open_price, close)
        open_close_product = group['open'] * group['close']
        
        # 3. 计算X2对X1在过去regres_window个周期内的滚动回归残差
        # 创建一个存储结果的Series
        alpha113 = pd.Series(np.nan, index=group.index)
        
        # 对每个时间点计算回归
        for i in range(regres_window, len(group)):
            # 获取过去regres_window个时间点的数据(包括当前点)
            X = delayed_low.iloc[i-regres_window:i].values.reshape(-1, 1)  # 自变量
            Y = open_close_product.iloc[i-regres_window:i].values  # 因变量
            
            # 检查是否有足够的有效数据点进行回归
            valid_mask = ~(np.isnan(X.flatten()) | np.isnan(Y))
            if np.sum(valid_mask) < 2:  # 至少需要2个点才能进行回归
                continue
                
            # 使用有效数据点进行回归
            X_valid = X[valid_mask].reshape(-1, 1)
            Y_valid = Y[valid_mask]
            
            # 计算回归系数
            try:
                # 直接使用linregress，其内部已包含对标准差为0的保护
                # 添加对输入数据的inf/-inf检查
                if np.any(np.isinf(X_valid)) or np.any(np.isinf(Y_valid)):
                    continue

                slope, intercept, r_value, p_value, std_err = stats.linregress(X_valid.flatten(), Y_valid)
                
                # 计算当前点的预测值
                current_x = delayed_low.iloc[i]
                if not np.isnan(current_x) and not np.isinf(current_x):
                    predicted_y = slope * current_x + intercept
                    
                    # 计算残差: 实际值 - 预测值
                    actual_y = open_close_product.iloc[i]
                    if not np.isnan(actual_y) and not np.isinf(actual_y):
                        residual = actual_y - predicted_y
                        # 检查残差是否为inf或nan，如果是则跳过
                        if not np.isnan(residual) and not np.isinf(residual):
                            alpha113.iloc[i] = residual
            except:
                # 如果回归失败，保持NaN值
                continue
        
        # 将结果添加到group中
        group['factor'] = alpha113
        
        # 4. 将无穷大值替换为NaN (虽然上面已经有检查，但作为后处理再加一层保护)
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)
        
        # 只保留需要的列
        result_df = group[['trade_date', 'time', 'symbol', 'factor']]
        result_dfs.append(result_df)
    
    # 合并所有结果
    if result_dfs:
        result = pd.concat(result_dfs)
    else:
        # 如果没有结果，返回空DataFrame
        result = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])
    
    # 恢复日期和时间格式为字符串
    result['trade_date'] = result['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result['time'] = result['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 删除缺失值 (保留真实的缺值情况，只删除factor列为NaN的行)
    result = result.dropna(subset=['factor'])
    
    return result

