# Alpha299因子 - factor_15
# 原始因子编号: 15
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_15(data_df, w: int | None = 2, uni_col: str | None = 'close'):
    """
    计算Alpha因子：基于收益率标准差比值和价格差分的排名因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（默认为'close'）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'window_2': 2,          # 2期标准差和差分排名窗口
        'window_5': 5,          # 5期标准差窗口  
        'rank_window': 20       # 最终排名窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    window_2 = window_sizes['window_2']
    window_5 = window_sizes['window_5'] 
    rank_window = window_sizes['rank_window']

    # 按symbol分组，处理每个品种的数据
    df = data_df.copy()

    # 确保数据按时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 1. 计算RETURNS（基于close的对数收益率）
    # 保护 log(x) - log(x.shift(1)) 中的 x 和 x.shift(1) 大于 0
    df['returns'] = df.groupby('symbol')[uni_col].transform(
        lambda x: np.log(x + 1e-8) - np.log(x.shift(1) + 1e-8)
    )

    # 2. 计算2期和5期标准差
    df['std_2'] = df.groupby('symbol')['returns'].transform(
        lambda x: x.rolling(window=window_2, min_periods=w).std()
    )
    df['std_5'] = df.groupby('symbol')['returns'].transform(
        lambda x: x.rolling(window=window_5, min_periods=w).std()
    )

    # 3. 计算标准差比值
    # 保护分母不为0
    df['ratio_std'] = df['std_2'] / (df['std_5'] + 1e-8)
    # 保护 ratio_std 中的 inf 和 nan
    df['ratio_std'] = df['ratio_std'].replace([np.inf, -np.inf], np.nan)


    # 4. 对比值进行排名（正向排名）
    # 保护分母不为0
    df['rank_std_ratio'] = df.groupby('symbol')['ratio_std'].transform(
        lambda x: x.rolling(window=window_5, min_periods=w).rank(method='average') / (x.rolling(window=window_5, min_periods=w).count() + 1e-8)
    )
    df['part1'] = 1 - df['rank_std_ratio']

    # 5. 计算CLOSE的1期差分
    df['delta_close'] = df.groupby('symbol')[uni_col].transform(lambda x: x.diff(1))

    # 6. 对差分进行排名（正向排名）
    # 保护分母不为0
    df['rank_delta_close'] = df.groupby('symbol')['delta_close'].transform(
        lambda x: x.rolling(window=window_2, min_periods=w).rank(method='average') / (x.rolling(window=window_2, min_periods=w).count() + 1e-8)
    )
    df['part2'] = 1 - df['rank_delta_close']

    # 7. 两部分相加并再次排名（正向排名）
    df['sum_parts'] = df['part1'] + df['part2']
    # 保护分母不为0
    df['factor'] = df.groupby('symbol')['sum_parts'].transform(
        lambda x: x.rolling(window=rank_window, min_periods=w).rank(method='average') / (x.rolling(window=rank_window, min_periods=w).count() + 1e-8)
    )
    # 保护 factor 中的 inf 和 nan
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)


    # 8. 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 9. 输出所需列并去除无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

