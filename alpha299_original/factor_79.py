# Alpha299因子 - factor_79
# 原始因子编号: 79
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_79(data_df, w: int | None = None, uni_col: str | None = None):
    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'close', 'volume', 'amount']
    for col in required_cols:
        if col not in data_df.columns:
            raise ValueError(f"数据中缺少必要列: {col}")

    # 动态计算vwap（假设amount是总成交额，volume是总成交量）
    # 保护分母不为零
    data_df['vwap'] = data_df['amount'] / (data_df['volume'] + 1e-8)

    # 确保数据按symbol和时间排序
    data_df = data_df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 计算混合价格 P_mix
    data_df['P_mix'] = 0.35 * data_df['close'] + 0.65 * data_df['vwap']

    # 计算Delta_2 P_mix
    data_df['P_mix_2d_diff'] = data_df.groupby('symbol')['P_mix'].transform(lambda x: x - x.shift(2))

    # 线性衰减函数
    def decay_linear(series, window):
        weights = np.arange(1, window + 1)
        # 保护分母不为零
        return series.rolling(window=window).apply(lambda x: np.dot(x, weights) / (weights.sum() + 1e-8) if not np.all(np.isnan(x)) else np.nan, raw=True)

    # 应用3期线性衰减得到DL1
    data_df['DL1'] = decay_linear(data_df.groupby('symbol')['P_mix_2d_diff'].transform(lambda x: x), window=3)

    # 横截面排名R1（修正百分比计算）
    def pct_rank(x):
        # 过滤掉NaN值再计算排名
        valid_x = x[~np.isnan(x)]
        if len(valid_x) <= 1:
            return np.full(len(x), np.nan) # 返回与输入相同长度的NaN数组
        ranks = rankdata(valid_x, method='average', nan_policy='omit')
        # 将排名结果映射回原始索引
        result = np.full(len(x), np.nan)
        result[~np.isnan(x)] = (ranks - 1) / (len(valid_x) - 1 + 1e-8)
        return result

    # 使用apply而不是transform，因为transform在某些pandas版本中对自定义函数处理不一致
    data_df['R1'] = data_df.groupby('time')['DL1'].transform(pct_rank)


    # 计算MA_180(Volume)
    data_df['MA_volume_180'] = data_df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=180, min_periods=w).mean())

    # 计算CorrVC_t
    # 对滚动相关性结果进行NaN/inf处理
    data_df['CorrVC'] = data_df.groupby('symbol').apply(
        lambda group: group['MA_volume_180'].rolling(window=13, min_periods=w).corr(group['close']).fillna(0).replace([np.inf, -np.inf], 0)
    ).reset_index(level=0, drop=True)

    # 对绝对值CorrVC应用5期线性衰减得到DL2
    data_df['abs_CorrVC'] = data_df['CorrVC'].abs()
    data_df['DL2'] = decay_linear(data_df.groupby('symbol')['abs_CorrVC'].transform(lambda x: x), window=5)

    # 15期时序排名R2（修正百分比计算）
    def ts_rank(series, window):
        def _pct_rank(x):
            # 过滤掉NaN值再计算排名
            valid_x = x[~np.isnan(x)]
            if len(valid_x) <= 1:
                return np.nan
            ranks = rankdata(valid_x, method='average', nan_policy='omit')
            # 找到原始序列中最后一个非NaN值对应的排名
            last_val = x[-1]  # 使用numpy数组索引而不是iloc
            if np.isnan(last_val):
                return np.nan
            # 找到最后一个值在有效值列表中的位置
            try:
                 last_val_rank_idx = np.where(valid_x == last_val)[0][-1] # 取最后一个匹配项的索引
                 return (ranks[last_val_rank_idx] - 1) / (len(valid_x) - 1 + 1e-8)
            except IndexError:
                 return np.nan # 如果最后一个值不在有效值列表中（理论上不应该发生，但作为保护）


        # 使用apply而不是transform，因为transform在某些pandas版本中对自定义函数处理不一致
        return series.rolling(window=window).apply(_pct_rank, raw=True)

    # 使用apply而不是transform
    data_df['R2'] = data_df.groupby('symbol')['DL2'].transform(lambda x: ts_rank(x, window=15))


    # 最终因子值
    data_df['factor'] = -data_df[['R1', 'R2']].max(axis=1)

    # 处理日期和时间格式
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去除非NaN值
    output_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

