# Alpha299因子 - factor_271
# 原始因子编号: 271
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_271(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha220因子，解决量纲混加问题

    参数:
        data_df: 原始数据DataFrame
        w: 核心可调窗口参数（单位：天），默认7
        uni_col: 单一基础列参数，本因子不适用则设为None

    返回:
        包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'high_std_window': 5,     # HIGH标准差窗口
        'vwap_std_window': 7,     # VWAP标准差窗口
        'volume_max_window': 10,  # VOLUME最大值窗口
        'final_std_window': 14,   # 最终标准差窗口
        'std_window': 20          # 标准化窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    high_std_window = window_sizes['high_std_window']         # HIGH标准差窗口
    vwap_std_window = window_sizes['vwap_std_window']         # VWAP标准差窗口
    volume_max_window = window_sizes['volume_max_window']     # VOLUME最大值窗口
    final_std_window = window_sizes['final_std_window']       # 最终标准差窗口
    std_window = window_sizes['std_window']                   # 标准化窗口

    df = data_df.copy()

    # 确保按symbol和时间排序
    df.sort_values(['symbol', 'trade_date', 'time'], inplace=True)

    # 1. 计算每日VWAP（成交量加权平均价）
    # 确保分母不为0
    df['vwap'] = df.groupby(['symbol', 'trade_date']).apply(
        lambda x: (x['close'] * x['volume']).sum() / (x['volume'].sum() + 1e-8)
    ).reset_index(level=[0, 1], drop=True)

    # 2. 计算VWAP的vwap_std_window周期标准差
    df['vwap_std'] = df.groupby('symbol')['vwap'].transform(
        lambda x: x.rolling(vwap_std_window, min_periods=w).std()
    )

    # 3. 计算VWAP标准差的Z-score（std_window周期）
    # 确保分母不为0
    vwap_std_mean = df.groupby('symbol')['vwap_std'].transform(
        lambda x: x.rolling(std_window, min_periods=w).mean()
    )
    vwap_std_std = df.groupby('symbol')['vwap_std'].transform(
        lambda x: x.rolling(std_window, min_periods=w).std()
    )
    df['ts_zscore_vwap_std'] = (df['vwap_std'] - vwap_std_mean) / (vwap_std_std + 1e-8)

    # 4. 计算VOLUME的volume_max_window周期最大值
    df['volume_max'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(volume_max_window, min_periods=w).max()
    )

    # 5. 计算VOLUME最大值的Z-score（std_window周期）
    # 确保分母不为0
    volume_max_mean = df.groupby('symbol')['volume_max'].transform(
        lambda x: x.rolling(std_window, min_periods=w).mean()
    )
    volume_max_std = df.groupby('symbol')['volume_max'].transform(
        lambda x: x.rolling(std_window, min_periods=w).std()
    )
    df['ts_zscore_volume_max'] = (df['volume_max'] - volume_max_mean) / (volume_max_std + 1e-8)

    # 6. GP_MAX操作（比较标准化后的VWAP标准差和VOLUME最大值）
    df['gp_max_result'] = df[['ts_zscore_vwap_std', 'ts_zscore_volume_max']].max(axis=1)

    # 7. 计算HIGH的high_std_window周期标准差
    df['high_std'] = df.groupby('symbol')['high'].transform(
        lambda x: x.rolling(high_std_window, min_periods=w).std()
    )

    # 8. 计算HIGH标准差的Z-score（std_window周期）
    # 确保分母不为0
    high_std_mean = df.groupby('symbol')['high_std'].transform(
        lambda x: x.rolling(std_window, min_periods=w).mean()
    )
    high_std_std = df.groupby('symbol')['high_std'].transform(
        lambda x: x.rolling(std_window, min_periods=w).std()
    )
    df['ts_zscore_high_std'] = (df['high_std'] - high_std_mean) / (high_std_std + 1e-8)

    # 9. 将GP_MAX结果与HIGH标准差Z-score相加
    df['sum_zscores'] = df['gp_max_result'] + df['ts_zscore_high_std']

    # 10. 计算最终final_std_window周期标准差
    df['factor'] = df.groupby('symbol')['sum_zscores'].transform(
        lambda x: x.rolling(final_std_window, min_periods=w).std()
    )

    # 11. 处理无穷值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 12. 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 13. 构造输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

