# Alpha299因子 - factor_2
# 原始因子编号: 2
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_2(
    data_df,
    w: int | None = 1,
    uni_col: str | None = None
):
    """
    计算Alpha12因子，基于成交量与收盘价的1期差分及符号函数。

    参数:
    - data_df: 输入数据DataFrame，必须包含'volume'和'close'列
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为1天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 本因子未使用，强制设为None

    返回:
    - 仅包含['trade_date', 'time', 'symbol', 'factor']列的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'diff_period': 1    # 差分周期
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    diff_period = window_sizes['diff_period']
    
    # 检查必要列是否存在
    if not {'volume', 'close'}.issubset(data_df.columns):
        raise ValueError("输入数据缺少必要列：volume 或 close")

    # 强制uni_col设为None
    if uni_col is not None:
        raise ValueError("本因子不需要uni_col参数，必须设为None")

    df = data_df.copy()

    # 按symbol分组计算差分
    # 对volume和close进行差分，差分操作本身会产生NaN，这是正常的
    df['volume_diff'] = df.groupby('symbol')['volume'].transform(lambda x: x.diff(diff_period))
    df['close_diff'] = df.groupby('symbol')['close'].transform(lambda x: x.diff(diff_period))

    # 计算符号函数与因子值
    # np.sign对NaN的处理是返回NaN，对inf的处理是返回1或-1，这是符合预期的
    # 乘法操作中，如果任一操作数为NaN，结果为NaN；如果操作数为inf和0，结果为NaN；inf和非零数，结果为inf
    # 这里的volume_diff和close_diff可能包含NaN，乘积自然会是NaN
    # 对于inf的情况，如果volume_diff是inf，sign(inf)是1，如果close_diff是inf，-close_diff是-inf或inf
    # 1 * (-inf) = -inf，1 * inf = inf
    # 考虑到原始因子逻辑，inf/nan的出现通常意味着数据异常或差分周期过大导致数据不足
    # 保持原始逻辑，让NaN/inf自然产生，并在最后通过dropna处理是合理的
    df['factor'] = np.sign(df['volume_diff']) * (-df['close_diff'])

    # 严格处理日期时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并删除NaN和inf
    # dropna()会同时删除包含NaN和inf的行
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

