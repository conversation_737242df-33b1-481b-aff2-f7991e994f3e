# Alpha299因子 - factor_356
# 原始因子编号: 356
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_356(data_df, w: int | None = 6, uni_col: str | None = 'close'):
    """
    计算Alpha196因子：TS_CORR(14, <PERSON><PERSON><PERSON><PERSON>, TS_MEAN(VOLUME, 6))

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为14天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 收盘价数据列，默认为'close'

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 14,            # 相关系数窗口
        'volume_mean_window': 6       # 成交量均值窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']
    volume_mean_window = window_sizes['volume_mean_window']

    # 复制原始数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组计算
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 按时间排序
        group = group.sort_values('time')

        # 计算成交量的6周期移动平均
        volume_mean = group['volume'].rolling(window=volume_mean_window, min_periods=w).mean()

        # 计算收盘价与成交量移动平均之间的14周期相关系数
        # 使用rolling.corr方法计算相关系数
        # rolling.corr内部会处理除以标准差的情况，但为了确保万无一失，可以对输入数据进行微小扰动
        # 然而，根据题目要求，只能在除法处添加保护，而rolling.corr的内部实现我们无法直接修改
        # 因此，我们只能依赖pandas的rolling.corr的健壮性，或者在计算相关系数之前对数据进行预处理
        # 考虑到题目要求"保持函数体的其他部分彻底完全不变，只添加除法保护"，我们无法在计算corr之前添加预处理代码
        # rolling.corr的计算公式为 Cov(X, Y) / (Std(X) * Std(Y))
        # 潜在的除零风险在于Std(X)或Std(Y)为零
        # pandas的rolling.corr在计算过程中会处理标准差为零的情况，通常会返回NaN
        # 因此，我们在此处不直接修改rolling.corr的调用，而是依赖其内部实现。
        # 如果需要更严格的保护，可能需要手动计算协方差和标准差，并在除法时添加保护。
        # 但这违反了"保持函数体的其他部分彻底完全不变"的要求。
        # 因此，我们假设pandas的rolling.corr已经包含了足够的除零保护。
        # 如果在其他地方有明确的除法运算，我们会添加保护。

        # 检查是否存在明确的除法运算需要保护
        # 在这个函数中，主要的计算是rolling.mean和rolling.corr
        # rolling.mean是求和后除以窗口大小，窗口大小不会为零
        # rolling.corr内部有除法，但我们不能直接修改
        # 唯一可能需要考虑的是在计算volume_mean_window时，如果w为0，但w的类型是int或None，且默认为14，所以w不会为零。
        # 即使w为0，6 * w / 14 也不会导致除零。

        # 考虑到题目要求，我们只能在明确的除法运算中添加保护。
        # 在这个函数中，没有其他明确的除法运算。
        # rolling.corr的内部实现我们无法修改。

        # 计算收盘价与成交量移动平均之间的14周期相关系数
        factor = group[uni_col].rolling(window=corr_window, min_periods=w).corr(volume_mean)

        # 创建结果DataFrame
        result = pd.DataFrame({
            'trade_date': group['trade_date'],
            'time': group['time'],
            'symbol': symbol,
            'factor': factor
        })

        result_dfs.append(result)

    # 合并所有结果
    result_df = pd.concat(result_dfs, ignore_index=True)

    # 替换无穷大和负无穷大为NaN
    result_df['factor'] = result_df['factor'].replace([float('inf'), float('-inf')], float('nan'))

    # 对于滚动相关系数，如果结果是NaN，通常是因为窗口内数据不足或其中一个序列是常数
    # 根据要求，涉及到滚动.corr，需要将corr出来的结果nan值或者+-inf填0
    # 这里我们只处理NaN和Inf，将它们替换为0
    result_df['factor'] = result_df['factor'].fillna(0)


    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回有效数据
    return result_df[['trade_date', 'time', 'symbol', 'factor']]

