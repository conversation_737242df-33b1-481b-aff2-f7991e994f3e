# Alpha299因子 - factor_361
# 原始因子编号: 361
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_361(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha201因子

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为16天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 不适用于此因子，设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'corr_period': 16,        # 相关系数窗口期
        'pct_chg_period': 12,     # 百分比变化窗口期
        'delta_period': 10        # 差值窗口期
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    corr_period = window_sizes['corr_period']
    pct_chg_period = window_sizes['pct_chg_period']
    delta_period = window_sizes['delta_period']

    # 复制输入数据以避免修改原始数据
    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组进行计算
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')

        # 1. 计算ARCTAN(LOW)
        # 确保输入arctan的值大于0，避免潜在问题
        group['arctan_low'] = group['low'].apply(lambda x: atan(x + 1e-8))

        # 2. 计算TS_PCTCHG(OPEN, 12)
        group['open_shifted'] = group['open'].shift(pct_chg_period)
        # 避免除以0
        group['pct_chg_open'] = (group['open'] - group['open_shifted']) / (group['open_shifted'] + 1e-8)

        # 3. 计算DELTA(VOLUME, 10)
        group['volume_shifted'] = group['volume'].shift(delta_period)
        group['delta_volume'] = group['volume'] - group['volume_shifted']

        # 4. 计算GP_MIN(TS_PCTCHG(OPEN, 12), DELTA(VOLUME, 10))
        group['gp_min'] = np.minimum(group['pct_chg_open'], group['delta_volume'])

        # 5. 计算TS_CORR(16, ARCTAN(LOW), GP_MIN)
        # 使用pandas内置的rolling.corr方法计算滚动相关系数
        # 考虑到滚动窗口内可能出现常数序列或包含NaN/Inf的情况，直接使用corr方法，并在结果中处理NaN/Inf
        group['factor'] = group['arctan_low'].rolling(window=corr_period, min_periods=w).corr(group['gp_min'])

        # 替换无穷大和负无穷大为NaN，并将NaN填充为0，因为滚动相关系数为NaN通常意味着窗口内数据不足或为常数，此时相关性可视为0
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan).fillna(0)


        # 添加到结果列表
        result_dfs.append(group[['trade_date', 'time', 'symbol', 'factor']])

    # 合并所有结果
    if result_dfs:
        result_df = pd.concat(result_dfs)
    else:
        result_df = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果，保留NaN值
    return result_df

