# Alpha299因子 - factor_5
# 原始因子编号: 5
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_5(data_df, w: int | None = 7, uni_col: str | None = 'close'):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 7        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    # 检查必要列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'close']
    for col in required_columns:
        if col not in data_df.columns:
            raise ValueError(f"输入数据必须包含列: {col}")

    df = data_df.copy()

    # 确保数据按symbol和时间排序
    df.sort_values(by=['symbol', 'trade_date', 'time'], inplace=True)

    # 计算收益率（简单收益率）
    # 避免除以0的情况，虽然pct_change内部有处理，但为了代码健壮性，可以考虑对close+epsilon
    df['RETURNS'] = df.groupby('symbol')[uni_col].transform(lambda x: x.pct_change())

    # 参数推导
    delay_window = w
    diff_window = w
    sum_window = int(w / 7 * 250)  # 因子公式中指定250期

    # 计算CLOSE的延迟和差分
    df['delayed_close'] = df.groupby('symbol')[uni_col].transform(lambda x: x.shift(delay_window))
    df['delta_close'] = df.groupby('symbol')[uni_col].transform(lambda x: x - x.shift(diff_window))

    # 计算CLOSE延迟和差分的和
    df['sum_part'] = df['delayed_close'] + df['delta_close']

    # 取符号
    # np.sign(0) = 0, 这是符合预期的
    df['sign_part'] = np.sign(df['sum_part'])

    # 计算RETURNS的sum_window期累计和
    # rolling().sum() 会自动处理NaN，但如果窗口内全是NaN，结果是NaN。这里不做特殊处理，保留NaN。
    df['returns_sum'] = df.groupby('symbol')['RETURNS'].transform(lambda x: x.rolling(window=sum_window, min_periods=w).sum())

    # 加1后排名（使用expanding rank避免未来数据泄露）
    df['returns_plus_one'] = df['returns_sum'] + 1
    # expanding().rank() 会自动处理NaN，NaN的排名结果是NaN。这里不做特殊处理，保留NaN。
    df['rank_returns'] = df.groupby('symbol')['returns_plus_one'].transform(
        lambda x: x.expanding().rank(method='average', pct=False)
    )

    # 计算rank_part = 1 + rank
    df['rank_part'] = 1 + df['rank_returns']

    # 计算最终因子
    # 乘法操作，如果任一操作数为NaN，结果为NaN。如果sign_part为0，结果为0。这符合预期。
    df['factor'] = - df['sign_part'] * df['rank_part']

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并删除缺失值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

