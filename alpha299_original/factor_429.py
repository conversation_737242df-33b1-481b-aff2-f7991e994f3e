# Alpha299因子 - factor_429
# 原始因子编号: 429
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_429(data_df, w: int | None = 5, uni_col: str | None = 'close'):
    """
    计算方差(Varian<PERSON>, VAR)因子

    参数:
    - data_df: 输入数据框
    - w: 回看周期，默认为5。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    - uni_col: 用于计算方差的列，默认为'close'

    返回:
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'variance_window': 5
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    variance_window = window_sizes['variance_window']

    # 创建数据副本
    df = data_df.copy()

    # 确保日期列为datetime类型用于计算
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 方差计算
    # 使用公式: VAR_t = E[X^2]_t - (E[X]_t)^2
    def calculate_variance(series):
        # 确保输入序列没有inf或nan，并处理可能为常数的情况
        series_cleaned = series.replace([np.inf, -np.inf], np.nan)
        # 计算均值
        mean_value = series_cleaned.rolling(window=variance_window, min_periods=w).mean()
        # 计算平方的均值
        mean_square = (series_cleaned ** 2).rolling(window=variance_window, min_periods=w).mean()
        # 计算方差
        variance = mean_square - (mean_value ** 2)
        # 处理计算出的方差可能由于浮点数精度问题为负数的情况，以及inf/nan
        variance = variance.apply(lambda x: max(0, x) if pd.notna(x) else np.nan)
        return variance

    # 按symbol分组计算方差
    df['factor'] = df.groupby('symbol')[uni_col].transform(calculate_variance)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 只保留需要的列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

