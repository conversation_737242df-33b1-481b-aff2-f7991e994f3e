# Alpha299因子 - factor_18
# 原始因子编号: 18
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_18(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha因子：基于OPEN和VOLUME排名的负相关系数因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'rolling_window': 10    # OPEN、VOLUME排名和相关系数计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    rolling_window = window_sizes['rolling_window']

    # 检查必要列是否存在
    required_columns = ['open', 'volume']
    if not all(col in data_df.columns for col in required_columns):
        raise ValueError(f"输入数据缺少必要列: {required_columns}")

    df = data_df.copy()

    # 按symbol和trade_date排序确保时间序列正确
    df = df.sort_values(by=['symbol', 'trade_date'])

    # 使用滚动窗口计算OPEN和VOLUME的排名（避免全局排序）
    def rolling_rank(group):
        # 确保输入到rankdata的数据没有inf或nan，虽然rankdata有nan_policy='omit'，但为了稳妥
        group_cleaned = group.replace([np.inf, -np.inf], np.nan).dropna()
        if len(group_cleaned) == 0:
            return pd.Series(np.nan, index=group.index)
        return group.rolling(window=rolling_window, min_periods=w).apply(
            lambda x: rankdata(x.replace([np.inf, -np.inf], np.nan).dropna(), method='average')[-1] if len(x.replace([np.inf, -np.inf], np.nan).dropna()) > 0 else np.nan,
            raw=False
        )

    df['open_rank'] = df.groupby('symbol')['open'].transform(rolling_rank)
    df['volume_rank'] = df.groupby('symbol')['volume'].transform(rolling_rank)

    # 计算滚动相关系数（使用当前及之前的历史数据）
    def rolling_corr(group):
        open_rank = group['open_rank']
        volume_rank = group['volume_rank']
        # 使用pandas内置的rolling.corr，它会自动处理nan，但对于常数序列corr为nan，需要特殊处理
        corr_result = open_rank.rolling(window=rolling_window, min_periods=w).corr(volume_rank)
        # 将计算出的nan或inf替换为0，因为常数序列的相关性可以视为0
        corr_result = corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)
        return corr_result

    df['corr'] = df.groupby('symbol').apply(rolling_corr).reset_index(level=0, drop=True)

    # 计算因子值并取负
    df['factor'] = -df['corr']

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并删除无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

