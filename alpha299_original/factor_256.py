# Alpha299因子 - factor_256
# 原始因子编号: 256
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_256(data_df, w: int | None = 8, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 8,      # 协方差计算窗口
        'rank_window': 12     # 排名计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    cov_window = window_sizes['cov_window']      # 协方差计算窗口
    rank_window = window_sizes['rank_window']    # 排名计算窗口

    df = data_df.copy()

    # 计算VWAP
    df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3.0
    df['cum_typical_vol'] = df.groupby('symbol')['typical_price'].transform(lambda x: (x * df['volume']).cumsum())
    df['cum_volume'] = df.groupby('symbol')['volume'].transform('cumsum')
    df['VWAP'] = df['cum_typical_vol'] / (df['cum_volume'] + 1e-8)

    # 计算TS_RANK(VOLUME, rank_window)
    def compute_rank(x):
        # 过滤掉nan和inf，避免rankdata出错
        valid_x = x[np.isfinite(x)]
        if len(valid_x) == 0:
            return np.nan
        return rankdata(valid_x, method='min', nan_policy='omit')[-1] / (len(valid_x) + 1e-8)

    df['TS_RANK_VOLUME'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=rank_window, min_periods=w).apply(compute_rank, raw=False))

    # 计算TS_COV(cov_window, VWAP, TS_RANK_VOLUME)
    # 使用apply处理分组，并在cov计算后处理可能的inf和nan
    df['TS_COV'] = df.groupby('symbol').apply(lambda g: g['VWAP'].rolling(window=cov_window, min_periods=w).cov(g['TS_RANK_VOLUME'])).reset_index(level=0, drop=True)

    # 处理无穷值和nan，将inf/-inf/nan替换为0
    df['TS_COV'] = df['TS_COV'].replace([np.inf, -np.inf, np.nan], 0)


    # 重命名并格式化输出
    df.rename(columns={'TS_COV': 'factor'}, inplace=True)
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

