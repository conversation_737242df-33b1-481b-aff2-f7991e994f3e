# Alpha299因子 - factor_115
# 原始因子编号: 115
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_115(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha 157因子：基于修正价格范围的加权指标
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数，本因子涉及多列，设为None
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'w1': 6,    # 第一个窗口期
        'w2': 12,   # 第二个窗口期 (2*w)
        'w3': 24    # 第三个窗口期 (4*w)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    w1 = window_sizes['w1']
    w2 = window_sizes['w2']
    w3 = window_sizes['w3']

    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'low', 'high', 'close']
    for col in required_cols:
        if col not in data_df.columns:
            raise KeyError(f"Missing required column: {col}")

    df = data_df.copy()
    df = df.sort_values(by=['symbol', 'time'])

    # 计算close_lag1
    df['close_lag1'] = df.groupby('symbol')['close'].shift(1)

    # 计算修正最低价和最高价
    df['L_prime'] = df[['low', 'close_lag1']].min(axis=1)
    df['H_prime'] = df[['high', 'close_lag1']].max(axis=1)
    df['Range_prime'] = df['H_prime'] - df['L_prime']

    # 计算Term(w1)
    sum_L_w1 = df.groupby('symbol')['L_prime'].rolling(window=w1, min_periods=w).sum().reset_index(level=0, drop=True)
    sum_Range_w1 = df.groupby('symbol')['Range_prime'].rolling(window=w1, min_periods=w).sum().reset_index(level=0, drop=True)
    numerator_w1 = df['close'] - sum_L_w1
    denominator_w1 = sum_Range_w1
    term_w1 = numerator_w1 / (denominator_w1 + 1e-8)
    term_w1 = term_w1.replace([np.inf, -np.inf], np.nan)

    # 计算Term(w2)
    sum_L_w2 = df.groupby('symbol')['L_prime'].rolling(window=w2, min_periods=w).sum().reset_index(level=0, drop=True)
    sum_Range_w2 = df.groupby('symbol')['Range_prime'].rolling(window=w2, min_periods=w).sum().reset_index(level=0, drop=True)
    numerator_w2 = df['close'] - sum_L_w2
    denominator_w2 = sum_Range_w2
    term_w2 = numerator_w2 / (denominator_w2 + 1e-8)
    term_w2 = term_w2.replace([np.inf, -np.inf], np.nan)

    # 计算Term(w3)
    sum_L_w3 = df.groupby('symbol')['L_prime'].rolling(window=w3, min_periods=w3).sum().reset_index(level=0, drop=True)
    sum_Range_w3 = df.groupby('symbol')['Range_prime'].rolling(window=w3, min_periods=w3).sum().reset_index(level=0, drop=True)
    numerator_w3 = df['close'] - sum_L_w3
    denominator_w3 = sum_Range_w3
    term_w3 = numerator_w3 / (denominator_w3 + 1e-8)
    term_w3 = term_w3.replace([np.inf, -np.inf], np.nan)

    # 计算加权项
    X = term_w1 * w2 * w3
    Y = term_w2 * w1 * w3
    Z = term_w3 * w1 * w2

    # 最终因子计算
    total_weight = w1 * w2 + w1 * w3 + w2 * w3
    df['factor'] = (X + Y + Z) * (100 / (total_weight + 1e-8))
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)


    # 格式化日期和时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并移除无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

