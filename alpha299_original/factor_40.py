# Alpha299因子 - factor_40
# 原始因子编号: 40
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_40(data_df, w: int | None = 5, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 5        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    # 参数联动
    sum_window = w
    delay_window = 2 * w
    z_window = 4 * w

    df = data_df.copy()

    # 检查必要列是否存在
    if 'open' not in df.columns or 'close' not in df.columns:
        raise ValueError("数据中缺少必要的列 'open' 或 'close'")

    # 确保数据按交易日期排序
    df = df.sort_values(['symbol', 'trade_date'])

    # 计算收益率（RETURN）
    df['RETURN'] = df.groupby('symbol')['close'].transform(lambda x: x.pct_change())

    # 计算OPEN和RETURN的5期累和
    df['open_sum'] = df.groupby('symbol')['open'].transform(
        lambda x: x.rolling(window=sum_window, min_periods=w).sum()
    )
    df['return_sum'] = df.groupby('symbol')['RETURN'].transform(
        lambda x: x.rolling(window=sum_window, min_periods=w).sum()
    )

    # 计算乘积
    df['product'] = df['open_sum'] * df['return_sum']

    # 计算10期延迟值
    df['delayed_product'] = df.groupby('symbol')['product'].transform(
        lambda x: x.shift(delay_window)
    )

    # 计算差值
    df['difference'] = df['product'] - df['delayed_product']

    # 计算20期滚动标准化值（Z-score）
    df['difference_mean'] = df.groupby('symbol')['difference'].transform(
        lambda x: x.rolling(window=z_window, min_periods=w).mean()
    )
    df['difference_std'] = df.groupby('symbol')['difference'].transform(
        lambda x: x.rolling(window=z_window, min_periods=w).std()
    )
    # 添加对标准差为0的保护
    df['z_score'] = (df['difference'] - df['difference_mean']) / (df['difference_std'].replace(0, 1e-8) + 1e-8)

    # 修复点：使用滚动窗口排名代替全局排名
    df['rank'] = df.groupby('symbol')['z_score'].transform(
        lambda x: x.rolling(window=z_window, min_periods=w).rank()
    )
    df['factor'] = -df['rank']

    # 严格恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去除NaN
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

