# Alpha299因子 - factor_202
# 原始因子编号: 202
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_202(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha114因子：基于close排名、vwap对数和回归残差的复合因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'window1': 6,    # ts_rank窗口
        'window2': 8     # ts_regres窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    window1 = window_sizes['window1']  # ts_rank窗口
    window2 = window_sizes['window2']  # ts_regres窗口

    # 数据预处理
    df = data_df.copy()
    df = df.sort_values(['symbol', 'trade_date', 'time'])  # 按symbol和时间排序

    # 计算vwap（成交量加权平均价）
    df['vwap'] = df.groupby('symbol').apply(
        lambda group: (group['close'] * group['volume']).cumsum() / (group['volume'].cumsum() + 1e-8)
    ).reset_index(level=0, drop=True)

    # 1. 计算ts_rank(close, window1)
    df['T1'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=window1, min_periods=w).apply(
            lambda s: rankdata(-s, method='max', nan_policy='omit')[0] / (window1 + 1e-8) if len(s) > 0 else np.nan,
            raw=False
        )
    )

    # 2. 计算log(vwap)
    # 对vwap取绝对值并加上一个很小的数，避免log(0)或log(负数)
    df['T2'] = np.log(np.abs(df['vwap']) + 1e-8)

    # 3. 计算gp_max(T1, T2)
    df['X1'] = df[['T1', 'T2']].max(axis=1)

    # 4. 计算ts_regres(X1, vwap, window2)
    def calc_residual(group):
        # 滚动窗口计算残差
        X = group['X1'].values
        Y = group['vwap'].values

        def rolling_regression(x, y):
            # 手动实现滚动线性回归残差
            if np.isnan(x).any() or np.isnan(y).any() or len(x) < window2:
                return np.nan

            # 确保输入是二维的，以便np.cov正确处理
            stacked_data = np.vstack((x, y))

            # 计算协方差和方差
            # 使用 try-except 块处理协方差计算可能出现的异常
            try:
                cov_matrix = np.cov(stacked_data, ddof=0)
                cov = cov_matrix[0, 1]
                var = np.var(x, ddof=0)
            except Exception:
                return np.nan # 协方差计算失败则返回nan

            if var == 0:
                return np.nan  # 避免除以零，如果X是常数序列则无法回归

            beta = cov / (var + 1e-8)
            residual = y - beta * x
            return residual[-1]  # 返回当前周期的残差

        result = np.zeros(len(X))
        for i in range(len(X)):
            if i < window2 - 1:
                result[i] = np.nan
            else:
                result[i] = rolling_regression(X[max(0, i - window2 + 1):i + 1], Y[max(0, i - window2 + 1):i + 1])

        # 后处理，将计算出的残差中的inf和nan替换为0
        result[np.isinf(result)] = np.nan
        # result[np.isnan(result)] = 0 # 根据要求，不在这里填充0

        return pd.Series(result, index=group.index)

    df['factor'] = df.groupby('symbol').apply(calc_residual).reset_index(level=0, drop=True)

    # 5. 处理无穷大值和NaN
    df['factor'] = pd.to_numeric(df['factor'], errors='coerce')
    # df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan) # to_numeric with coerce handles this

    # 日期时间格式处理
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

