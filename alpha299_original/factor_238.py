# Alpha299因子 - factor_238
# 原始因子编号: 238
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_238(
    data_df,
    w: int | None = 5,
    uni_col: str | None = None
):
    """
    计算Alpha_167因子：ts_max(ts_corr(16, volume, high), 5)
    参数说明：
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为16天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 本因子不依赖单一基础列，因此设为None
    """
    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 16,  # ts_corr窗口
        'max_window': 5     # ts_max窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']
    max_window = window_sizes['max_window']
    
    # 深拷贝数据确保原始数据不被修改
    df = data_df.copy()

    # 检查并转换数值列
    required_numeric_cols = ['volume', 'high']
    for col in required_numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            if df[col].dtype != np.float64:
                raise ValueError(f"列 {col} 转换后类型不为float64，当前类型: {df[col].dtype}")
        else:
            raise KeyError(f"数据中缺少必要列: {col}")

    # 按symbol分组，时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 计算滚动相关系数
    def rolling_corr(group):
        # 使用pandas内置的rolling.corr方法直接计算两列的相关系数
        # pandas的rolling.corr方法内部已经处理了除零问题，无需额外添加保护
        # 考虑到滚动窗口内可能出现常数序列或包含inf/nan的情况导致corr为nan
        # 这里对结果进行后处理，将nan和inf替换为0
        corr_result = group['volume'].rolling(window=corr_window, min_periods=w).corr(group['high'])
        return corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    # 计算滚动最大值
    def rolling_max(group):
        return group.rolling(window=max_window, min_periods=w).max()

    # 分组计算
    df['corr_16'] = df.groupby('symbol').apply(rolling_corr).reset_index(level=0, drop=True)
    df['factor'] = df.groupby('symbol')['corr_16'].transform(rolling_max)

    # 处理NaN和无效值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 格式化日期时间字段
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并清理无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

