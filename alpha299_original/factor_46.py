# Alpha299因子 - factor_46
# 原始因子编号: 46
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_46(data_df, w: int | None = 20, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 20        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    """
    计算Alpha 107优化版因子，包含局部基准消除的ts_zscore变换和横截面排序

    参数:
        data_df (pd.DataFrame): 输入数据，必须包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close']
        w (int | None): 时间序列滚动窗口（天数），默认20
        uni_col (str | None): 本因子不依赖单一基础列，因此设为None
    """
    df = data_df.copy()

    # 确保时间顺序正确
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 计算前一日的high, close, low
    df['high_lag1'] = df.groupby('symbol')['high'].shift(1)
    df['close_lag1'] = df.groupby('symbol')['close'].shift(1)
    df['low_lag1'] = df.groupby('symbol')['low'].shift(1)

    # 计算差值
    df['open_diff_high'] = df['open'] - df['high_lag1']
    df['open_diff_close'] = df['open'] - df['close_lag1']
    df['open_diff_low'] = df['open'] - df['low_lag1']

    # 计算滚动Z-score (20天窗口)
    def rolling_zscore(x):
        # 避免除以零，增加一个小的常数
        return (x - x.rolling(window=w, min_periods=w).mean()) / (x.rolling(window=w, min_periods=w).std() + 1e-8)

    df['z1'] = df.groupby('symbol')['open_diff_high'].transform(rolling_zscore)
    df['z2'] = df.groupby('symbol')['open_diff_close'].transform(rolling_zscore)
    df['z3'] = df.groupby('symbol')['open_diff_low'].transform(rolling_zscore)

    # 横截面排序
    def cross_sectional_rank(group):
        # 确保输入没有inf或nan，rank函数本身会处理nan，但为了稳健性，可以先替换
        group = group.replace([float('inf'), -float('inf')], float('nan'))
        return group.rank(pct=True)

    # 按时间点分组进行横截面排序
    df = df.sort_values(['trade_date', 'time', 'symbol'])
    df['rank_z1'] = df.groupby(['trade_date', 'time'])['z1'].transform(cross_sectional_rank)
    df['rank_z2'] = df.groupby(['trade_date', 'time'])['z2'].transform(cross_sectional_rank)
    df['rank_z3'] = df.groupby(['trade_date', 'time'])['z3'].transform(cross_sectional_rank)

    # 计算最终因子值
    df['factor'] = (-df['rank_z1']) * df['rank_z2'] * df['rank_z3']

    # 替换inf为NaN
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 转换日期时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

