# Alpha299因子 - factor_192
# 原始因子编号: 192
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_192(data_df, w: int | None = 11, uni_col: str | None = None):
    """
    计算Alpha 101因子，基于优化后的局部基准处理逻辑

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要字段
        w (int): 核心可调参数，单位为天，默认20
        uni_col (str): 单一基础数据列，此处设为None

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果
    """
    # 窗口配置
    window_configs = {
        'n_ma': 30.0,                    # int(1.5 * w) = 1.5 * 20 = 30
        'sum_days': 37.0,                # int(1.85 * w) = 1.85 * 20 = 37
        'correlation_window1': 15.0,     # int(0.75 * w) = 0.75 * 20 = 15  
        'correlation_window2': 11.0,     # 固定窗口11，与w无关
        'zscore_window': 20.0            # w = 20，Z-score窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n_ma = window_sizes['n_ma']
    sum_days = window_sizes['sum_days']
    correlation_window1 = window_sizes['correlation_window1']
    correlation_window2 = window_sizes['correlation_window2']
    zscore_window = window_sizes['zscore_window']
    
    df = data_df.copy()

    # 计算VWAP（基于close和volume）
    df['vwap'] = df.groupby(['symbol', 'trade_date']).apply(
        lambda group: (group['close'] * group['volume']).cumsum() / (group['volume'].cumsum() + 1e-8)
    ).droplevel(['symbol', 'trade_date']).astype(np.float64)

    # 步骤1: 计算日均值的求和（使用实际窗口大小）
    df['MA_VOL'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=n_ma, min_periods=w).mean()
    )
    df['SumMAVol'] = df.groupby('symbol')['MA_VOL'].transform(
        lambda x: x.rolling(window=sum_days, min_periods=w).sum()
    )

    # 步骤2: 计算收盘价与SumMAVol的相关系数（使用实际窗口大小）
    def rolling_corr(group, col1, col2, window):
        # 增加对常数序列和inf/nan的处理
        corr_result = group[col1].rolling(window=window, min_periods=w).corr(group[col2])
        # 将NaN和inf填充为0，因为常数序列相关系数无意义，inf/nan导致无法计算也应视为无相关
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    df['Corr_1'] = df.groupby('symbol').apply(
        lambda g: rolling_corr(g, 'close', 'SumMAVol', correlation_window1)
    ).droplevel('symbol').astype(np.float64)

    # 步骤3: 横截面排序R1
    df['R_1'] = df.groupby('time')['Corr_1'].transform(lambda x: x.rank(pct=True))

    # 步骤4: 计算混合价格
    df['P_mix'] = 0.1 * df['high'] + 0.9 * df['vwap']

    # 步骤5: 横截面排序P_mix
    df['RankP_mix'] = df.groupby('time')['P_mix'].transform(lambda x: x.rank(pct=True))

    # 步骤6: 计算Z-score标准化成交量（使用实际窗口大小）
    df['ZScoreVol'] = df.groupby('symbol')['volume'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                 (x.rolling(window=zscore_window, min_periods=w).std() + 1e-8) # 分母加1e-8防止除以0
    )
    # 处理ZScoreVol中的inf和nan，例如常数序列std为0
    df['ZScoreVol'] = df['ZScoreVol'].replace([np.inf, -np.inf], np.nan)


    # 步骤7: 横截面排序ZScoreVol
    df['RankVol'] = df.groupby('time')['ZScoreVol'].transform(lambda x: x.rank(pct=True))

    # 步骤8: 计算RankP_mix与RankVol的相关系数（使用实际窗口大小）
    df['Corr_2'] = df.groupby('symbol').apply(
        lambda g: rolling_corr(g, 'RankP_mix', 'RankVol', correlation_window2) # 使用修正后的rolling_corr
    ).droplevel('symbol').astype(np.float64)

    # 步骤9: 横截面排序R2
    df['R_2'] = df.groupby('time')['Corr_2'].transform(lambda x: x.rank(pct=True))

    # 步骤10: 最终因子值（修正数据类型转换）
    df['factor'] = -1 * (df['R_1'] < df['R_2']).astype(np.int64)

    # 处理无效值（无穷大）
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建结果DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

