# Alpha299因子 - factor_23
# 原始因子编号: 23
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_23(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha因子：基于VOLUME排名与HIGH相关系数的负值因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'rolling_window': 5    # VOLUME排名和相关系数计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    rolling_window = window_sizes['rolling_window']

    df = data_df.copy()

    # 按symbol和时间排序
    df = df.sort_values(['symbol', 'time'])
    df.reset_index(drop=True, inplace=True)  # 重置索引避免重复标签

    # 计算VOLUME的滚动排名（每个窗口内取最后一个排名值）
    # 确保volume非负，虽然排名不受影响，但保持一致性
    df['volume_protected'] = df['volume'].apply(lambda x: x if x >= 0 else np.nan)
    df['rank_volume'] = df.groupby('symbol')['volume_protected'].transform(
        lambda x: x.rolling(rolling_window, min_periods=w).apply(
            lambda y: rankdata(y, method='average', nan_policy='omit')[-1] if len(y) == rolling_window and not np.all(y == y[0]) else np.nan,
            raw=True
        )
    )

    # 计算HIGH与rank_volume的滚动相关系数
    def rolling_corr_protected(group):
        # 确保high和rank_volume在计算corr前没有inf或nan
        group_cleaned = group[['high', 'rank_volume']].replace([np.inf, -np.inf], np.nan).dropna()
        if len(group_cleaned) < rolling_window:
            return pd.Series([np.nan] * len(group), index=group.index)

        # 计算滚动相关系数
        corr_series = group_cleaned['high'].rolling(rolling_window, min_periods=w).corr(group_cleaned['rank_volume'])

        # 将计算出的corr结果中的nan或inf替换为0
        corr_series = corr_series.replace([np.inf, -np.inf, np.nan], 0)

        # 创建一个与原始group长度相同的Series，用于对齐索引
        result = pd.Series([np.nan] * len(group), index=group.index)
        
        # 将有效的相关系数值填入对应位置
        for idx in corr_series.index:
            if idx in result.index:
                result.loc[idx] = corr_series.loc[idx]

        return result

    df['corr'] = df.groupby('symbol').apply(rolling_corr_protected).reset_index(level=0, drop=True)

    # 取负得到最终因子值
    df['factor'] = -df['corr']

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并去重
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

