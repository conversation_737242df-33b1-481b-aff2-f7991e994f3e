# Alpha299因子 - factor_145
# 原始因子编号: 145
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_145(data_df, w: int | None = 2, uni_col: str | None = 'high'):
    """
    计算Alpha 38因子：条件高点价格变化因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含symbol, trade_date, time, high等列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列（默认'high'）

    返回:
        pd.DataFrame: 包含trade_date, time, symbol, factor四列的因子结果
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'ma_window': 20,  # 移动平均窗口
        'delta_window': 2  # 差分窗口 (0.1 * w)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    ma_window = window_sizes['ma_window']
    delta_window = window_sizes['delta_window']

    df = data_df.copy()

    # 分组计算移动平均
    # 滚动平均本身对NaN有处理，min_periods保证了窗口内有足够数据才计算
    df['ma'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=ma_window, min_periods=w).mean()
    )

    # 计算条件：当前最高价 > 移动均线
    # 条件计算本身不会产生inf/nan，除非输入是inf/nan
    condition = df[uni_col] > df['ma']

    # 计算差分：当前值 - delta_window天前的值
    # shift操作可能引入NaN，但不会引入inf
    df['delta_high'] = df[uni_col] - df.groupby('symbol')[uni_col].shift(delta_window)

    # 根据条件计算因子值
    # np.where根据条件选择值，如果condition, -df['delta_high'], 0中任何一个输入是inf/nan，结果可能为inf/nan
    # 这里delta_high可能为nan，0不会是inf/nan，condition是布尔值
    df['factor'] = np.where(condition, -df['delta_high'], 0)

    # 处理原始high为NaN的位置
    # 如果原始high是NaN，那么ma和delta_high很可能也是NaN，np.where的结果也可能是NaN
    # 这一步是额外的保险，确保原始数据缺失时因子也缺失
    df['factor'] = df['factor'].where(df[uni_col].notna(), np.nan)

    # 替换±∞为NaN
    # 尽管前面的计算步骤不太可能直接产生inf，但为了鲁棒性，保留此步骤
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    # dropna()会移除包含NaN的行，这符合因子计算后通常的处理方式
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

