# Alpha299因子 - factor_415
# 原始因子编号: 415
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_415(data_df, w: int | None = 5, uni_col: str | None = 'close'):
    """
    计算随机相对强弱指数K值 (StochRSIK)

    参数:
    - data_df: 输入数据DataFrame
    - w: RSI的周期长度，默认为14。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    - uni_col: 用于计算的价格列，默认为'close'

    返回:
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'rsi_window': 14,
        'stoch_window': 5
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    n_rsi = window_sizes['rsi_window']
    n_k = window_sizes['stoch_window']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组计算
    result_dfs = []
    for symbol, group in df.groupby('symbol'):
        # 按时间排序
        group = group.sort_values('time')

        # 计算价格变化
        price_changes = group[uni_col].diff()

        # 分离上涨和下跌
        up_moves = np.where(price_changes > 0, price_changes, 0)
        down_moves = np.where(price_changes < 0, -price_changes, 0)

        # 计算初始的平均上涨和平均下跌
        avg_up = np.zeros_like(price_changes)
        avg_down = np.zeros_like(price_changes)

        # 初始化第n_rsi个位置的平均值（使用简单平均）
        if len(up_moves) >= n_rsi:
            # 确保切片范围有效
            if n_rsi > 0:
                avg_up[n_rsi-1] = np.mean(up_moves[0:n_rsi])
                avg_down[n_rsi-1] = np.mean(down_moves[0:n_rsi])
            else:
                 # 如果n_rsi为0或负数，无法计算平均值，跳过初始化
                 pass

        # 使用Wilder's平滑法计算后续的平均上涨和平均下跌
        # 确保n_rsi > 0
        if n_rsi > 0:
            for i in range(n_rsi, len(price_changes)):
                avg_up[i] = (avg_up[i-1] * (n_rsi-1) + up_moves[i]) / (n_rsi + 1e-8)
                avg_down[i] = (avg_down[i-1] * (n_rsi-1) + down_moves[i]) / (n_rsi + 1e-8)
        else:
            # 如果n_rsi为0或负数，无法计算平滑平均，avg_up和avg_down保持为0
            pass

        # 计算相对强度(RS)
        rs = np.zeros_like(price_changes)
        mask = avg_down != 0  # 避免除以零
        rs[mask] = avg_up[mask] / (avg_down[mask] + 1e-8)

        # 计算RSI
        rsi = np.zeros_like(price_changes)
        # 确保1 + rs[mask] 不为0
        rsi[mask] = 100 - (100 / (1 + rs[mask] + 1e-8))
        rsi[~mask] = 100  # 当avg_down为0时，RSI为100

        # 计算StochRSIK
        stoch_rsi_k = np.zeros_like(rsi)

        # 对每个位置计算StochRSIK
        # 确保n_rsi + n_k - 1 >= 0
        start_index = max(0, n_rsi + n_k - 1)
        for i in range(start_index, len(rsi)):
            # 确保切片范围有效
            if i - n_k + 1 >= 0:
                min_rsi = np.min(rsi[i-n_k+1:i+1])
                max_rsi = np.max(rsi[i-n_k+1:i+1])

                stoch_rsi_k[i] = 100 * (rsi[i] - min_rsi) / (max_rsi - min_rsi + 1e-8)
            else:
                # 如果切片范围无效，跳过计算
                pass

        # 创建结果DataFrame
        result_df = pd.DataFrame({
            'trade_date': group['trade_date'],
            'time': group['time'],
            'symbol': symbol,
            'factor': stoch_rsi_k
        })

        result_dfs.append(result_df)

    # 合并所有结果
    if result_dfs:
        final_result = pd.concat(result_dfs, ignore_index=True)
    else:
        final_result = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    # 恢复日期和时间格式为字符串
    final_result['trade_date'] = final_result['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    final_result['time'] = final_result['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 去除无效值
    final_result = final_result.replace([np.inf, -np.inf], np.nan).dropna()

    return final_result

