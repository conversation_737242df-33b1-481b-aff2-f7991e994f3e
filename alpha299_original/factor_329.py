# Alpha299因子 - factor_329
# 原始因子编号: 329
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_329(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha110因子

    参数:
    data_df: 输入数据
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为18天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 单一列参数，本因子不使用单一列，设为None

    返回:
    包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 18,     # 相关系数窗口
        'max_window': 12,      # 滚动最大值窗口
        'delta_window': 6,     # delta差值窗口
        'zscore_window': 20    # 标准化窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']
    max_window = window_sizes['max_window']
    delta_window = window_sizes['delta_window']
    zscore_window = window_sizes['zscore_window']

    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 计算步骤1: 计算成交量的绝对值的平方根
    # 确保volume非负，虽然np.sqrt(np.abs)已经处理了，但为了稳妥
    df['sqrt_volume'] = np.sqrt(np.abs(df['volume'] + 1e-8))

    # 计算步骤2: 取sqrt_volume和open中较小值
    df['min_sqrt_volume_open'] = np.minimum(df['sqrt_volume'], df['open'])

    # 计算步骤3: 计算成交量在过去max_window个周期内的滚动最大值
    df['volume_max'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=max_window, min_periods=w).max()
    )

    # 计算步骤4: 计算min_sqrt_volume_open和volume_max在过去corr_window个周期内的滚动相关系数
    def rolling_corr(group):
        x = group['min_sqrt_volume_open']
        y = group['volume_max']
        # 确保输入corr的数据没有inf/nan，并且处理std=0的情况
        # pandas corr会自动处理nan，但std=0时会返回nan，这里后处理
        corr_result = x.rolling(window=corr_window, min_periods=w).corr(y)
        # 将NaN和inf替换为0，因为常数序列与任何序列的相关系数为0
        corr_result = corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)
        return pd.Series(
            corr_result,
            index=group.index
        )

    df['corr_result'] = df.groupby('symbol').apply(rolling_corr).reset_index(level=0, drop=True)

    # 计算步骤5: 计算corr_result的相反数
    df['neg_corr'] = -df['corr_result']

    # 计算步骤6: 取low和volume中较小值
    df['min_low_volume'] = np.minimum(df['low'], df['volume'])

    # 计算步骤7: 计算min_low_volume在过去delta_window个周期内的差值
    df['delta_min_low_volume'] = df.groupby('symbol')['min_low_volume'].transform(
        lambda x: x - x.shift(delta_window)
    )

    # 计算步骤8: 对delta_min_low_volume进行过去zscore_window个周期内的滚动Z-score标准化
    df['zscore_delta_min_low_volume'] = df.groupby('symbol')['delta_min_low_volume'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std() + 1e-8) # 避免除以0
    )
    # Z-score结果可能出现inf/nan，进行后处理
    df['zscore_delta_min_low_volume'] = df['zscore_delta_min_low_volume'].replace([np.inf, -np.inf], np.nan).fillna(0)

    # 计算步骤9: 计算neg_corr与zscore_delta_min_low_volume的和得到Alpha110
    df['factor'] = df['neg_corr'] + df['zscore_delta_min_low_volume']

    # 计算步骤10: 将结果中的无穷大值替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

