# Alpha299因子 - factor_237
# 原始因子编号: 237
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_237(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha_165因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'volume', 'amount', 'high']等字段
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为4天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 用于指定单一可替换基础数据列 (默认值: None)
    
    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子计算结果
    """
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 12,      # ts_cov窗口
        'regbeta_window': 16,  # ts_regbeta窗口
        'mean_window': 8       # ts_mean窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    ts_cov_window = window_sizes['cov_window']
    ts_regbeta_window = window_sizes['regbeta_window']
    ts_mean_window = window_sizes['mean_window']
    
    # 按symbol分组，确保时间排序
    df = data_df.copy()
    
    # 1. 计算volume和amount的滚动协方差
    def calc_cov(x, y, window):
        # 确保输入数据没有inf或nan，或者在计算后处理
        cov_result = x.rolling(window=window, min_periods=w).cov(y)
        # 协方差计算结果可能为nan或inf，这里不特殊处理，留到最后处理因子值
        return cov_result
    
    # 按symbol分组计算
    df['ts_cov'] = df.groupby('symbol')['volume'].transform(
        lambda x: calc_cov(x, df.loc[x.index, 'amount'], ts_cov_window)
    )
    
    # 2. 计算amount对high的回归beta值
    def calc_regbeta(x, y, window):
        # 确保输入数据没有inf或nan，或者在计算后处理
        cov = x.rolling(window=window, min_periods=w).cov(y)
        var = x.rolling(window=window, min_periods=w).var()
        # 防止除以0，添加一个小的常数
        beta = cov / (var + 1e-8)
        # 回归beta计算结果可能为nan或inf，这里不特殊处理，留到最后处理因子值
        return beta
    
    df['ts_regbeta'] = df.groupby('symbol')['amount'].transform(
        lambda x: calc_regbeta(x, df.loc[x.index, 'high'], ts_regbeta_window)
    )
    
    # 3. 计算ts_regbeta的滚动均值
    df['ts_mean'] = df.groupby('symbol')['ts_regbeta'].transform(
        lambda x: x.rolling(window=ts_mean_window, min_periods=w).mean()
    )
    
    # 4. 计算最终因子值 (ts_cov / ts_mean)
    # 防止除以0，添加一个小的常数
    df['factor'] = df['ts_cov'] / (df['ts_mean'] + 1e-8)
    
    # 5. 替换无穷值为NaN
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))
    
    # 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 返回结果
    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

