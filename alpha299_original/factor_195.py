# Alpha299因子 - factor_195
# 原始因子编号: 195
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_195(data_df, w=8, uni_col=None):
    """
    计算Alpha101因子
    
    参数:
        data_df: 输入数据DataFrame
        w: 核心可调参数（天数）
        uni_col: 未使用（因子涉及多列）
    
    返回:
        包含因子值的DataFrame
    """
    
    # 窗口配置
    window_configs = {
        'n1': 10.0,  # w // 2 = 20 // 2 = 10
        'n2': 20.0,  # w = 20
        'n3': 15.0,  # 固定值（非w的函数）
        'n4': 8.0    # 固定值（非w的函数）
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    
    df = data_df.copy()
    
    # 确保必要列存在
    required_cols = ['open', 'high', 'low', 'close', 'volume', 'amount']
    if not all(col in df.columns for col in required_cols):
        raise ValueError(f"数据缺少必要列: {required_cols}")
    
    # 计算vwap（成交量加权平均价）
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
    
    # 按symbol分组处理
    def process_group(group):
        # T1: open_price * volume
        group['T1'] = group['open'] * group['volume']
        
        # T2: arctan(low)
        # 确保输入arctan的值大于0，虽然arctan定义域是R，但为了和log等函数处理方式一致，且low通常大于0
        group['T2'] = np.arctan(group['low'] + 1e-8)
        
        # T3: gp_max(high, volume)
        group['T3'] = group[['high', 'volume']].max(axis=1)
        
        # T4: T3 + vwap
        group['T4'] = group['T3'] + group['vwap']
        
        # T5: ts_cov(n1, T2, T4)（使用实际窗口大小）
        def rolling_cov(x, y, window):
            # 确保输入没有inf/nan
            x_clean = x.replace([np.inf, -np.inf], np.nan)
            y_clean = y.replace([np.inf, -np.inf], np.nan)
            
            cov_result = x_clean.rolling(window=window, min_periods=w).cov(y_clean)
            # 填充cov计算结果中的nan/inf为0 (cov为0表示无线性关系)
            return cov_result.fillna(0).replace([np.inf, -np.inf], 0)
        
        group['T5'] = rolling_cov(group['T2'], group['T4'], n1)
        
        # T6: ts_regbeta(T1, T5, n2)（使用实际窗口大小）
        def rolling_beta(x, y, window):
            # 确保输入没有inf/nan
            x_clean = x.replace([np.inf, -np.inf], np.nan)
            y_clean = y.replace([np.inf, -np.inf], np.nan)
            
            cov = x_clean.rolling(window=window, min_periods=w).cov(y_clean)
            var = x_clean.rolling(window=window, min_periods=w).var()
            
            # 避免除以0，并处理cov或var计算结果中的nan/inf
            beta_result = cov.fillna(0).replace([np.inf, -np.inf], 0) / (var.fillna(0).replace([np.inf, -np.inf], 0) + 1e-8)
            
            # 填充beta计算结果中的nan/inf为0 (beta为0表示无线性关系)
            return beta_result.fillna(0).replace([np.inf, -np.inf], 0)
        
        group['T6'] = rolling_beta(group['T1'], group['T5'], n2)
        
        # X1: delay(T6, n3)（使用实际窗口大小）
        group['X1'] = group['T6'].shift(n3)
        
        # X2: ts_pctchg(close, n4)（使用实际窗口大小）
        # pct_change可能产生inf，例如前一天close为0
        group['X2'] = group['close'].pct_change(periods=n4).replace([np.inf, -np.inf], np.nan)
        
        # Alpha101: gp_min(X1, X2)
        group['factor'] = group[['X1', 'X2']].min(axis=1)
        
        # 处理无穷大值
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)
        
        return group
    
    df = df.groupby('symbol', group_keys=False).apply(process_group)
    
    # 保留必要列并恢复日期时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

