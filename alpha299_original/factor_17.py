# Alpha299因子 - factor_17
# 原始因子编号: 17
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_17(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha38因子：基于CLOSE排名和CLOSE/OPEN比值排名的乘积因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'ts_rank_window': 10    # CLOSE的ts_rank窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    ts_rank_window = window_sizes['ts_rank_window']

    df = data_df.copy()

    # 确保数据按symbol和时间排序
    df = df.sort_values(['symbol', 'time'])

    # 计算CLOSE的ts_rank
    def compute_ts_rank(s, window):
        # 将inf替换为nan
        s = np.where(np.isinf(s), np.nan, s)
        # 移除nan值
        s_cleaned = s[~np.isnan(s)]
        if len(s_cleaned) < window:
            return np.nan
        return stats.rankdata(s_cleaned, method='average')[-1] / (window + 1e-8)

    df['ts_rank_close'] = df.groupby('symbol')['close'].transform(lambda x: x.rolling(window=ts_rank_window, min_periods=w).apply(
        lambda y: compute_ts_rank(y, ts_rank_window), raw=True
    ))

    # 计算取负后的ts_rank排名
    df['neg_ts_rank'] = -df['ts_rank_close']
    # 对负ts_rank进行横截面排名
    df['rank_neg_ts_rank'] = df.groupby('time')['neg_ts_rank'].rank(pct=True)

    # 计算CLOSE/OPEN比值的排名
    # 避免除以0
    df['close_open_ratio'] = df['close'] / (df['open'] + 1e-8)
    # 对close/open比值进行横截面排名
    df['rank_close_open'] = df.groupby('time')['close_open_ratio'].rank(pct=True)

    # 计算最终因子值
    df['factor'] = df['rank_neg_ts_rank'] * df['rank_close_open']

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择并返回结果
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

