# Alpha299因子 - factor_233
# 原始因子编号: 233
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_233(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha155因子
    参数:
        data_df (DataFrame): 输入数据，包含必要的列
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为16天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str): 本因子不依赖单一基础列，设为None
    返回:
        DataFrame: 包含因子结果的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'mean_window': 16,  # amount的滚动均值窗口
        'cov_window': 10    # 协方差计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    mean_window = window_sizes['mean_window']
    cov_window = window_sizes['cov_window']
    
    df = data_df.copy()

    # 确保数据按时间正确排序
    df = df.sort_values(['symbol', 'time'])

    # 步骤1: 计算vwap
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
    T1 = -df['vwap']  # neg(vwap)

    # 步骤2: 计算amount的mean_window周期滚动均值
    T2 = df.groupby('symbol')['amount'].transform(
        lambda x: x.rolling(window=mean_window, min_periods=w).mean()
    )

    # 步骤3: X1 = T1 / T2
    X1 = T1 / (T2 + 1e-8)

    # 步骤4: 计算close和vwap的逐元素最大值
    df['T3'] = df[['close', 'vwap']].max(axis=1)

    # 步骤5: 计算T3和volume的cov_window周期滚动协方差
    # 使用apply处理滚动协方差，并处理可能出现的NaN/Inf
    T4 = df.groupby('symbol').apply(
        lambda group: group['T3'].rolling(window=cov_window, min_periods=w).cov(group['volume'])
    ).reset_index(level=0, drop=True)
    df['T4'] = T4.values
    # 协方差结果可能为NaN，这里保留NaN，后续排名会处理

    # 步骤6: 计算截面排名（按时间点）
    df['X2'] = df.groupby('time')['T4'].transform(
        lambda x: x.rank(method='average', na_option='keep')
    )

    # 步骤7: 最终因子计算
    df['factor'] = X1 / (df['X2'] + 1e-8)

    # 步骤8: 替换无效值
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 格式化日期时间列
    df['trade_date'] = df['trade_date'].astype('string')
    df['time'] = df['time'].astype('string')

    # 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

