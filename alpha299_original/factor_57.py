# Alpha299因子 - factor_57
# 原始因子编号: 57
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_57(data_df, w: int | None = 16, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 16        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    """
    计算Alpha154因子：VWAP条件比较因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest', 'industry_name']
        w (int | None): 核心可调参数，16期滚动窗口的基准值，默认16
        uni_col (str | None): 单一基础列参数，此处设为None，因为使用多列计算

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 检查必要列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'high', 'low', 'close', 'volume']
    missing_cols = [col for col in required_columns if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    # 参数推导
    n_min = w
    ma_volume_window = int(11.25 * w)  # 180 = 11.25 * 16
    corr_window = int(1.125 * w)       # 18 = 1.125 * 16

    # 创建副本以避免修改原始数据
    df = data_df.copy()

    # 1. 计算VWAP
    df = df.sort_values(['symbol', 'time'])
    # 确保volume.cumsum()不会出现0，避免除以0
    df['vwap'] = df.groupby('symbol').apply(
        lambda x: ((x['high'] + x['low'] + x['close']) / 3 * x['volume']).cumsum() /
        (x['volume'].cumsum() + 1e-8)
    ).reset_index(level=0, drop=True)

    # 2. 计算VWAP的n_min期滚动最小值
    df['vwap_min'] = df.groupby('symbol')['vwap'].rolling(
        window=n_min, min_periods=w
    ).min().reset_index(level=0, drop=True)

    # 3. 计算VWAP与成交量MA180的18期滚动相关系数
    # 先计算成交量的MA180
    df['volume_ma'] = df.groupby('symbol')['volume'].rolling(
        window=ma_volume_window, min_periods=w
    ).mean().reset_index(level=0, drop=True)

    # 使用更直接的滚动相关系数计算方法
    def calc_corr(group):
        # 确保输入到corr的数据没有inf/nan，并且处理常数序列的情况
        vwap_series = group['vwap'].replace([np.inf, -np.inf], np.nan)
        volume_ma_series = group['volume_ma'].replace([np.inf, -np.inf], np.nan)

        # 计算相关系数
        corr_series = vwap_series.rolling(window=corr_window, min_periods=w).corr(volume_ma_series)

        # 将相关系数结果中的NaN和inf替换为0
        corr_series = corr_series.replace([np.inf, -np.inf], 0).fillna(0)
        return corr_series

    df['corr_vwap_vol'] = df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)

    # 4. 计算DiffVWAP
    df['diff_vwap'] = df['vwap'] - df['vwap_min']

    # 5. 比较条件并生成因子值
    df['factor'] = np.where(df['diff_vwap'] < df['corr_vwap_vol'], 1, 0)

    # 6. 移除VWAP为NaN的位置 (这一步在计算vwap时已经通过dropna处理了，但保留以防万一)
    df = df.dropna(subset=['vwap'])

    # 7. 替换±∞为NaN (这一步在corr计算中已经处理，但保留以防万一)
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 8. 保留必要列并调整格式
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    # 严格按照要求恢复日期和时间格式为字符串
    output_df['trade_date'] = pd.to_datetime(output_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    output_df['time'] = pd.to_datetime(output_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 最终输出前移除包含NaN的行，确保因子值是有效的
    return output_df.dropna()

