# Alpha299因子 - factor_133
# 原始因子编号: 133
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_133(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha因子：成交量变化排名与收益率排名的滚动相关性因子
    
    参数:
        data_df (pd.DataFrame): 输入数据
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列参数（本因子不适用，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'correlation_window': 6  # 滚动相关系数窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    correlation_window = window_sizes['correlation_window']

    df = data_df.copy()

    # 按symbol、trade_date和time排序以确保时间序列顺序
    df.sort_values(['symbol', 'trade_date', 'time'], inplace=True)

    # 1. 计算成交量的自然对数
    # 保护：对volume加一个小的正数，避免log(0)或log(负数)
    df['log_volume'] = np.log(df['volume'] + 1e-8)

    # 2. 计算对数成交量的一期差分
    df['diff_log_volume'] = df.groupby('symbol')['log_volume'].diff(1)

    # 3. 对差分值进行横截面百分比排名
    df['R_V'] = df.groupby(['trade_date', 'time'])['diff_log_volume'].transform(lambda x: x.rank(pct=True))

    # 4. 计算当日开盘到收盘的收益率
    # 保护：对open加一个小的正数，避免除以0
    df['ret_o2c'] = (df['close'] - df['open']) / (df['open'] + 1e-8)

    # 5. 对收益率进行横截面百分比排名
    df['R_R'] = df.groupby(['trade_date', 'time'])['ret_o2c'].transform(lambda x: x.rank(pct=True))

    # 6. 计算R_V和R_R的滚动相关系数
    def rolling_corr(group):
        # 保护：在计算相关系数前，先处理可能存在的inf/nan，这里选择保留nan，corr函数会处理
        # 如果rolling().corr()结果是NaN (例如窗口内常数)，则填充为0
        corr_result = group['R_V'].rolling(window=correlation_window, min_periods=w).corr(group['R_R'])
        # 保护：将corr结果中的inf/-inf替换为nan，再将nan替换为0
        corr_result = corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)
        return corr_result

    df['Corr'] = df.groupby('symbol').apply(rolling_corr).reset_index(level=0, drop=True)

    # 7. 取负数得到最终因子值
    df['factor'] = -df['Corr']

    # 8. 替换±∞为NaN (尽管rolling_corr中已经处理，这里再加一层保险)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复trade_date和time的字符串格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame并删除NaN
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

