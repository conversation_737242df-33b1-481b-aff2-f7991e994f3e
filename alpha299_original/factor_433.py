# Alpha299因子 - factor_433
# 原始因子编号: 433
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_433(data_df, w: int | None = 14, uni_col: str | None = 'low'):
    """
    计算阿隆下降指标(Aroon Down)

    参数:
    data_df: 输入的DataFrame，包含价格数据
    w: 时间窗口长度，默认为14。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    uni_col: 用于计算的价格列，对于Aroon Down应使用'low'

    返回:
    包含阿隆下降指标的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'aroon_window': 14
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    aroon_window = window_sizes['aroon_window']

    # 创建数据副本
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组计算阿隆下降指标
    result_dfs = []
    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')

        # 计算阿隆下降指标
        # 对于每个窗口，找到最低价的位置
        def find_lowest_position(window):
            """找到窗口内最低价的位置"""
            # 过滤掉窗口中的NaN和inf值
            valid_window = window.replace([np.inf, -np.inf], np.nan).dropna()

            if len(valid_window) == 0:
                return float('nan')
            # 如果有多个相同的最低价，取最近的一个（索引最大的）
            min_value = valid_window.min()
            # 反向查找最后一个最小值的位置
            # 使用原始窗口的索引来确定位置
            for i in range(len(window)-1, -1, -1):
                # 检查原始窗口中的值是否等于最小值，并且不是NaN或inf
                if not pd.isna(window.iloc[i]) and window.iloc[i] not in [np.inf, -np.inf] and window.iloc[i] == min_value:
                    return float(i)
            return float('nan')

        # 使用滚动窗口计算
        # 在进行滚动计算前，对输入列进行inf/nan处理，避免apply函数内部处理复杂
        group[uni_col] = group[uni_col].replace([np.inf, -np.inf], np.nan)
        rolling_windows = group[uni_col].rolling(window=aroon_window, min_periods=w)
        # 对每个窗口应用函数，找到最低价位置
        lowest_positions = rolling_windows.apply(find_lowest_position, raw=False)

        # 计算自最低价出现以来经过的周期数
        periods_since_low = aroon_window - 1 - lowest_positions

        # 计算阿隆下降指标
        # 避免除以0，虽然aroon_window通常大于0，但为了鲁棒性添加
        group['factor'] = ((aroon_window - periods_since_low) / (aroon_window + 1e-8)) * 100

        result_dfs.append(group)

    # 合并结果
    result_df = pd.concat(result_dfs)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 只保留需要的列
    # 保留dropna()以去除计算过程中产生的NaN
    result_df = result_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

