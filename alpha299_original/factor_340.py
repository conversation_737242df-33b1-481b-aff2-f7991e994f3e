# Alpha299因子 - factor_340
# 原始因子编号: 340
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_340(data_df, w: int | None = 3, uni_col: str | None = None):
    """
    计算Alpha140因子

    参数:
    data_df: 输入数据
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 单一列参数，本因子不使用单一列，设为None

    返回:
    包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 8,   # delay(open_price, n1)
        'n2': 8,   # ts_pctchg(T3, n2)
        'n3': 5,   # ts_pctchg(T5, n3)
        'n4': 3,   # delay(T6, n4)
        'n5': 8    # ts_regres窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']

    # 检查必要列是否存在
    required_cols = ['open', 'close', 'time', 'trade_date', 'symbol', 'high', 'low', 'amount', 'volume']
    if not all(col in data_df.columns for col in required_cols):
        raise ValueError(f"输入数据缺少必要列: {required_cols}")

    # 复制数据以避免修改原始数据
    data_df = data_df.copy()

    # 计算 vwap (使用 amount / volume)
    # 保护：分母加一个小数，避免除以0
    data_df['vwap'] = data_df['amount'] / (data_df['volume'] + 1e-10)

    # 处理日期和时间格式
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 按symbol和时间排序
    data_df = data_df.sort_values(by=['symbol', 'time'])

    # 步骤1: T1 = delay(open_price, n1)
    data_df['T1'] = data_df.groupby('symbol')['open'].shift(n1)

    # 步骤2: T2 = rank(vwap)
    # 按时间排序，然后每个时间点内对vwap进行rank
    data_df = data_df.sort_values(by=['time', 'symbol'])
    data_df['T2'] = data_df.groupby('time')['vwap'].transform(lambda x: x.rank(method='min', ascending=True))

    # 步骤3: T3 = arctan(T2)
    # 保护：arctan定义域为R，无需特殊处理0，但为保持一致性，可以对输入加一个小数
    data_df['T3'] = np.arctan(data_df['T2'] + 1e-8)

    # 步骤4: T4 = ts_pctchg(T3, n2)
    data_df = data_df.sort_values(by=['symbol', 'time'])  # 重新按symbol和时间排序
    # 保护：pct_change可能产生inf，nan，这里先计算，后面再处理
    data_df['T4'] = data_df.groupby('symbol')['T3'].pct_change(periods=n2)
    # 保护：pct_change产生的inf，nan替换为0
    data_df['T4'] = data_df['T4'].replace([np.inf, -np.inf], np.nan).fillna(0)


    # 步骤5: T5 = gp_min(T1, T4)
    data_df['T5'] = data_df[['T1', 'T4']].min(axis=1)

    # 步骤6: T6 = ts_pctchg(T5, n3)
    # 保护：pct_change可能产生inf, nan，这里先计算，后面再处理
    data_df['T6'] = data_df.groupby('symbol')['T5'].pct_change(periods=n3)
    # 保护：pct_change产生的inf，nan替换为0
    data_df['T6'] = data_df['T6'].replace([np.inf, -np.inf], np.nan).fillna(0)

    # 步骤7: T7 = delay(T6, n4)
    data_df['T7'] = data_df.groupby('symbol')['T6'].shift(n4)

    # 步骤8: X1 = arctan(T7)
    # 保护：arctan定义域为R，无需特殊处理0，但为保持一致性，可以对输入加一个小数
    data_df['X1'] = np.arctan(data_df['T7'] + 1e-8)

    # 步骤9: X2 = neg(close)
    data_df['X2'] = -data_df['close']

    # 步骤10: Alpha140 = ts_regres(X1, X2, n5)
    def calculate_residuals(group):
        X = group['X1'].values
        Y = group['X2'].values
        residuals = []
        for i in range(len(group)):
            if i < n5 - 1:
                residuals.append(np.nan)
            else:
                X_window = X[max(0, i - n5 + 1):i + 1]
                Y_window = Y[max(0, i - n5 + 1):i + 1]
                Y_mean = np.nanmean(Y_window)

                # 保护：检查窗口内是否有足够的有效数据，并且X窗口内的值不能全部相同（否则std=0）
                if len(X_window) < n5 or np.isnan(X_window).any() or np.isnan(Y_window).any() or np.std(X_window) < 1e-8:
                    residuals.append(Y_mean)  # 如果窗口内数据不足或X窗口内的值全部相同，返回Y的均值
                else:
                    try:
                        X_window_const = sm.add_constant(X_window)
                        model = sm.OLS(Y_window, X_window_const)
                        results = model.fit()
                        residual = results.resid[-1]  # 取最后一步的残差
                        residuals.append(residual)
                    except:
                        # 保护：回归失败时返回NaN
                        residuals.append(Y_mean)

        return pd.Series(residuals, index=group.index)

    data_df['Alpha140'] = data_df.groupby('symbol').apply(calculate_residuals).reset_index(level=0, drop=True)

    # 步骤11: 替换inf为NaN
    data_df['Alpha140'] = data_df['Alpha140'].replace([np.inf, -np.inf], np.nan)

    # 选择输出列并处理NaN
    result_df = data_df[['trade_date', 'time', 'symbol', 'Alpha140']].copy()
    # 保留真实的NaN情况，不在这里dropna
    # result_df = result_df.dropna()
    result_df.rename(columns={'Alpha140': 'factor'}, inplace=True)

    # 恢复日期和时间格式
    result_df['trade_date'] = pd.to_datetime(result_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = pd.to_datetime(result_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df

