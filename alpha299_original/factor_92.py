# Alpha299因子 - factor_92
# 原始因子编号: 92
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_92(data_df, w: int | None = 6, uni_col: str | None = None):
    # 三段式混合模型窗口配置
    window_configs = {
        'base_window': 20,      # 基础窗口 w
        'n_mavol': 60,          # 3*w = 60
        'n_corr1': 9,           # 0.45*w = 9
        'n_corr2': 6,           # 0.3*w = 6
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        base_window = window_sizes.loc[w, 'base_window']
        n_mavol = window_sizes.loc[w, 'n_mavol']
        n_corr1 = window_sizes.loc[w, 'n_corr1']
        n_corr2 = window_sizes.loc[w, 'n_corr2']
    else:
        base_window = window_configs['base_window']
        n_mavol = window_configs['n_mavol']
        n_corr1 = window_configs['n_corr1']
        n_corr2 = window_configs['n_corr2']
    
    df = data_df.copy()

    # 参数推导（现在使用动态计算的值）
    # n_mavol = 3 * w  # 60 = 3*20
    # n_corr1 = int(0.45 * w)  # 9 = 0.45*20
    # n_corr2 = int(0.3 * w)  # 6 = 0.3*20

    # 计算中间价
    df['mid'] = (df['high'] + df['low']) / (2 + 1e-8)

    # 计算SumMid_20
    df['sum_mid_20'] = df.groupby('symbol')['mid'].transform(
        lambda x: x.rolling(window=base_window, min_periods=w).sum()
    )

    # 计算MAVol_60
    df['mavol_60'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=n_mavol, min_periods=w).mean()
    )

    # 计算SumMAVol_20
    df['sum_mavol_20'] = df.groupby('symbol')['mavol_60'].transform(
        lambda x: x.rolling(window=base_window, min_periods=w).sum()
    )

    # 计算Corr_1 (9期滚动相关系数)
    def calc_corr(group):
        # 对可能导致std=0的情况进行处理，或者对结果进行后处理
        corr_result = group['sum_mid_20'].rolling(window=n_corr1, min_periods=w).corr(group['sum_mavol_20'])
        return corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    df['corr1'] = df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)

    # 计算Corr_2 (6期滚动相关系数)
    def calc_corr2(group):
        # 对可能导致std=0的情况进行处理，或者对结果进行后处理
        corr_result = group['low'].rolling(window=n_corr2, min_periods=w).corr(group['volume'])
        return corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    df['corr2'] = df.groupby('symbol').apply(calc_corr2).reset_index(level=0, drop=True)

    # 横截面百分比排序
    df = df.sort_values(['time', 'symbol'])  # 确保时间排序正确
    df['r1'] = df.groupby('time')['corr1'].transform(lambda x: x.rank(pct=True))
    df['r2'] = df.groupby('time')['corr2'].transform(lambda x: x.rank(pct=True))

    # 生成因子值
    df['factor'] = np.where(df['r1'] < df['r2'], -1, 0)

    # 处理无穷大值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去重
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

