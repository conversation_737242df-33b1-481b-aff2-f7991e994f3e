# Alpha299因子 - factor_255
# 原始因子编号: 255
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_255(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha191因子。
    参数:
        data_df: 输入的DataFrame，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest', 'industry_name']
        w: 核心窗口参数，用于控制TS_REGRES和PCT_CHG窗口的联动（默认6，为最小窗口值）
        uni_col: 本因子不适用，设为None
    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 三段式混合模型窗口配置
    window_configs = {
        'w1': 16,       # TS_REGRES的窗口期
        'w2': 6,        # PCT_CHG的窗口期
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        w1 = window_sizes.loc[w, 'w1']
        w2 = window_sizes.loc[w, 'w2']
    else:
        w1 = window_configs['w1']
        w2 = window_configs['w2']
    
    # 确保时间排序
    df = data_df.sort_values(by=['symbol', 'trade_date', 'time']).copy()

    # 1. 计算TS_REGRES(OPEN, CLOSE, w1)
    # 1.1 计算OPEN的方差
    df['var_open'] = df.groupby('symbol')['open'].transform(
        lambda x: x.rolling(window=w1, min_periods=w).var()
    )

    # 1.2 计算OPEN和CLOSE的协方差
    df['cov_open_close'] = df.groupby('symbol').apply(
        lambda group: group['open'].rolling(window=w1, min_periods=w).cov(group['close'])
    ).reset_index(level=0, drop=True)

    # 1.3 计算beta系数
    # 避免除以0
    df['beta'] = df['cov_open_close'] / (df['var_open'] + 1e-8)

    # 1.4 计算回归残差
    df['ts_regres'] = df['close'] - df['beta'] * df['open']

    # 2. 计算ABS(HIGH)
    df['abs_high'] = df['high'].abs()

    # 3. 计算ABS(HIGH) - TS_REGRES
    df['term1'] = df['abs_high'] - df['ts_regres']

    # 4. 计算LOW + HIGH
    df['low_high_sum'] = df['low'] + df['high']

    # 5. 计算中间结果
    df['intermediate'] = df['low_high_sum'] - df['term1']

    # 6. 计算w2周期百分比变化
    # pct_change可能产生inf或nan，需要处理
    df['pct_chg'] = df.groupby('symbol')['intermediate'].transform(
        lambda x: x.pct_change(periods=w2)
    )

    # 7. 处理无效值
    df['pct_chg'] = df['pct_chg'].replace([np.inf, -np.inf], np.nan)

    # 8. 格式化日期和时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'pct_chg']].rename(columns={'pct_chg': 'factor'})
    # 保持原始的nan值，不进行fillna(0)
    # output_df = output_df.dropna() # 移除dropna()以保留原始的nan值

    return output_df

