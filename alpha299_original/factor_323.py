# Alpha299因子 - factor_323
# 原始因子编号: 323
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_323(data_df, w: int | None = 9, uni_col: str | None = None):
    """
    计算Alpha101因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 指定单一基础列，此处设为None
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window_zscore': 20,      # Z-score标准化窗口
        'window_regbeta1': 15,    # 第一个回归beta窗口
        'window_regbeta2': 9      # 第二个回归beta窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window_zscore = window_sizes['window_zscore']
    window_regbeta1 = window_sizes['window_regbeta1']
    window_regbeta2 = window_sizes['window_regbeta2']

    df = data_df.copy()

    # 检查必要列（移除vwap检查，因为我们将用amount/volume计算它）
    required_columns = ['amount', 'low', 'open', 'volume', 'trade_date', 'time', 'symbol']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"缺失必要列: {col}")

    # 计算vwap（成交量加权平均价）
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 计算滚动Z-score
    def rolling_zscore(group, col, window):
        # 避免对常数序列计算std
        std_val = group[col].rolling(window=window, min_periods=w).std()
        mean_val = group[col].rolling(window=window, min_periods=w).mean()
        zscore = (group[col] - mean_val) / (std_val + 1e-8)
        # 处理inf和nan
        zscore = zscore.replace([np.inf, -np.inf], np.nan)
        return zscore

    # 标准化成交额和最低价
    df['amount_z'] = df.groupby('symbol').apply(rolling_zscore, 'amount', window_zscore).reset_index(level=0, drop=True)
    df['low_z'] = df.groupby('symbol').apply(rolling_zscore, 'low', window_zscore).reset_index(level=0, drop=True)

    # 应用tanh变换
    df['tanh_amount_z'] = np.tanh(df['amount_z'])
    df['tanh_low_z'] = np.tanh(df['low_z'])

    # 计算T1 = tanh_amount_z + tanh_low_z
    df['T1'] = df['tanh_amount_z'] + df['tanh_low_z']

    # 标准化开盘价
    df['open_price_z'] = df.groupby('symbol').apply(rolling_zscore, 'open', window_zscore).reset_index(level=0, drop=True)

    # 计算滚动回归贝塔系数
    def rolling_beta(group, x_col, y_col, window):
        x = group[x_col]
        y = group[y_col]
        betas = []
        for i in range(len(x)):
            if i < window - 1:
                betas.append(np.nan)
            else:
                x_window = x[i - window + 1:i + 1]
                y_window = y[i - window + 1:i + 1]

                # 检查窗口内是否有nan或inf
                if x_window.isnull().any() or y_window.isnull().any() or \
                   np.isinf(x_window).any() or np.isinf(y_window).any():
                    betas.append(np.nan)
                # 检查x_window是否为常数序列
                elif x_window.var() == 0:
                    betas.append(np.nan)
                else:
                    # 确保输入linregress的数据是有限的
                    valid_indices = np.isfinite(x_window) & np.isfinite(y_window)
                    if np.sum(valid_indices) < 2: # 需要至少两个有效点进行回归
                         betas.append(np.nan)
                    else:
                        try:
                            slope, _, _, _, _ = linregress(x_window[valid_indices], y_window[valid_indices])
                            betas.append(slope)
                        except ValueError: # 处理linregress可能抛出的异常
                            betas.append(np.nan)

        return pd.Series(betas, index=x.index)

    # 计算X1 = ts_regbeta(T1, open_price_z, 15)
    df['X1'] = df.groupby('symbol').apply(rolling_beta, 'T1', 'open_price_z', window_regbeta1).reset_index(level=0, drop=True)

    # 计算X2 = ts_regbeta(volume, vwap, 9)
    df['X2'] = df.groupby('symbol').apply(rolling_beta, 'volume', 'vwap', window_regbeta2).reset_index(level=0, drop=True)

    # gp_min(X1, X2)
    df['factor'] = df[['X1', 'X2']].min(axis=1)

    # 处理无穷大值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

