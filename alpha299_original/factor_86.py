# Alpha299因子 - factor_86
# 原始因子编号: 86
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_86(data_df, w: int | None = 6, uni_col: str | None = None,
              alpha: float = 1.0, w_max: float = 300.0, lambda_rate: float = 0.1):
    # 定义所有窗口的基准值
    window_configs = {
        'roll_window': 6        # 滚动窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        nonlocal w_max, lambda_rate, alpha
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    roll_window = window_sizes['roll_window']       # 滚动窗口

    """
    计算Alpha 11因子：日内价格位置与成交量加权因子，使用动态窗口系统

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest', 'industry_name']
        w (int | None): 滚动窗口期，默认6天
        uni_col (str | None): 本因子不依赖单一基础列，因此设为None
        alpha: 动态窗口的非线性调整参数（默认1.0）
        w_max: 动态窗口的绝对上限（默认300.0）
        lambda_rate: 动态窗口的增长率（默认0.1）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    df = data_df.copy()

    # 按symbol分组处理
    def process_group(group):
        # 计算价格位置指标
        high_low = group['high'] - group['low']
        # 处理High-Low=0的情况（避免除以零）
        # 确保分母不为0，并处理可能出现的inf/nan
        pos = ((group['close'] - group['low']) - (group['high'] - group['close'])) / (high_low + 1e-8)
        # 替换inf为NaN
        pos = pos.replace([np.inf, -np.inf], np.nan)

        # 计算成交量加权指标
        x_t = pos * group['volume']

        # 滚动求和（使用动态窗口）
        # rolling().sum()会自动处理NaN，结果为NaN
        alpha_11 = x_t.rolling(window=roll_window, min_periods=w).sum()

        group['factor'] = alpha_11
        return group

    df = df.groupby('symbol', group_keys=False).apply(process_group)

    # 保留必要列并恢复日期/时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 移除包含NaN的行
    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

