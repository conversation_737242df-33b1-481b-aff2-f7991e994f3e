# Alpha299因子 - factor_236
# 原始因子编号: 236
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_236(data_df, w: int | None = None, uni_col: str | None = None):
    df = data_df.copy()

    # 计算I1: ts_max(low, 7)
    df['I1'] = df.groupby('symbol')['low'].transform(lambda x: x.rolling(window=7, min_periods=w).max())

    # 计算I2: ts_mean(amount, 20)
    df['I2'] = df.groupby('symbol')['amount'].transform(lambda x: x.rolling(window=20, min_periods=w).mean())

    # 计算I3: ts_cov(10, I1, I2)
    def calc_cov(group):
        # 在计算协方差前，对可能导致问题的列进行预处理，例如将inf/-inf替换为nan
        group['I1'] = group['I1'].replace([np.inf, -np.inf], np.nan)
        group['I2'] = group['I2'].replace([np.inf, -np.inf], np.nan)
        # 计算协方差，并对结果中的inf/-inf/nan进行处理，这里选择填充0
        cov_result = group['I1'].rolling(window=10, min_periods=w).cov(group['I2'])
        return cov_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    df['I3'] = df.groupby('symbol').apply(calc_cov).reset_index(level=0, drop=True)

    # 计算Alpha164: ts_max(I3, 16)
    # 在计算滚动最大值前，对I3中的inf/-inf进行处理
    df['I3'] = df['I3'].replace([np.inf, -np.inf], np.nan)
    df['factor'] = df.groupby('symbol')['I3'].transform(lambda x: x.rolling(window=16, min_periods=w).max())

    # 替换无穷值为NaN (这一步在计算I3和factor前已经做了，但保留一次作为最终检查)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 输出所需的列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

