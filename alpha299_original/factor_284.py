# Alpha299因子 - factor_284
# 原始因子编号: 284
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_284(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算优化后的Alpha37因子，解决局部基准单位问题。

    参数:
    - data_df: 输入DataFrame，包含['symbol', 'trade_date', 'time', 'amount', 'close']等列
    - w: 可调参数，单位为天，作为核心窗口参数 (默认6)
    - uni_col: 本因子不依赖单一基础列，故设置为None
    """
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 6,         # ts_cov窗口
        'zscore_window': 20      # ts_zscore窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    cov_window = window_sizes['cov_window']           # ts_cov窗口
    zscore_window = window_sizes['zscore_window']     # ts_zscore窗口

    df = data_df.copy()

    # 1. 计算滚动协方差 ts_cov(cov_window, amount, close)
    # 按symbol分组，计算amount和close的滚动协方差
    df['cov'] = df.groupby('symbol').apply(
        lambda g: g['amount'].rolling(cov_window, min_periods=w).cov(g['close'])
    ).reset_index(level=0, drop=True)

    # 2. 滚动Z-score标准化 ts_zscore(zscore_window, cov)
    # 按symbol分组，计算滚动均值和标准差，进行标准化
    df['zscore'] = df.groupby('symbol')['cov'].transform(
        lambda x: (x - x.rolling(zscore_window, min_periods=w).mean()) /
                  (x.rolling(zscore_window, min_periods=w).std() + 1e-8)
    )

    # 3. 截面排名 rank(zscore)
    # 按时间点分组，对zscore进行截面排名
    df['factor'] = df.sort_values(['trade_date', 'time']).groupby(['trade_date', 'time'])['zscore'].rank()

    # 4. 处理无效值：替换inf/-inf为NaN
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], float('nan'))

    # 5. 日期和时间格式处理
    df['trade_date'] = df['trade_date'].astype('string')
    df['time'] = df['time'].astype('string')

    # 6. 输出结果：保留必要列并去除无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

