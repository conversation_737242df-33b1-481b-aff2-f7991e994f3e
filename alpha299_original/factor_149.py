# Alpha299因子 - factor_149
# 原始因子编号: 149
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_149(data_df, w: int | None = 6, uni_col: str | None = 'close'):
    """
    计算Alpha43: OBV变种累积因子

    参数:
        data_df: 输入DataFrame，包含symbol, trade_date, time, close, volume等列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 用于计算的单一基础数据列 (默认'close')

    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'rolling_window': 6  # 滚动求和窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    rolling_window = window_sizes['rolling_window']

    df = data_df.copy()

    # 按symbol分组处理
    def process_symbol(group):
        # 计算符号成交量
        group['prev_close'] = group['close'].shift(1)

        # 确保close和prev_close不是NaN，避免后续计算SignedVol产生NaN
        group['close_safe'] = group['close'].fillna(method='ffill').fillna(method='bfill')
        group['prev_close_safe'] = group['prev_close'].fillna(method='ffill').fillna(method='bfill')
        group['volume_safe'] = group['volume'].fillna(0) # 成交量为NaN时视为0

        group['SignedVol'] = np.where(
            group['close_safe'] > group['prev_close_safe'],
            group['volume_safe'],
            np.where(
                group['close_safe'] < group['prev_close_safe'],
                -group['volume_safe'],
                0
            )
        )

        # 滚动求和（min_periods=w表示只要有数据就计算）
        # 滚动求和本身对NaN有处理能力，但为了健壮性，确保输入SignedVol尽量没有NaN
        group['factor'] = group['SignedVol'].rolling(window=rolling_window, min_periods=w).sum()

        # 移除原始close为NaN的位置的因子值
        # 这一步保留，因为我们希望因子值对应有效的close数据
        group.loc[group['close'].isna(), 'factor'] = np.nan

        # 替换inf/-inf为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        # 清理临时列
        group = group.drop(columns=['close_safe', 'prev_close_safe', 'volume_safe'])

        return group

    df = df.groupby('symbol', group_keys=False).apply(process_symbol)

    # 严格处理日期时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去重
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].drop_duplicates()

    return result_df

