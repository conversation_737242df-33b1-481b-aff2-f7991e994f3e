# Alpha299因子 - factor_214
# 原始因子编号: 214
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_214(
    data_df,
    w: int | None = 9,
    uni_col: str | None = None
):
    """
    计算Alpha131因子
    参数:
        data_df: 输入DataFrame，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest', 'industry_name']
        w: 核心窗口参数（单位为天），默认9
        uni_col: 单一基础列参数（本因子不适用，故设为None）
    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 窗口配置
    window_configs = {
        'n1': 9.0,   # w，第一个窗口参数
        'n2': 16.0   # int((16/9)*w) = int((16/9)*9) = 16，第二个窗口参数
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    
    df = data_df.copy()

    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'open', 'low', 'close', 'volume']
    if not all(col in df.columns for col in required_cols):
        raise ValueError(f"输入数据缺少必要列: {required_cols}")

    # 计算T1: ts_regbeta(volume, open_price, n1)（使用实际窗口大小）
    def _ts_regbeta(group, X_col, Y_col, window):
        """计算滚动回归贝塔系数"""
        X = group[X_col]
        Y = group[Y_col]
        # 增加对X和Y的inf/nan处理，并对X的方差加一个小的常数避免除以0
        X_cleaned = X.replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill')
        Y_cleaned = Y.replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill')

        cov = X_cleaned.rolling(window=window, min_periods=w).cov(Y_cleaned)
        var = X_cleaned.rolling(window=window, min_periods=w).var()
        # 对var加一个小的常数，避免除以0
        beta = cov / (var + 1e-8)
        # 对计算出的beta进行inf/nan处理，替换为0
        beta = beta.replace([np.inf, -np.inf], np.nan).fillna(0)
        return beta

    # 第一部分计算
    df['T1'] = df.groupby('symbol').apply(
        lambda x: _ts_regbeta(x, 'volume', 'open', n1)
    ).reset_index(level=0, drop=True)

    # T1_std: ts_zscore(T1, n1)（使用实际窗口大小）
    def _ts_zscore(group, col, window):
        """计算滚动Z-score"""
        # 增加对输入列的inf/nan处理
        cleaned_col = group[col].replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill')
        rolling_mean = cleaned_col.rolling(window=window, min_periods=w).mean()
        rolling_std = cleaned_col.rolling(window=window, min_periods=w).std()
        # 对rolling_std加一个小的常数，避免除以0
        z_score = (cleaned_col - rolling_mean) / (rolling_std + 1e-8)
        # 对计算出的z_score进行inf/nan处理，替换为0
        z_score = z_score.replace([np.inf, -np.inf], np.nan).fillna(0)
        return z_score

    df['T1_std'] = df.groupby('symbol').apply(
        lambda x: _ts_zscore(x, 'T1', n1)
    ).reset_index(level=0, drop=True)

    # X1: rank(T1_std)
    def _rank(group, col):
        """截面排名"""
        # 增加对输入列的inf/nan处理
        cleaned_col = group[col].replace([np.inf, -np.inf], np.nan)
        return cleaned_col.rank(axis=0)

    # 使用apply并处理返回的Series，确保索引对齐
    rank_result = df.groupby(['trade_date', 'time']).apply(
        lambda x: _rank(x.drop(columns=['trade_date', 'time']), 'T1_std')
    )
    
    # 确保结果能正确展开为Series
    if isinstance(rank_result, pd.DataFrame):
        # 如果结果是DataFrame，需要将其展开为Series
        df['X1'] = rank_result.stack().reset_index(level=[0,1,2], drop=True)
    else:
        # 如果结果是Series，直接使用
        df['X1'] = rank_result.reset_index(level=[0,1], drop=True)

    # 第二部分计算（使用实际窗口大小）
    df['T2'] = df.groupby('symbol').apply(
        lambda x: _ts_regbeta(x, 'low', 'close', n2)
    ).reset_index(level=0, drop=True)

    # X2: neg(T2)
    df['X2'] = -df['T2']

    # 最终因子计算
    df['factor'] = df['X1'] + df['X2']

    # 处理无穷大值和NaN值
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))
    # 保留真实的NaN值，不进行fillna(0)

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    # Check if we have any results before processing
    if not output_df.empty:
        # 格式化日期和时间
        output_df['trade_date'] = pd.to_datetime(output_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
        output_df['time'] = pd.to_datetime(output_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return output_df

