# Alpha299因子 - factor_332
# 原始因子编号: 332
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_332(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha117因子

    参数:
    data_df: 输入数据
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为18天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 单一列参数，本因子不使用单一列，设为None

    返回:
    包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'w1': 18,  # ts_rank(sub_vwap_open, 18)
        'w2': 9,   # ts_rank(ts_rank_18, 9)
        'w3': 8    # ts_regres(ts_rank_9, arctan_sqrt_vwap, 8)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    w1 = window_sizes['w1']
    w2 = window_sizes['w2']
    w3 = window_sizes['w3']

    df = data_df.copy()

    # 确保日期列的正确格式
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 计算vwap
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 1. 计算vwap与open_price的差值
    df['sub_vwap_open'] = df['vwap'] - df['open']

    # 按symbol分组处理
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        group = group.sort_values('time')

        # 2. 计算ts_rank(sub_vwap_open, 18)
        ts_rank_18 = []
        for i in range(len(group)):
            if i < w1 - 1:
                # 使用可用数据，min_periods=w
                window = group['sub_vwap_open'].iloc[:i+1].values
                if len(window) > 0:
                    # 降序排名并归一化
                    # 确保分母不为0
                    denominator = window.shape[0]
                    if denominator == 0:
                        ts_rank_18.append(np.nan)
                    else:
                        rank = (window.shape[0] - stats.rankdata(window, method='ordinal', nan_policy='omit') + 1) / (denominator + 1e-8)
                        ts_rank_18.append(rank[-1])
                else:
                    ts_rank_18.append(np.nan)
            else:
                window = group['sub_vwap_open'].iloc[i-w1+1:i+1].values
                # 降序排名并归一化
                # 确保分母不为0
                denominator = window.shape[0]
                if denominator == 0:
                    ts_rank_18.append(np.nan)
                else:
                    rank = (window.shape[0] - stats.rankdata(window, method='ordinal', nan_policy='omit') + 1) / (denominator + 1e-8)
                    ts_rank_18.append(rank[-1])

        group['ts_rank_18'] = ts_rank_18

        # 3. 计算ts_rank(ts_rank_18, 9)
        ts_rank_9 = []
        for i in range(len(group)):
            if i < w2 - 1:
                # 使用可用数据，min_periods=w
                window = group['ts_rank_18'].iloc[:i+1].values
                if len(window) > 0:
                    # 降序排名并归一化
                    # 确保分母不为0
                    denominator = window.shape[0]
                    if denominator == 0:
                        ts_rank_9.append(np.nan)
                    else:
                        rank = (window.shape[0] - stats.rankdata(window, method='ordinal', nan_policy='omit') + 1) / (denominator + 1e-8)
                        ts_rank_9.append(rank[-1])
                else:
                    ts_rank_9.append(np.nan)
            else:
                window = group['ts_rank_18'].iloc[i-w2+1:i+1].values
                # 降序排名并归一化
                # 确保分母不为0
                denominator = window.shape[0]
                if denominator == 0:
                    ts_rank_9.append(np.nan)
                else:
                    rank = (window.shape[0] - stats.rankdata(window, method='ordinal', nan_policy='omit') + 1) / (denominator + 1e-8)
                    ts_rank_9.append(rank[-1])

        group['ts_rank_9'] = ts_rank_9

        # 4. 计算sqrt(vwap)
        # 对vwap取绝对值再开方，避免负数开方
        group['sqrt_vwap'] = np.sqrt(np.abs(group['vwap']))

        # 5. 计算arctan(sqrt_vwap)
        # arctan定义域为全体实数，无需特殊处理
        group['arctan_sqrt_vwap'] = np.arctan(group['sqrt_vwap'])

        # 6. 计算ts_regres(ts_rank_9, arctan_sqrt_vwap, 8)
        factor_values = []
        for i in range(len(group)):
            if i < w3 - 1:
                # 使用可用数据，min_periods=w
                x_window = group['ts_rank_9'].iloc[:i+1].values
                y_window = group['arctan_sqrt_vwap'].iloc[:i+1].values

                # 过滤掉无效值
                valid_indices = ~(np.isnan(x_window) | np.isnan(y_window))
                x_valid = x_window[valid_indices]
                y_valid = y_window[valid_indices]

                # 确保有足够的数据点进行回归且x_valid不是常数
                if len(x_valid) > 1 and not np.all(x_valid == x_valid[0]):
                    # 计算回归
                    try:
                        slope, intercept, _, _, _ = stats.linregress(x_valid, y_valid)
                        # 计算当前点的残差
                        current_x = group['ts_rank_9'].iloc[i]
                        current_y = group['arctan_sqrt_vwap'].iloc[i]

                        if not np.isnan(current_x) and not np.isnan(current_y):
                            # 残差计算: Y - β*X (不包含截距)
                            residual = current_y - (slope * current_x)
                            factor_values.append(residual)
                        else:
                            factor_values.append(np.nan)
                    except ValueError:
                        # 回归计算可能出现问题，例如数据点共线等
                        factor_values.append(np.nan)
                else:
                    factor_values.append(np.nan)
            else:
                x_window = group['ts_rank_9'].iloc[i-w3+1:i+1].values
                y_window = group['arctan_sqrt_vwap'].iloc[i-w3+1:i+1].values

                # 过滤掉无效值
                valid_indices = ~(np.isnan(x_window) | np.isnan(y_window))
                x_valid = x_window[valid_indices]
                y_valid = y_window[valid_indices]

                # 确保有足够的数据点进行回归且x_valid不是常数
                if len(x_valid) > 1 and not np.all(x_valid == x_valid[0]):
                    # 计算回归
                    try:
                        slope, intercept, _, _, _ = stats.linregress(x_valid, y_valid)
                        # 计算当前点的残差
                        current_x = group['ts_rank_9'].iloc[i]
                        current_y = group['arctan_sqrt_vwap'].iloc[i]

                        if not np.isnan(current_x) and not np.isnan(current_y):
                            # 残差计算: Y - β*X (不包含截距)
                            residual = current_y - (slope * current_x)
                            factor_values.append(residual)
                        else:
                            factor_values.append(np.nan)
                    except ValueError:
                         # 回归计算可能出现问题
                        factor_values.append(np.nan)
                else:
                    factor_values.append(np.nan)

        group['factor'] = factor_values

        # 7. 将无穷大值替换为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        # 只保留需要的列
        result_dfs.append(group[['trade_date', 'time', 'symbol', 'factor']])

    # 合并结果
    result_df = pd.concat(result_dfs, ignore_index=True)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df.dropna()

