# Alpha299因子 - factor_428
# 原始因子编号: 428
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_428(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算终极振荡指标 (Ultimate Oscillator, ULTOSC)

    参数:
    data_df: 输入数据DataFrame
    w: 短周期参数，默认为7。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    uni_col: 单一列参数，本因子不使用，设为None

    返回:
    包含trade_date, time, symbol, factor列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'short_period': 7,
        'medium_period': 14,
        'long_period': 28
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    p1 = window_sizes['short_period']
    p2 = window_sizes['medium_period']
    p3 = window_sizes['long_period']

    # 复制输入数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组进行计算
    result_dfs = []
    for symbol, group in df.groupby('symbol'):
        # 确保按时间排序
        group = group.sort_values('time')

        # 计算真实低点 (True Low)
        group['prev_close'] = group['close'].shift(1)
        group['true_low'] = group.apply(lambda x: min(x['low'], x['prev_close']) if not pd.isna(x['prev_close']) else x['low'], axis=1)

        # 计算购买压力 (Buying Pressure)
        group['buying_pressure'] = group['close'] - group['true_low']

        # 计算真实波幅 (True Range)
        group['true_range'] = group.apply(
            lambda x: max(
                x['high'] - x['low'],
                abs(x['high'] - x['prev_close']) if not pd.isna(x['prev_close']) else 0,
                abs(x['low'] - x['prev_close']) if not pd.isna(x['prev_close']) else 0
            ) if not pd.isna(x['prev_close']) else (x['high'] - x['low']),
            axis=1
        )

        # 计算各周期的BP和TR总和
        group['sum_bp1'] = group['buying_pressure'].rolling(window=p1, min_periods=w).sum()
        group['sum_tr1'] = group['true_range'].rolling(window=p1, min_periods=w).sum()

        group['sum_bp2'] = group['buying_pressure'].rolling(window=p2, min_periods=w).sum()
        group['sum_tr2'] = group['true_range'].rolling(window=p2, min_periods=w).sum()

        group['sum_bp3'] = group['buying_pressure'].rolling(window=p3, min_periods=w).sum()
        group['sum_tr3'] = group['true_range'].rolling(window=p3, min_periods=w).sum()

        # 计算各周期的比率
        # 处理除以零的情况
        group['ratio1'] = group['sum_bp1'] / (group['sum_tr1'] + 1e-8)
        group['ratio2'] = group['sum_bp2'] / (group['sum_tr2'] + 1e-8)
        group['ratio3'] = group['sum_bp3'] / (group['sum_tr3'] + 1e-8)

        # 计算终极振荡指标 (ULTOSC)
        group['factor'] = 100 * ((4 * group['ratio1'] + 2 * group['ratio2'] + group['ratio3']) / 7)

        # 只保留需要的列
        result_df = group[['trade_date', 'time', 'symbol', 'factor']]
        result_dfs.append(result_df)

    # 合并所有品种的结果
    if result_dfs:
        final_result = pd.concat(result_dfs)
    else:
        final_result = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    # 恢复日期格式为字符串
    final_result['trade_date'] = final_result['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    final_result['time'] = final_result['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 删除含有NaN的行
    final_result = final_result.dropna()

    return final_result

