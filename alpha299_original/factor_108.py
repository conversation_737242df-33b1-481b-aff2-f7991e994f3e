# Alpha299因子 - factor_108
# 原始因子编号: 108
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_108(data_df, w: int | None = None, uni_col: str | None = None):
    df = data_df.copy()

    # 1. 计算60日成交量移动平均
    df['MA60_volume'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=60, min_periods=w).mean()
    )

    # 2. 计算MA60_volume的9日滚动求和
    df['SumMAVol_t'] = df.groupby('symbol')['MA60_volume'].transform(
        lambda x: x.rolling(window=9, min_periods=w).sum()
    )

    # 3. 计算开盘价与SumMAVol_t的6期滚动相关系数
    # 处理滚动相关系数可能出现的NaN或inf
    df['Corr_1'] = df.groupby('symbol').apply(
        lambda group: group['open'].rolling(window=6, min_periods=w).corr(group['SumMAVol_t'])
    ).reset_index(level=0, drop=True)
    df['Corr_1'] = df['Corr_1'].replace([np.inf, -np.inf], np.nan).fillna(0)


    # 4. 按时间点排序，准备横截面排名
    df = df.sort_values(by=['trade_date', 'time'])

    # 5. 对Corr_1进行横截面百分比排名
    df['R_1'] = df.groupby(['trade_date', 'time'])['Corr_1'].rank(pct=True)

    # 6. 计算开盘价的14期滚动最小值
    df['MinO_14'] = df.groupby('symbol')['open'].transform(
        lambda x: x.rolling(window=14, min_periods=w).min()
    )

    # 7. 计算Open - MinO_14，并进行横截面排名
    df['diff'] = df['open'] - df['MinO_14']
    df['R_2'] = df.groupby(['trade_date', 'time'])['diff'].rank(pct=True)

    # 8. 生成因子值：-I(R_1 < R_2)
    df['factor'] = np.where(df['R_1'] < df['R_2'], -1, 0)

    # 9. 处理无效值：替换±inf为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 10. 删除无效值行
    df = df.dropna(subset=['factor'])

    # 11. 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 12. 选择输出列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

