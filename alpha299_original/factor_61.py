# Alpha299因子 - factor_61
# 原始因子编号: 61
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_61(data_df, w: int | None = 5, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 5        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    """
    计算Alpha 16因子：成交量与VWAP相关性的负向最大值因子

    参数:
        data_df: 输入的DataFrame，包含必要的字段
        w: 核心时间窗口参数（单位：天），默认5
        uni_col: 单一基础列参数，此处设为None因为涉及两个基础列
    """
    df = data_df.copy()

    # 确保必要列存在（volume已存在，VWAP_t需要计算）
    required_columns = ['volume', 'high', 'low', 'close']
    missing_cols = [col for col in required_columns if col not in df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    # 计算VWAP_t（成交量加权平均价）
    # 按symbol分组并按时间排序
    df.sort_values(['symbol', 'trade_date', 'time'], inplace=True)

    # 计算典型价格（假设使用收盘价）
    df['typical_price'] = df['close']

    # 计算累计成交金额和累计成交量
    df['cumulative_turnover'] = df.groupby('symbol')['typical_price'].transform(lambda x: (x * df['volume']).cumsum())
    df['cumulative_volume'] = df.groupby('symbol')['volume'].cumsum()

    # 计算VWAP
    df['VWAP_t'] = df['cumulative_turnover'] / (df['cumulative_volume'] + 1e-8)

    # 1. 时间序列滚动标准化 (Z-score)
    def ts_zscore(x, window):
        # 增加对std为0的处理
        std_dev = x.rolling(window=window, min_periods=w).std()
        mean_val = x.rolling(window=window, min_periods=w).mean()
        # 避免除以零，std为0时，zscore为0
        return (x - mean_val) / (std_dev + 1e-8)

    df['Z_V'] = df.groupby('symbol')['volume'].transform(lambda x: ts_zscore(x, w))
    df['Z_VWAP'] = df.groupby('symbol')['VWAP_t'].transform(lambda x: ts_zscore(x, w))

    # 2. 横截面百分比排序
    # 按时间排序以确保时间序列正确
    df.sort_values(['trade_date', 'time'], inplace=True)

    # 横截面排序函数
    def rank_cs(group):
        # rank函数默认处理NaN，此处无需额外处理
        return group.rank(pct=True)

    # 对每个时间点进行横截面排序
    df['R_V'] = df.groupby(['trade_date', 'time'])['Z_V'].transform(rank_cs)
    df['R_VWAP'] = df.groupby(['trade_date', 'time'])['Z_VWAP'].transform(rank_cs)

    # 3. 计算滚动相关系数
    # 按symbol分组并按时间排序
    df.sort_values(['symbol', 'trade_date', 'time'], inplace=True)

    # 滚动相关系数计算
    def rolling_corr(a, b, window):
        # pandas rolling().corr() 会自动处理NaN，但常数序列相关性为NaN
        # 且结果可能出现inf/-inf，需要后处理
        corr_result = a.rolling(window=window, min_periods=w).corr(b)
        # 将NaN和inf/-inf替换为0
        corr_result = corr_result.replace([float('inf'), float('-inf')], 0).fillna(0)
        return corr_result

    df['Corr_RV_RWAP'] = df.groupby('symbol').apply(
        lambda group: rolling_corr(group['R_V'], group['R_VWAP'], w)
    ).reset_index(level=0, drop=True)

    # 4. 过滤inf值并进行横截面排序
    # 在rolling_corr中已经处理了inf，此处只需处理NaN
    df['RankCorr'] = df.groupby(['trade_date', 'time'])['Corr_RV_RWAP'].transform(
        lambda x: x.rank(pct=True)
    )

    # 5. 计算滚动最大值
    df['MaxRankCorr'] = df.groupby('symbol')['RankCorr'].transform(
        lambda x: x.rolling(window=w, min_periods=w).max()
    )

    # 6. 最终因子取负
    df['factor'] = -df['MaxRankCorr']

    # 7. 处理无效值（±inf替换为NaN）
    # 在rolling_corr中已经处理了inf，此处保留NaN
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], float('nan'))

    # 8. 日期和时间格式处理
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 9. 选择输出列并去除无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

