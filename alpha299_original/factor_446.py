# Alpha299因子 - factor_446
# 原始因子编号: 446
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_446(data_df, w: int | None = 30, uni_col: str | None = None):
    """
    计算皮尔逊相关系数因子

    参数:
    - data_df: 输入数据DataFrame
    - w: 计算窗口期长度，默认为30。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    - uni_col: 单一列参数，本因子不使用单一列计算，设为None

    返回:
    - 包含因子值的DataFrame，列为['trade_date', 'time', 'symbol', 'factor']
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}
    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 30
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    corr_window = window_sizes['corr_window']

    # 复制输入数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期和时间列的格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组，然后计算每个品种的皮尔逊相关系数
    # 本例中，我们计算close与volume之间的相关性
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 按时间排序
        group = group.sort_values('time')

        # 初始化结果列
        group['factor'] = np.nan

        # 计算滚动窗口的皮尔逊相关系数
        # 初始化累积值
        n = corr_window  # 窗口大小

        if len(group) < n:
            continue  # 跳过数据量不足的品种

        # 初始化第一个窗口的累积值
        # 过滤掉NaN和Inf值
        valid_group_initial = group.iloc[:n].replace([np.inf, -np.inf], np.nan).dropna(subset=['close', 'volume'])
        current_n = len(valid_group_initial)

        if current_n < 2: # 需要至少两个点才能计算相关系数
             group.iloc[n-1, group.columns.get_loc('factor')] = 0.0
        else:
            sumX = valid_group_initial['close'].sum()
            sumY = valid_group_initial['volume'].sum()
            sumX2 = (valid_group_initial['close'] ** 2).sum()
            sumY2 = (valid_group_initial['volume'] ** 2).sum()
            sumXY = (valid_group_initial['close'] * valid_group_initial['volume']).sum()

            # 计算第一个窗口的相关系数
            num_val = sumXY - (sumX * sumY) / (current_n + 1e-8)
            den_x_val = sumX2 - (sumX * sumX) / (current_n + 1e-8)
            den_y_val = sumY2 - (sumY * sumY) / (current_n + 1e-8)
            den_sqrt_val = den_x_val * den_y_val

            if den_sqrt_val > 1e-8: # 避免分母接近0
                factor_value = num_val / (np.sqrt(den_sqrt_val) + 1e-8)
                # 确保结果在[-1, 1]范围内
                group.iloc[n-1, group.columns.get_loc('factor')] = np.clip(factor_value, -1.0, 1.0)
            else:
                group.iloc[n-1, group.columns.get_loc('factor')] = 0.0


        # 滚动计算后续窗口的相关系数
        # 使用rolling方法处理NaN和Inf，并计算相关系数
        # 使用min_periods=w确保至少有两个有效数据点
        rolling_corr = group[['close', 'volume']].rolling(window=corr_window, min_periods=w).corr().unstack().iloc[:, 1]

        # 填充NaN和Inf为0
        rolling_corr = rolling_corr.replace([np.inf, -np.inf], np.nan).fillna(0.0)

        # 将计算出的滚动相关系数赋值给factor列
        group['factor'] = rolling_corr

        result_dfs.append(group)

    # 合并结果
    if result_dfs:
        result_df = pd.concat(result_dfs)
        
        # 恢复日期和时间的字符串格式
        result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
        result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
        
        # 只保留需要的列并删除NaN值
        result_df = result_df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    else:
        # 如果没有足够的数据，返回空的DataFrame
        result_df = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    return result_df

