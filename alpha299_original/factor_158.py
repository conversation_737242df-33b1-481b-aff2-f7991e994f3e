# Alpha299因子 - factor_158
# 原始因子编号: 158
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_158(data_df, w=20, uni_col=None):
    """
    计算Alpha 55因子：复杂条件价格变动累积因子

    参数:
        data_df: 输入数据DataFrame，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', ...]
        w: 滚动窗口大小（默认20，与因子信息一致）
        uni_col: 本因子不依赖单一基础列，故设为None

    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 窗口配置
        window_configs = {
            'w': 20.0  # 基础窗口参数
        }
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    actual_w = window_sizes['w']

    # 按symbol分组并按时间排序
    df = data_df.copy()
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 计算滞后值
    df['close_lag1'] = df.groupby('symbol')['close'].shift(1)
    df['open_lag1'] = df.groupby('symbol')['open'].shift(1)
    df['low_lag1'] = df.groupby('symbol')['low'].shift(1)

    # 计算分子部分
    df['price_move'] = (
        (df['close'] - df['close_lag1']) +
        (df['close'] - df['open']) / (2 + 1e-8) +
        (df['close_lag1'] - df['open_lag1'])
    )

    df['vol_amp'] = np.maximum(
        np.abs(df['high'] - df['close_lag1']),
        np.abs(df['low'] - df['close_lag1'])
    )

    df['Nt'] = 16 * df['price_move'] * df['vol_amp']

    # 计算分母部分
    df['TermA'] = np.abs(df['high'] - df['close_lag1'])
    df['TermB'] = np.abs(df['low'] - df['close_lag1'])
    df['TermC'] = np.abs(df['high'] - df['low_lag1'])
    df['TermD'] = np.abs(df['close_lag1'] - df['open_lag1'])

    # 条件判断
    condition1 = (df['TermA'] > df['TermB']) & (df['TermA'] > df['TermC'])
    condition2 = (df['TermB'] > df['TermC']) & (df['TermB'] > df['TermA'])

    df['Dt'] = np.select(
        [
            condition1,
            condition2
        ],
        [
            df['TermA'] + df['TermB']/(2 + 1e-8) + df['TermD']/(4 + 1e-8),
            df['TermB'] + df['TermA']/(2 + 1e-8) + df['TermD']/(4 + 1e-8)
        ],
        default=df['TermC'] + df['TermD']/(4 + 1e-8)
    )

    # 计算Xt并处理除零
    df['Xt'] = df['Nt'] / (df['Dt'] + 1e-8)
    df['Xt'] = df['Xt'].replace([np.inf, -np.inf], np.nan)

    # 滚动求和（使用实际窗口大小，注意min_periods=w以避免NaN）
    df['factor'] = df.groupby('symbol')['Xt'].transform(
        lambda x: x.rolling(window=actual_w, min_periods=w).sum()
    )

    # 处理无效值
    df = df.dropna(subset=['factor'])

    # 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']]

