# Alpha299因子 - factor_54
# 原始因子编号: 54
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_54(data_df, w: int | None = 9, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'ma_window': 15,          # 移动平均窗口 (对应n_ma)
        'zscore_window': 20,     # Z-score标准化窗口 (对应n_zscore)  
        'corr_window': 9         # 相关系数计算窗口 (对应n_corr)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    ma_window = window_sizes['ma_window']           # 移动平均窗口
    zscore_window = window_sizes['zscore_window']   # Z-score标准化窗口  
    corr_window = window_sizes['corr_window']       # 相关系数计算窗口

    # 参数推导
    n_ma = ma_window
    n_zscore = zscore_window
    n_corr = corr_window

    df = data_df.copy()

    # 确保数据按trade_date和time排序
    df = df.sort_values(by=['trade_date', 'time'])

    # 步骤1: 计算RH_t (high的横截面排序)
    df['RH_t'] = df.groupby(['trade_date', 'time'])['high'].transform(lambda x: x.rank(pct=True))

    # 步骤2: 计算MA15(Volume)
    # 确保volume非负
    df['volume_protected'] = df['volume'].apply(lambda x: max(x, 0))
    df['MAV_15'] = df.groupby('symbol')['volume_protected'].transform(lambda x: x.rolling(window=n_ma, min_periods=w).mean())

    # 步骤3: 计算Z-score标准化
    df['ZMAV_15'] = df.groupby('symbol')['MAV_15'].transform(
        lambda x: x.rolling(window=n_zscore, min_periods=w).apply(
            lambda s: (s.iloc[-1] - s.mean()) / (s.std() + 1e-8), raw=False
        )
    )
    # 处理ZMAV_15可能产生的inf/nan
    df['ZMAV_15'] = df['ZMAV_15'].replace([np.inf, -np.inf], np.nan)


    # 步骤4: 计算RMAV_15 (Z-score后的横截面排序)
    df['RMAV_15'] = df.groupby(['trade_date', 'time'])['ZMAV_15'].transform(lambda x: x.rank(pct=True))

    # 步骤5: 计算Corr_t (RH_t与RMAV_15的滚动相关系数)
    def calculate_corr(group):
        # 确保输入corr的数据没有inf/nan，或者在corr后处理
        corr_result = group['RH_t'].rolling(window=n_corr, min_periods=w).corr(group['RMAV_15'])
        # 将corr结果中的inf/nan填充为0，因为常数序列相关系数为0
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    df['Corr_t'] = df.groupby('symbol').apply(calculate_corr).droplevel('symbol')

    # 步骤6: 对Corr_t进行横截面排序，取负
    df['rank_Corr'] = df.groupby(['trade_date', 'time'])['Corr_t'].transform(lambda x: x.rank(pct=True))
    df['factor'] = -df['rank_Corr']

    # 步骤7: 处理无效值 (虽然前面已经处理了一些，这里再做一次最终检查)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并dropna
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

