# Alpha299因子 - factor_90
# 原始因子编号: 90
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_90(data_df, w: int | None = 20, uni_col: str | None = None):
    """
    计算Alpha 118因子：上下影线比率因子
    参数:
        data_df: 输入数据DataFrame，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', ...]
        w: 滚动窗口期数（默认20）
        uni_col: 本因子不依赖单一基础列，故设为None
    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 三段式混合模型窗口配置
    window_configs = {
        'rolling_window': 20,   # 滚动窗口
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        rolling_window = window_sizes.loc[w, 'rolling_window']
    else:
        rolling_window = window_configs['rolling_window']

    df = data_df.copy()

    # 1. 按symbol和trade_date排序确保时间序列连续
    df = df.sort_values(by=['symbol', 'trade_date'])

    # 2. 计算每日上下影线长度
    df['up_shadow'] = df['high'] - df['open']
    df['low_shadow'] = df['open'] - df['low']

    # 3. 计算20期滚动求和
    df['sum_up'] = df.groupby('symbol')['up_shadow'].transform(
        lambda x: x.rolling(window=rolling_window, min_periods=w).sum()
    )
    df['sum_low'] = df.groupby('symbol')['low_shadow'].transform(
        lambda x: x.rolling(window=rolling_window, min_periods=w).sum()
    )

    # 4. 计算比率并处理除以0的情况
    # 避免除以0，对分母加一个很小的数
    df['factor'] = (df['sum_up'] / (df['sum_low'] + 1e-8)) * 100
    # 处理可能出现的inf和-inf
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 5. 日期时间格式转换
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 6. 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

