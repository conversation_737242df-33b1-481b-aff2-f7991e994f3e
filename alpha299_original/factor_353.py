# Alpha299因子 - factor_353
# 原始因子编号: 353
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_353(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha_189因子

    Alpha_189 = ts_max(ts_regbeta(vwap, amount, 6), 12)

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，本因子不使用

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'regbeta_window': 6,      # ts_regbeta窗口
        'max_window': 12          # ts_max窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    regbeta_window = window_sizes['regbeta_window']
    max_window = window_sizes['max_window']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 检查必要的列是否存在
    required_columns = ['amount']
    if not all(col in df.columns for col in required_columns):
        raise ValueError(f"数据中缺少必要的列: {[col for col in required_columns if col not in df.columns]}")

    # 计算vwap（成交量加权平均价）
    # 如果数据中没有vwap列，则需要计算
    if 'vwap' not in df.columns:
        if 'volume' in df.columns:
            df['vwap'] = df['amount'] / (df['volume'] + 1e-8) # 避免除以0
        else:
            raise ValueError("数据中缺少计算vwap所需的volume列")

    # 转换日期格式以便于分组计算
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 定义滚动回归beta的计算函数
    def rolling_regbeta(x, y, window):
        """计算y对x的滚动回归beta值"""
        # 计算滚动协方差
        rolling_cov = (x * y).rolling(window=window, min_periods=w).mean() - \
                      x.rolling(window=window, min_periods=w).mean() * \
                      y.rolling(window=window, min_periods=w).mean()

        # 计算x的滚动方差
        rolling_var = x.rolling(window=window, min_periods=w).var(ddof=0)

        # 计算beta
        # 避免除以0，并处理可能产生的inf/nan
        beta = rolling_cov / (rolling_var + 1e-8)
        return beta

    # 按symbol分组计算
    result_dfs = []
    for symbol, group in df.groupby('symbol'):
        # 按时间排序
        group = group.sort_values('time')

        # 计算ts_regbeta
        group['regbeta'] = rolling_regbeta(group['vwap'], group['amount'], regbeta_window)
        # 处理regbeta可能产生的inf/nan，替换为0
        group['regbeta'] = group['regbeta'].replace([np.inf, -np.inf], np.nan).fillna(0)


        # 计算ts_max
        group['factor'] = group['regbeta'].rolling(window=max_window, min_periods=w).max()

        # 将无穷值替换为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        result_dfs.append(group)

    # 合并结果
    result_df = pd.concat(result_dfs)

    # 恢复日期和时间格式
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 只保留需要的列
    result_df = result_df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

