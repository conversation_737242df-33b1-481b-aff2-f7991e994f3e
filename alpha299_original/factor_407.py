# Alpha299因子 - factor_407
# 原始因子编号: 407
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_407(data_df, w: int | None = None, uni_col: str | None = 'close'):
    """
    计算正弦函数因子 (Sine Function, SIN)
    
    参数:
    data_df: 输入的DataFrame，包含交易数据
    w: 窗口参数，对于正弦函数因子不需要窗口参数，设为None
    uni_col: 用于计算的数据列，默认为'close'
    
    返回:
    包含trade_date, time, symbol和factor列的DataFrame
    """
    # 创建数据副本
    df = data_df.copy()
    
    # 检查输入列是否存在
    if uni_col not in df.columns:
        raise ValueError(f"输入列 {uni_col} 不存在于数据中")
    
    # 计算正弦函数因子
    # 对输入列的每个值应用正弦函数
    # 正弦函数定义域为实数，无需特殊处理inf, nan，np.sin会自动处理nan为nan
    df['factor'] = np.sin(df[uni_col])
    
    # 格式化日期和时间列
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d').dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S').dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 选择所需的列并返回
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]
    
    return result_df

