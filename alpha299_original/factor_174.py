# Alpha299因子 - factor_174
# 原始因子编号: 174
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_174(data_df, w=3, uni_col=None):
    """
    计算Alpha 77因子：价格与VWAP偏离及价格与成交量相关性的衰减排名最小化因子
    """
    # 窗口配置
    window_configs = {
        'linear_decay_20': 20.0,    # w，线性衰减窗口
        'zscore_20': 20.0,          # w，滚动标准化窗口
        'ma_40': 40.0,              # 2*w，移动平均窗口
        'corr_3': 3.0,              # 固定窗口，相关系数
        'linear_decay_6': 6.0       # 固定窗口，线性衰减
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    linear_decay_20 = window_sizes['linear_decay_20']
    zscore_20 = window_sizes['zscore_20']
    ma_40 = window_sizes['ma_40']
    corr_3 = window_sizes['corr_3']
    linear_decay_6 = window_sizes['linear_decay_6']
    
    df = data_df.copy()

    # 检查必要列是否存在
    required_columns = ['high', 'low', 'close', 'volume', 'amount']
    for col in required_columns:
        if col not in df.columns:
            raise KeyError(f"输入数据缺少必要列: {col}")

    # 1. 计算中间价 Mid_t
    df['Mid_t'] = (df['high'] + df['low']) / (2 + 1e-8)

    # 2. 计算VWAP（基于累计金额/累计成交量）
    df['vwap'] = df.groupby(['symbol', 'trade_date'])['amount'].cumsum() / (df.groupby(['symbol', 'trade_date'])['volume'].cumsum() + 1e-8)

    # 3. 计算Term1_t = Mid_t - VWAP
    df['Term1_t'] = df['Mid_t'] - df['vwap']

    # 4. 线性衰减加权 DecayLinear(Term1_t, 使用实际窗口大小)
    def linear_decay(series, window):
        """自定义线性衰减加权函数，权重 w_k=2k/(window*(window+1))"""
        weights = np.arange(1, window+1) * 2 / (window * (window + 1) + 1e-8)
        # 确保序列长度足够进行卷积
        if len(series) < window:
            return pd.Series([np.nan] * len(series), index=series.index)
        # 使用convolve进行线性衰减加权，并处理边界
        decayed_series = np.convolve(series.fillna(0), weights[::-1], mode='full')[:len(series)]
        # 对于窗口不足的部分，结果应为NaN
        decayed_series[:window-1] = np.nan
        return pd.Series(decayed_series, index=series.index)

    df['DL_1'] = df.groupby('symbol')['Term1_t'].transform(
        lambda x: linear_decay(x, window=linear_decay_20)  # 使用实际窗口大小
    )

    # 5. 滚动标准化 ts_zscore(DL_1, 使用实际窗口大小)
    def rolling_zscore(series, window):
        rolling_mean = series.rolling(window=window, min_periods=w).mean()
        rolling_std = series.rolling(window=window, min_periods=w).std(ddof=0)
        # 处理标准差为0的情况
        zscore = (series - rolling_mean) / (rolling_std + 1e-8)
        return zscore

    df['Z_DL_1'] = df.groupby('symbol')['DL_1'].transform(
        lambda x: rolling_zscore(x, window=zscore_20)  # 使用实际窗口大小
    )

    # 6. 横截面排序 R_1 = rank_cs(Z_DL_1)
    def cross_sectional_rank(group):
        return group.rank(pct=True)

    df['R_1'] = df.groupby('time')['Z_DL_1'].transform(cross_sectional_rank)

    # 7. 计算MA_40(Volume)（使用实际窗口大小）
    df['MA_40_Volume'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=ma_40, min_periods=w).mean()
    )

    # 8. 计算滚动相关系数 Corr_2（使用实际窗口大小）
    def safe_rolling_corr(x, y, window):
        # 使用pandas内置的rolling.corr，它会自动处理NaN
        corr_series = x.rolling(window=window, min_periods=w).corr(y)
        # 额外处理corr结果中的inf和nan，将其替换为0
        return corr_series.replace([np.inf, -np.inf], np.nan).fillna(0)

    df['Corr_2'] = df.groupby('symbol').apply(
        lambda group: safe_rolling_corr(group['Mid_t'], group['MA_40_Volume'], window=corr_3)
    ).droplevel(0)

    # 9. 线性衰减加权 DecayLinear(Corr_2, 使用实际窗口大小)
    df['DL_2'] = df.groupby('symbol')['Corr_2'].transform(
        lambda x: linear_decay(x, window=linear_decay_6)  # 使用实际窗口大小
    )

    # 10. 横截面排序 R_2 = rank_cs(DL_2)
    df['R_2'] = df.groupby('time')['DL_2'].transform(cross_sectional_rank)

    # 11. 最终因子值 alpha_77 = min(R_1, R_2)
    df['factor'] = df[['R_1', 'R_2']].min(axis=1)

    # 12. 处理无效值（±∞转为NaN）
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 13. 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 14. 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

