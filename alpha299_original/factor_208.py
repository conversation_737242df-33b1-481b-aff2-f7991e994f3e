# Alpha299因子 - factor_208
# 原始因子编号: 208
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_208(data_df, w=8, uni_col=None):
    """
    计算Alpha123因子

    参数:
        data_df: 输入的DataFrame，包含交易数据
        w: 核心时间窗口参数（单位：天）
        uni_col: 单一基础数据列（本因子不适用，故设为None）

    返回:
        包含因子值的DataFrame
    """
    # 窗口配置
    window_configs = {
        'n1': 16.0,   # w，ts_std的窗口
        'n2': 8.0     # int(w/16*8) = int(16/16*8) = 8，ts_regbeta的窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']

    # 检查必要列
    required_columns = ['volume', 'amount', 'close']
    if not all(col in data_df.columns for col in required_columns):
        raise ValueError(f"输入数据缺少必要列: {required_columns}")

    # 1. 计算volume的滚动标准差（使用实际窗口大小）
    # 确保volume非负且非nan，避免std计算异常
    data_df['volume_protected'] = data_df['volume'].apply(lambda x: max(0, x) if pd.notna(x) else np.nan)
    volume_std = data_df.groupby('symbol')['volume_protected'].transform(
        lambda x: x.rolling(window=n1, min_periods=w).std()
    )
    # std为0或nan时填0
    volume_std = volume_std.fillna(0).replace([np.inf, -np.inf], 0)


    # 2. 计算amount对close的滚动回归贝塔系数（使用实际窗口大小）
    def calculate_beta(group):
        # 确保amount和close非nan
        group_protected = group[['amount', 'close']].copy().dropna()
        if group_protected.empty:
            return pd.Series(np.nan, index=group.index)

        # 使用apply计算滚动beta，并处理分母为0的情况
        return group_protected['amount'].rolling(window=n2, min_periods=w).apply(
            lambda x: np.cov(x, group_protected['close'].loc[x.index])[0, 1] / (np.var(x) + 1e-8),
            raw=False
        )

    # 应用calculate_beta函数，并重新索引
    amount_beta_raw = data_df.groupby('symbol').apply(calculate_beta)
    # 将多层索引展平，并与原始数据对齐
    amount_beta = amount_beta_raw.reset_index(level=0, drop=True).reindex(data_df.index)
    # 处理beta计算结果中的inf和nan，填0
    amount_beta = amount_beta.fillna(0).replace([np.inf, -np.inf], 0)


    # 3. 计算乘积
    product = volume_std * amount_beta

    # 4. 计算arctan
    # 对product添加一个小的正数，避免arctan(0)或arctan(负数)可能引起的数值问题，虽然arctan定义域包括0和负数，但为了数值稳定性
    arctan_result = np.arctan(product + 1e-8)

    # 5. 处理无穷大 (虽然arctan结果不会是inf，但保留此步骤作为通用保护)
    arctan_result = arctan_result.replace([np.inf, -np.inf], np.nan)

    # 生成结果DataFrame
    result_df = data_df[['trade_date', 'time', 'symbol']].copy()
    result_df['factor'] = arctan_result

    # 日期时间格式转换
    result_df['trade_date'] = pd.to_datetime(result_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = pd.to_datetime(result_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 最终结果不进行fillna(0)，保留真实的NaN
    return result_df.dropna(subset=['factor'])

