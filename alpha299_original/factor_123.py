# Alpha299因子 - factor_123
# 原始因子编号: 123
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_123(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha 171因子：价格相对位置与动能比率因子
    根据公式：(Close_t - Low_t) * Open_t^5 / (High_t - Close_t) * Close_t^5

    参数:
        data_df: 包含必要列的输入DataFrame
        w: 不涉及时间窗口参数，设为None
        uni_col: 不涉及单一基础列，设为None
    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 禁用链式赋值警告（全局设置）
    pd.options.mode.chained_assignment = None  # 禁用警告

    # 检查必要列是否存在
    required_columns = ['open', 'high', 'low', 'close']
    if not all(col in data_df.columns for col in required_columns):
        raise ValueError(f"输入数据缺少必要列: {required_columns}")

    df = data_df.copy()

    # 计算分子和分母
    # 对可能为0的open进行处理，避免0的5次方
    numerator = (df['close'] - df['low']) * ((df['open'] + 1e-8) ** 5)
    # 对可能为0的close进行处理，避免0的5次方
    denominator_part1 = (df['high'] - df['close'])
    denominator_part2 = (df['close'] + 1e-8) ** 5
    denominator = denominator_part1 * denominator_part2

    # 处理分母为0或无穷大的情况
    # 增加对denominator_part1为0的处理
    df['factor'] = numerator / (denominator + 1e-8)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()
    output_df = output_df.dropna(subset=['factor'])  # 移除无效值行

    return output_df

