# Alpha299因子 - factor_152
# 原始因子编号: 152
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_152(data_df, w=12, uni_col=None):
    """
    计算Alpha 49因子，基于趋向指标UOS的一部分。

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int): 滚动窗口大小（默认12）
        uni_col (str): 本因子不依赖单一列，故设为None

    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 窗口配置
        window_configs = {
            'w': 12.0  # 基础窗口参数
        }
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    actual_w = window_sizes['w']

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'high', 'low', 'close']
    missing_cols = [col for col in required_cols if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"Missing required columns: {missing_cols}")

    df = data_df.copy()

    # 确保按symbol和时间排序
    df = df.sort_values(by=['symbol', 'time'])

    # 计算前一日的high和low
    df['DH'] = df.groupby('symbol')['high'].shift(1)
    df['DL'] = df.groupby('symbol')['low'].shift(1)

    # 计算Term_t
    df['Term_t'] = np.maximum(np.abs(df['high'] - df['DH']), np.abs(df['low'] - df['DL']))

    # 计算CondDecrease和CondIncrease
    df['CondDecrease'] = (df['high'] + df['low']) < (df['DH'] + df['DL'])
    df['CondIncrease'] = (df['high'] + df['low']) > (df['DH'] + df['DL'])

    # 计算P1和P2的滚动和
    df['P1_part'] = df['Term_t'] * df['CondDecrease'].astype(np.int64)
    df['P2_part'] = df['Term_t'] * df['CondIncrease'].astype(np.int64)

    df['P1'] = df.groupby('symbol')['P1_part'].transform(lambda x: x.rolling(window=actual_w, min_periods=w).sum())
    df['P2'] = df.groupby('symbol')['P2_part'].transform(lambda x: x.rolling(window=actual_w, min_periods=w).sum())

    # 计算alpha49
    df['P1_plus_P2'] = df['P1'] + df['P2']
    # 避免除以零，添加一个小的常数
    df['factor'] = df['P1'] / (df['P1_plus_P2'] + 1e-8)

    # 过滤掉close为NaN的行
    df = df.dropna(subset=['close'])

    # 处理无穷大值和NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    # df = df.dropna(subset=['factor']) # 保留原始的NaN，不在这里移除

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

