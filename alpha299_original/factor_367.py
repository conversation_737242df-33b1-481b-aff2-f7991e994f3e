# Alpha299因子 - factor_367
# 原始因子编号: 367
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_367(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha215因子 (Numba重构版)
    """
    
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}
    
    # ========== Numba 加速的核心计算逻辑 ==========
    @njit
    def rolling_cov_var_mean(series1, series2, cov_window, reg_window, mean_window, min_periods):
        """
        使用Numba JIT编译来手动实现滚动协方差、方差和均值计算，以保证计算路径的确定性。
        """
        n = len(series1)
        # 初始化输出数组
        out_cov12 = np.full(n, np.nan)
        out_var2 = np.full(n, np.nan)
        out_cov23 = np.full(n, np.nan)
        out_regbeta_mean = np.full(n, np.nan)
        
        # 计算 regbeta
        regbeta = np.full(n, np.nan)
        for i in range(n):
            start = max(0, i - reg_window + 1)
            # 确保有足够的数据点
            if i - start + 1 >= min_periods:
                s2_win = series2[start:i+1]
                s3_win = series1[start:i+1] # series1 在这里是 low
                
                # 计算方差和协方差
                var2 = np.var(s2_win)
                if var2 > 1e-12: # 避免除以一个极小或零的数
                    cov23 = np.cov(s2_win, s3_win)[0, 1]
                    regbeta[i] = cov23 / var2
        
        # 计算 regbeta 的滚动均值
        for i in range(n):
            start = max(0, i - mean_window + 1)
            if i - start + 1 >= min_periods:
                window = regbeta[start:i+1]
                # 过滤掉nan值进行计算
                valid_window = window[~np.isnan(window)]
                if len(valid_window) >= min_periods:
                    out_regbeta_mean[i] = np.mean(valid_window)

        # 计算 volume 和 amount 的滚动协方差
        for i in range(n):
            start = max(0, i - cov_window + 1)
            if i - start + 1 >= min_periods:
                s1_win = series1[start:i+1] # series1 在这里是 volume
                s2_win = series2[start:i+1] # series2 在这里是 amount
                out_cov12[i] = np.cov(s1_win, s2_win)[0, 1]

        return out_cov12, out_regbeta_mean
    
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 12,         # 协方差计算窗口
        'regbeta_window': 16,     # 回归系数计算窗口  
        'mean_window': 8          # 均值计算窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    cov_window = window_sizes['cov_window']
    regbeta_window = window_sizes['regbeta_window']
    mean_window = window_sizes['mean_window']
    min_periods = w if w is not None else 8

    # 复制和排序
    df = data_df.copy()
    df = df.sort_values(by=['symbol', 'time']).reset_index(drop=True)

    # 按symbol分组处理
    all_results = []
    for symbol, group in df.groupby('symbol'):
        # 提取Numpy数组以提高Numba性能
        volume = group['volume'].to_numpy()
        amount = group['amount'].to_numpy()
        low = group['low'].to_numpy()
        
        # 调用Numba JIT函数进行核心计算
        # 计算 regbeta(amount, low)
        _, regbeta_arr = rolling_cov_var_mean(low, amount, regbeta_window, regbeta_window, mean_window, min_periods)
        
        # 计算 cov(volume, amount)
        cov_arr, _ = rolling_cov_var_mean(volume, amount, cov_window, regbeta_window, mean_window, min_periods)

        # 计算最终因子值
        factor = cov_arr / (regbeta_arr + 1e-8)
        
        # 将结果存入group
        group['factor'] = factor
        all_results.append(group)
    
    # 合并结果
    if not all_results:
        return pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])
    
    result_df = pd.concat(all_results)
    
    # 格式化输出
    result_df['factor'] = result_df['factor'].replace([np.inf, -np.inf], np.nan)
    result_df['trade_date'] = pd.to_datetime(result_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = pd.to_datetime(result_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    return result_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

