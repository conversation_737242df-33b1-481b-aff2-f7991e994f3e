# Alpha299因子 - factor_400
# 原始因子编号: 400
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_400(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha92因子

    参数:
    - data_df: 输入数据DataFrame
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天（n1窗口）。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，本因子不使用单一列，设为None

    返回:
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 5,   # low的滚动标准差窗口
        'n2': 7,   # 相关系数窗口
        'n3': 6,   # 相关系数的标准差窗口
        'n4': 20   # zscore窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']

    # 检查必要的列是否存在
    required_columns = ['low', 'open', 'amount', 'volume', 'symbol', 'trade_date', 'time']
    missing_columns = [col for col in required_columns if col not in data_df.columns]
    if missing_columns:
        raise ValueError(f"缺少必要的列: {missing_columns}")

    # 创建数据副本
    df = data_df.copy()

    # 按symbol分组进行计算
    grouped = df.groupby('symbol')

    # 1. 取最低价和开盘价中的较小值: T1 = gp_min(low, open_price)
    df['T1'] = np.minimum(df['low'], df['open'])

    # 2. 计算最低价在过去n1个周期内的滚动标准差: S1 = ts_std(low, n1)
    df['S1'] = grouped['low'].transform(lambda x: x.rolling(window=n1, min_periods=w).std())

    # 3. 计算T1和S1在过去n2个周期内的滚动相关系数: T2 = ts_corr(n2, T1, S1)
    def rolling_corr(group):
        t1 = group['T1']
        s1 = group['S1']
        # 增加对常数序列和包含NaN/Inf的序列的处理
        corr_result = t1.rolling(window=n2, min_periods=w).corr(s1)
        # 将NaN和Inf替换为0，因为常数序列或包含异常值的序列相关性为0
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    df['T2'] = grouped.apply(rolling_corr).reset_index(level=0, drop=True)

    # 4. 计算T2在过去n3个周期内的滚动标准差: N1 = ts_std(T2, n3)
    df['N1'] = grouped['T2'].transform(lambda x: x.rolling(window=n3, min_periods=w).std())

    # 5. 计算成交额在过去n4个周期内的滚动Z-score: T3 = ts_zscore(amount, n4)
    # Z-score计算中分母为标准差，可能为0，需要加一个小的常数
    df['T3'] = grouped['amount'].transform(lambda x: (x - x.rolling(window=n4, min_periods=w).mean()) / (x.rolling(window=n4, min_periods=w).std() + 1e-8))
    # Z-score结果可能出现inf/-inf，替换为NaN
    df['T3'] = df['T3'].replace([np.inf, -np.inf], np.nan)


    # 6. 计算T3在过去n4个周期内的滚动Z-score: T5 = ts_zscore(T3, n4)
    # Z-score计算中分母为标准差，可能为0，需要加一个小的常数
    df['T5'] = grouped['T3'].transform(lambda x: (x - x.rolling(window=n4, min_periods=w).mean()) / (x.rolling(window=n4, min_periods=w).std() + 1e-8))
    # Z-score结果可能出现inf/-inf，替换为NaN
    df['T5'] = df['T5'].replace([np.inf, -np.inf], np.nan)

    # 7. 计算成交量的反正切值: T4 = arctan(volume)
    # arctan定义域为全体实数，但为了避免volume为负数（虽然实际数据不会），可以考虑加上一个小的常数
    df['T4'] = np.arctan(df['volume'] + 1e-8)

    # 8. 计算T5除以T4: N2 = div(T5, T4)
    # 分母T4可能接近0，需要加一个小的常数
    df['N2'] = df['T5'] / (df['T4'] + 1e-8)
    # 除法结果可能出现inf/-inf，替换为NaN
    df['N2'] = df['N2'].replace([np.inf, -np.inf], np.nan)

    # 9. 计算N1与N2的差值: N3 = sub(N1, N2)
    df['N3'] = df['N1'] - df['N2']

    # 10. 计算最低价在过去n4个周期内的滚动Z-score: L1 = ts_zscore(low, n4)
    # Z-score计算中分母为标准差，可能为0，需要加一个小的常数
    df['L1'] = grouped['low'].transform(lambda x: (x - x.rolling(window=n4, min_periods=w).mean()) / (x.rolling(window=n4, min_periods=w).std() + 1e-8))
    # Z-score结果可能出现inf/-inf，替换为NaN
    df['L1'] = df['L1'].replace([np.inf, -np.inf], np.nan)

    # 11. 计算L1与N3的差值: X1 = sub(L1, N3)
    df['X1'] = df['L1'] - df['N3']

    # 12. 计算X1除以S1得到Alpha92: Alpha92 = div(X1, S1)
    # 分母S1为标准差，可能为0，需要加一个小的常数
    df['factor'] = df['X1'] / (df['S1'] + 1e-8)

    # 13. 将结果中的无穷大值(inf, -inf)替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 转换日期格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回最终结果
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()
    return result_df

