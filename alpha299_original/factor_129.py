# Alpha299因子 - factor_129
# 原始因子编号: 129
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_129(data_df, w: int | None = None, uni_col: str | None = 'close'):
    """
    计算Alpha 178因子：成交量加权1日价格变化率因子
    核心公式：(Close_t - Close_{t-1})/Close_{t-1} * Volume_t
    """
    df = data_df.copy()

    # 确保按symbol和时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 按symbol分组计算前一日收盘价
    df['prev_close'] = df.groupby('symbol')['close'].shift(1)

    # 计算1日价格变化率，处理除以零的情况
    df['roc'] = (df['close'] - df['prev_close']) / (df['prev_close'].replace(0, 1e-8))

    # 处理roc中的inf/-inf和nan
    df['roc'] = df['roc'].replace([float('inf'), -float('inf')], float('nan'))

    # 计算最终因子值，处理volume中的inf/-inf和nan
    df['volume_safe'] = df['volume'].replace([float('inf'), -float('inf')], float('nan'))
    df['factor'] = df['roc'] * df['volume_safe']

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

