# Alpha299因子 - factor_131
# 原始因子编号: 131
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_131(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha 185因子：开盘收盘价比率平方的负排名因子

    参数:
        data_df: 输入的DataFrame，包含['symbol', 'trade_date', 'time', 'open', 'close']等列
        w: 时间窗口参数（本因子无需时间窗口，设为None）
        uni_col: 单一基础数据列（本因子使用open和close，设为None）

    返回:
        包含因子值的DataFrame，列包含['trade_date', 'time', 'symbol', 'factor']
    """
    # 数据校验
    required_columns = ['symbol', 'trade_date', 'time', 'open', 'close']
    if not all(col in data_df.columns for col in required_columns):
        raise ValueError(f"输入数据缺少必要列: {required_columns}")

    df = data_df.copy()

    # 1. 计算开盘价与收盘价的比率
    # 添加1e-8避免除以零
    df['ratio'] = df['open'] / (df['close'] + 1e-8)

    # 2. 处理除以零的情况，将±inf替换为NaN
    df['ratio'] = df['ratio'].replace([float('inf'), -float('inf')], pd.NA)

    # 3. 计算项Term_t
    df['term'] = -(1 - df['ratio'])**2

    # 4. 横截面百分比排序（按时间分组）
    # rank函数本身会处理NaN，但为了稳健性，可以先填充一个不会影响排名的值（例如，对于百分比排名，可以填充一个极小或极大的值，或者直接依赖rank的默认行为）
    # 这里依赖rank的默认行为，即NaN不参与排名，结果为NaN
    df['factor'] = df.groupby('time')['term'].transform(lambda x: x.rank(pct=True))

    # 5. 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 6. 选择输出列并处理NaN
    # 保留NaN，不在这里dropna
    output_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return output_df

