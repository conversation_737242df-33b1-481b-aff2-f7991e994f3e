# Alpha299因子 - factor_118
# 原始因子编号: 118
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_118(data_df, w: int | None = 20, uni_col: str | None = 'close'):
    """
    计算Alpha 166因子：收益率分布偏度近似因子（修复后的版本）

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'close']等列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str): 用于计算收益率的基础列（默认为'close'）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子值DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'rolling_window': 20  # 滚动计算窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    rolling_window = window_sizes['rolling_window']

    df = data_df.copy()

    # 按品种和时间排序确保时间序列顺序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 1. 计算收益率Ret_t
    df['Ret'] = df.groupby('symbol')[uni_col].pct_change()

    # 2. 计算期简单移动平均MARet
    df['MARet'] = df.groupby('symbol')['Ret'].transform(
        lambda x: x.rolling(window=rolling_window, min_periods=w).mean()
    )

    # 3. 计算SumDevRet_t
    df['DevRet'] = df['Ret'] - df['MARet']
    df['SumDevRet'] = df.groupby('symbol')['DevRet'].transform(
        lambda x: x.rolling(window=rolling_window, min_periods=w).sum()
    )

    # 4. 计算SumSqRelStr_t
    # 确保 (1 + df['Ret']) 不为负数，虽然收益率通常不会小于-1，但为安全起见
    df['RelStr'] = (1 + df['Ret'].clip(lower=-0.999999)) ** 2
    df['SumSqRelStr'] = df.groupby('symbol')['RelStr'].transform(
        lambda x: x.rolling(window=rolling_window, min_periods=w).sum()
    )

    # 5. 动态计算分子和分母（根据窗口大小rolling_window）
    df['Num'] = -rolling_window * ((rolling_window-1) ** 1.5) * df['SumDevRet']
    # 确保 SumSqRelStr ** 1.5 的底数不为负，且分母不为0
    df['Denom'] = ((rolling_window-1) * (rolling_window-2)) * ((df['SumSqRelStr'].clip(lower=1e-8)) ** 1.5)

    # 6. 计算最终因子值
    # 避免直接除以可能为0的分母
    df['factor'] = df['Num'] / (df['Denom'] + 1e-8)

    # 处理分母为0的情况（无穷大）和NaN
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], float('nan'))


    # 格式化日期和时间列
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

