# Alpha299因子 - factor_272
# 原始因子编号: 272
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_272(data_df, w: int | None = 8, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 8        # DELTA计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']        # DELTA计算窗口

    df = data_df.copy()

    # 计算VWAP：成交金额除以成交量
    # 添加保护以避免除零错误
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    vwap_group = df.groupby(['symbol', 'trade_date']).apply(
        lambda group: (group['close'] * group['volume']).sum() / (group['volume'].sum() + 1e-8)
    ).reset_index(name='vwap_calc')
    df = df.merge(vwap_group, on=['symbol', 'trade_date'], how='left')
    
    # 使用计算的vwap_calc覆盖原来的vwap（如果计算成功）
    df['vwap'] = df['vwap_calc'].fillna(df['vwap'])
    df.drop('vwap_calc', axis=1, inplace=True)

    # 计算LOG(VWAP)
    # 确保vwap是正数，避免log(0)或log(负数)
    df['log_vwap'] = np.log(df['vwap'].abs() + 1e-8)

    # 计算DELTA(log_vwap, delta_window)
    df.sort_values(by=['symbol', 'trade_date'], inplace=True)
    df['log_vwap_shifted'] = df.groupby('symbol')['log_vwap'].shift(delta_window)
    df['factor'] = df['log_vwap'] - df['log_vwap_shifted']

    # 处理无穷大和负无穷
    df['factor'].replace([np.inf, -np.inf], np.nan, inplace=True)

    # 转换日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

