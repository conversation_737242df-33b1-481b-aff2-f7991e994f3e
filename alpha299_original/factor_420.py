# Alpha299因子 - factor_420
# 原始因子编号: 420
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_420(data_df, w: int | None = None, uni_col: str | None = 'close'):
    """
    计算输入序列的三角正切值
    
    参数:
    data_df: 输入的DataFrame
    w: 窗口期参数，本因子不需要窗口期，设为None
    uni_col: 用于计算因子的数据列，默认为'close'
    
    返回:
    包含因子值的DataFrame
    """
    # 复制输入数据，避免修改原始数据
    df = data_df.copy()
    
    # 确保输入列存在
    if uni_col not in df.columns:
        raise ValueError(f"输入数据中缺少必要的列: {uni_col}")
    
    # 计算正切值
    # np.tan 在输入为 pi/2 + n*pi 时会产生 inf 或 -inf，
    # 考虑到实际金融数据通常不会精确到这些值，且np.tan本身对nan有处理，
    # 这里主要处理可能出现的inf/-inf
    df['factor'] = np.tan(df[uni_col])
    
    # 处理无穷值和NaN
    # 将计算出的inf/-inf替换为NaN，保留真实的NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    
    # 转换日期格式
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')
    
    # 恢复日期格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 选择输出列
    # 保留dropna()以移除计算过程中产生的NaN
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    return result_df

