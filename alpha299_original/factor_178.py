# Alpha299因子 - factor_178
# 原始因子编号: 178
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_178(data_df, w: int | None = 3, uni_col: str | None = None):
    """
    计算Alpha 7因子：VWAP与收盘价偏离及成交量变化因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要字段
        w (int | None): 滚动窗口天数，默认3天
        uni_col (str | None): 单一基础数据列参数，此处设为None

    返回:
        pd.DataFrame: 包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']
    """
    # 窗口配置
    window_configs = {
        'w': 3.0  # 基础窗口参数
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    actual_w = window_sizes['w']
    
    df = data_df.copy()

    # 1. 计算VWAP与收盘价的差值Dt
    # 避免volume为0导致除以0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
    df['D_t'] = df['vwap'] - df['close']

    # 2. 计算Dt的滚动最大值和最小值（使用实际窗口大小）
    df['D_max3'] = df.groupby('symbol')['D_t'].transform(
        lambda x: x.rolling(window=actual_w, min_periods=w).max()
    )
    df['D_min3'] = df.groupby('symbol')['D_t'].transform(
        lambda x: x.rolling(window=actual_w, min_periods=w).min()
    )

    # 3. 对D_max3和D_min3进行横截面百分位排序
    df['R_Dmax'] = df.groupby(['trade_date', 'time'])['D_max3'].transform(
        lambda x: x.rank(pct=True)
    )
    df['R_Dmin'] = df.groupby(['trade_date', 'time'])['D_min3'].transform(
        lambda x: x.rank(pct=True)
    )

    # 4. 计算成交量的差分（使用实际窗口大小）
    df['delta_vol'] = df.groupby('symbol')['volume'].transform(
        lambda x: x - x.shift(actual_w)
    )

    # 5. 对delta_vol进行滚动标准化（使用实际窗口大小）
    # 避免滚动标准差为0导致除以0
    df['Z_delta_vol'] = df.groupby('symbol')['delta_vol'].transform(
        lambda x: (x - x.rolling(window=actual_w, min_periods=w).mean()) / (x.rolling(window=actual_w, min_periods=w).std() + 1e-8)
    )

    # 6. 对标准化后的Z_delta_vol进行横截面百分位排序
    df['R_delta_vol'] = df.groupby(['trade_date', 'time'])['Z_delta_vol'].transform(
        lambda x: x.rank(pct=True)
    )

    # 7. 计算最终因子值
    df['factor'] = (df['R_Dmax'] + df['R_Dmin']) * df['R_delta_vol']

    # 8. 处理无效值
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 9. 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].astype('string')
    df['time'] = df['time'].astype('string')

    # 10. 选择输出列并去除无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

