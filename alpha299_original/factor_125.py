# Alpha299因子 - factor_125
# 原始因子编号: 125
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_125(data_df, w: int | None = 12, uni_col: str | None = 'close'):
    """
    计算Alpha因子：DEMA和TEMA组合的Z-score标准化因子
    
    参数:
        data_df (pd.DataFrame): 输入数据
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为12天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 用于计算的基础列，默认'close'
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'ema_span': 12,      # EMA的span参数
        'zscore_window': 12  # Z-score标准化窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    ema_span = window_sizes['ema_span']
    zscore_window = window_sizes['zscore_window']

    df = data_df.copy()

    # 计算DEMA_C部分
    df['EMA1_C'] = df.groupby('symbol')[uni_col].transform(lambda x: x.ewm(span=ema_span, adjust=False).mean())
    df['EMA2_C'] = df.groupby('symbol')['EMA1_C'].transform(lambda x: x.ewm(span=ema_span, adjust=False).mean())
    df['DEMA_C'] = 3 * df['EMA1_C'] - 2 * df['EMA2_C']

    # 计算TEMA_LNC部分
    # 对log的输入进行保护
    df['LNC'] = np.log(df[uni_col] + 1e-8)
    df['EMA1_LNC'] = df.groupby('symbol')['LNC'].transform(lambda x: x.ewm(span=ema_span, adjust=False).mean())
    df['EMA2_LNC'] = df.groupby('symbol')['EMA1_LNC'].transform(lambda x: x.ewm(span=ema_span, adjust=False).mean())
    df['EMA3_LNC'] = df.groupby('symbol')['EMA2_LNC'].transform(lambda x: x.ewm(span=ema_span, adjust=False).mean())

    # 滚动Z-score标准化
    def rolling_zscore(x, window):
        # 在计算均值和标准差之前，先处理inf和nan
        x_cleaned = x.replace([np.inf, -np.inf], np.nan)
        mean = x_cleaned.rolling(window=window, min_periods=w).mean()
        # 对标准差加一个小的常数，避免除以0
        std = x_cleaned.rolling(window=window, min_periods=w).std(ddof=1)
        # 计算zscore，并对结果中的inf和nan进行处理
        zscore = (x_cleaned - mean) / (std + 1e-8)
        return zscore.replace([np.inf, -np.inf], np.nan)

    df['DEMA_C_z'] = df.groupby('symbol')['DEMA_C'].transform(lambda x: rolling_zscore(x, zscore_window))
    df['TEMA_LNC_z'] = df.groupby('symbol')['EMA3_LNC'].transform(lambda x: rolling_zscore(x, zscore_window))

    # 最终因子
    df['factor'] = df['DEMA_C_z'] + df['TEMA_LNC_z']

    # 处理无穷值和NaN值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 日期时间格式处理
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S')

    # 输出列筛选
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

