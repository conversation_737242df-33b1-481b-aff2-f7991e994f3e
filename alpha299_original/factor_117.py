# Alpha299因子 - factor_117
# 原始因子编号: 117
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_117(data_df, w: int | None = 20, uni_col: str | None = None):
    """
    计算Alpha因子：基于收益率、成交量和价格的复合指标
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数，本因子涉及多列，设为None
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'ma_volume_window': 20  # 成交量移动平均窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    ma_volume_window = window_sizes['ma_volume_window']

    df = data_df.copy()

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest', 'industry_name']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"缺少必要列: {col}")

    # 处理VWAP列，若不存在则计算
    # 确保分母不为0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 计算收益率RET_t（按symbol分组）
    df['ret'] = df.groupby('symbol')['close'].pct_change()

    # 计算成交量移动平均
    df['ma_volume'] = df.groupby('symbol')['volume'].rolling(window=ma_volume_window, min_periods=w).mean().reset_index(level=0, drop=True)

    # 计算High - Close
    df['high_minus_close'] = df['high'] - df['close']

    # 计算Term_t
    # 确保乘法中的因子不会导致inf或nan扩散，pct_change可能产生inf，ma_volume可能产生nan
    df['term'] = (-df['ret'].replace([np.inf, -np.inf], np.nan)) * df['ma_volume'] * df['vwap'] * df['high_minus_close']

    # 处理无穷大值为NaN
    df['term'] = df['term'].replace([np.inf, -np.inf], np.nan)

    # 按时间排序并分组计算横截面百分比排名
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')
    df.sort_values('time', inplace=True)

    def rank_group(group):
        valid_mask = group['term'].notna()
        valid_term = group.loc[valid_mask, 'term']
        if len(valid_term) == 0:
            return pd.Series([np.nan] * len(group), index=group.index)
        # rankdata会自动处理nan_policy='omit'，但为了安全，确保输入没有inf
        valid_term = valid_term.replace([np.inf, -np.inf], np.nan).dropna()
        if len(valid_term) == 0:
             return pd.Series([np.nan] * len(group), index=group.index)
        ranks = rankdata(valid_term, method='average') # nan_policy='omit' is default for numpy rankdata
        percent_rank = ranks / (len(valid_term) + 1e-8)
        result = pd.Series([np.nan] * len(group), index=group.index)
        result[valid_term.index] = percent_rank # Ensure ranks are placed back correctly
        return result

    # 使用apply并处理返回的Series，确保索引对齐
    rank_result = df.groupby('time').apply(lambda g: rank_group(g.drop(columns=['time'])))
    
    # 确保结果能正确展开为Series
    if isinstance(rank_result, pd.DataFrame):
        # 如果结果是DataFrame，需要将其展开为Series
        df['factor'] = rank_result.stack().reset_index(level=[0,1], drop=True)
    else:
        # 如果结果是Series，直接使用
        df['factor'] = rank_result.reset_index(level=0, drop=True)

    # 选择输出列并去除NaN值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    # Check if we have any results before processing
    if not output_df.empty:
        # 恢复日期和时间格式为字符串
        output_df['trade_date'] = pd.to_datetime(output_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
        output_df['time'] = output_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return output_df

