# Alpha299因子 - factor_110
# 原始因子编号: 110
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_110(data_df, w: int | None = 9, uni_col: str | None = 'close'):
    """
    计算优化后的Alpha 152因子。

    参数:
        data_df (pd.DataFrame): 包含必要字段的输入数据
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 用于计算的单一基础数据列，默认值'close'

    返回:
        pd.DataFrame: 包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'relstr_lag': 9,        # 价格相对强度滞后期
        'ema_span': 17,         # EMA的span参数
        'ma12_window': 12,      # MA12窗口
        'ma26_window': 26       # MA26窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    relstr_lag = window_sizes['relstr_lag']
    ema_span = window_sizes['ema_span']
    ma12_window = window_sizes['ma12_window']
    ma26_window = window_sizes['ma26_window']

    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'close']
    if not all(col in data_df.columns for col in required_cols):
        raise ValueError(f"输入数据缺少必要列: {required_cols}")

    df = data_df.copy()

    # 步骤1: 计算价格相对强度 RelStr_t = Close_t / Close_{t-relstr_lag}
    # 确保分母不为0
    df['RelStr'] = df.groupby('symbol')[uni_col].transform(lambda x: x / (x.shift(relstr_lag).replace(0, 1e-8) + 1e-8))

    # 步骤2: 取其1期滞后值 LagRS_t = RelStr_{t-1}
    df['LagRS'] = df.groupby('symbol')['RelStr'].transform(lambda x: x.shift(1))

    # 步骤3: 对 LagRS_t 进行EMA平滑得到 EMA1_LRS_t
    df['EMA1_LRS'] = df.groupby('symbol')['LagRS'].transform(
        lambda x: x.ewm(span=ema_span, adjust=False, min_periods=w).mean()
    )

    # 步骤4: 取 EMA1_LRS_t 的1期滞后值 LagEMA1_LRS_t
    df['LagEMA1_LRS'] = df.groupby('symbol')['EMA1_LRS'].transform(lambda x: x.shift(1))    

    # 步骤5: 计算 LagEMA1_LRS_t 的MA12和MA26
    df['MA12'] = df.groupby('symbol')['LagEMA1_LRS'].transform(
        lambda x: x.rolling(window=ma12_window, min_periods=w).mean()
    )
    df['MA26'] = df.groupby('symbol')['LagEMA1_LRS'].transform(
        lambda x: x.rolling(window=ma26_window, min_periods=w).mean()
    )

    # 步骤6: 计算 MA12 和 MA26 的差值 DiffMA_t
    df['DiffMA'] = df['MA12'] - df['MA26']

    # 步骤7: 对 DiffMA_t 进行EMA平滑得到原始因子值 alpha152_raw
    df['alpha152_raw'] = df.groupby('symbol')['DiffMA'].transform(
        lambda x: x.ewm(span=ema_span, adjust=False, min_periods=w).mean()
    )

    # 步骤8: 对原始因子值进行 tanh 变换
    # tanh函数定义域为全体实数，不需要特殊处理
    df['factor'] = df['alpha152_raw'].apply(np.tanh)

    # 步骤9: 替换 ±inf 为 NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 格式化日期和时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return output_df

