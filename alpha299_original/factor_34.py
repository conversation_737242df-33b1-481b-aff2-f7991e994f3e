# Alpha299因子 - factor_34
# 原始因子编号: 34
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_34(data_df, w: int | None = 3, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 3,       # DELTA窗口
        'ts_max_window': 5,      # ts_max窗口
        'corr_window': 5,        # 相关性窗口
        'ts_rank_window': 10,    # ts_rank窗口
        'adv_window': 20         # ADV计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']       # DELTA窗口
    ts_max_window = window_sizes['ts_max_window']     # ts_max窗口
    corr_window = window_sizes['corr_window']         # 相关性窗口
    ts_rank_window = window_sizes['ts_rank_window']   # ts_rank窗口
    adv_window = window_sizes['adv_window']           # ADV计算窗口

    df = data_df.copy()

    # 窗口已经是整数，不需要再转换
    delta_window_int = delta_window
    ts_max_window_int = ts_max_window
    corr_window_int = corr_window
    ts_rank_window_int = ts_rank_window

    # 计算VWAP
    df['typical_price'] = (df['high'] + df['low'] + df['close']) / (3 + 1e-8)
    df['typical_price_vol'] = df['typical_price'] * df['volume']
    df['cum_typical_price_vol'] = df.groupby(['symbol', 'trade_date'])['typical_price_vol'].cumsum()
    df['cum_volume'] = df.groupby(['symbol', 'trade_date'])['volume'].cumsum()
    df['vwap'] = df['cum_typical_price_vol'] / (df['cum_volume'] + 1e-8)

    # 行业中性化VWAP
    df['indneutralize_vwap'] = df.groupby(['trade_date', 'industry_name'])['vwap'].transform(lambda x: x - x.mean())

    # 计算delta_vwap
    df['delta_vwap'] = df.groupby('symbol')['indneutralize_vwap'].transform(lambda x: x.diff(delta_window_int))

    # ts_max
    df['ts_max_delta'] = df.groupby('symbol')['delta_vwap'].transform(lambda x: x.rolling(window=ts_max_window_int, min_periods=w).max())

    # 截面排名
    df['rank_part'] = df.groupby('trade_date')['ts_max_delta'].transform(lambda x: x.rank(pct=True))

    # 加权价格
    df['weighted_price'] = 0.490655 * df['close'] + (1 - 0.490655) * df['vwap']

    # ADV
    df['adv20'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=adv_window, min_periods=w).mean())

    # 相关性
    def calc_corr(group):
        # 确保输入没有inf或nan，并处理常数序列的情况
        weighted_price = group['weighted_price'].replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill')
        adv20 = group['adv20'].replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill')

        # 检查是否有常数序列，如果是则corr为0
        if weighted_price.std() < 1e-8 or adv20.std() < 1e-8:
            return pd.Series(0, index=group.index)
        else:
            corr_result = weighted_price.rolling(window=corr_window_int, min_periods=w).corr(adv20)
            # 将corr结果中的nan或inf替换为0
            return corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    df['correlation'] = df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)

    # ts_rank
    def ts_rank_func(x):
        # 确保输入没有inf或nan
        x_cleaned = x.replace([np.inf, -np.inf], np.nan)
        # 使用rankdata处理nan
        ranked_data = rankdata(x_cleaned.dropna(), method='average')
        if len(ranked_data) > 0:
            # 找到原始序列中最后一个非nan值对应的排名
            last_valid_index = x_cleaned.last_valid_index()
            if last_valid_index is not None:
                 # 获取最后一个有效值的排名
                last_valid_value = x_cleaned.loc[last_valid_index]
                # 找到这个值在非nan序列中的位置
                original_indices = x_cleaned.dropna().index.tolist()
                try:
                    pos_in_cleaned = original_indices.index(last_valid_index)
                    return ranked_data[pos_in_cleaned] / (len(x_cleaned.dropna()) + 1e-8)
                except ValueError:
                    # 如果最后一个有效值不在dropna()后的序列中（理论上不应该发生），返回nan
                    return np.nan
            else:
                return np.nan # 如果整个窗口都是nan，返回nan
        else:
            return np.nan # 如果窗口为空，返回nan

    df['ts_rank_part'] = df.groupby('symbol')['correlation'].transform(
        lambda x: x.rolling(window=ts_rank_window_int, min_periods=w).apply(ts_rank_func, raw=False)
    )

    # 组合并取负
    # 处理rank_part和ts_rank_part中的inf/nan，避免幂运算产生inf/nan
    df['rank_part_cleaned'] = df['rank_part'].replace([np.inf, -np.inf], np.nan)
    df['ts_rank_part_cleaned'] = df['ts_rank_part'].replace([np.inf, -np.inf], np.nan)

    # 确保底数大于0，指数不为nan
    df['factor'] = - (np.power(df['rank_part_cleaned'].clip(lower=1e-8), df['ts_rank_part_cleaned']))


    # 格式恢复
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

