# Alpha299因子 - factor_102
# 原始因子编号: 102
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_102(data_df, w: int | None = 3, uni_col: str | None = None):
    """
    计算Alpha 140因子，基于时间序列滚动标准化、线性衰减加权和横截面排名等操作。
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列如open, close等。
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 当前因子未使用单一基础列，故设为None。
        
    返回:
        pd.DataFrame: 包含trade_date, time, symbol, factor四列的因子结果。
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n_decay1': 8,      # DecayLinear窗口1
        'n_decay2': 7,      # DecayLinear窗口2
        'n_rank1': 8,       # ts_rank窗口1
        'n_rank2': 20,      # ts_rank窗口2
        'n_corr': 8,        # corr窗口
        'n_rank3': 3,       # ts_rank窗口3
        'n_z': 20,          # ts_zscore窗口
        'mav_window': 60    # 成交量移动平均窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n_decay1 = window_sizes['n_decay1']
    n_decay2 = window_sizes['n_decay2']
    n_rank1 = window_sizes['n_rank1']
    n_rank2 = window_sizes['n_rank2']
    n_corr = window_sizes['n_corr']
    n_rank3 = window_sizes['n_rank3']
    n_z = window_sizes['n_z']
    mav_window = window_sizes['mav_window']
    
    df = data_df.copy()
    
    # 确保日期和时间列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 按时间排序
    df = df.sort_values(by=['trade_date', 'time', 'symbol'])
    
    # 定义辅助函数
    def ts_zscore(group, window):
        """时序滚动标准化"""
        return group.rolling(window=window, min_periods=w).apply(
            lambda x: (x.iloc[-1] - x.mean()) / (x.std() + 1e-8) if x.std() != 0 else 0, raw=False
        )
    
    def decay_linear(group, window):
        """线性衰减加权求和"""
        weights = np.arange(1, window+1) / (np.sum(np.arange(1, window+1)) + 1e-8)
        return group.rolling(window=window, min_periods=w).apply(
            lambda x: np.sum(weights[-len(x):] * x) if len(x) > 0 else np.nan, raw=False
        )
    
    def ts_rank(group, window):
        """时间序列百分比排名"""
        return group.rolling(window=window, min_periods=w).apply(
            lambda x: (rankdata(x, method='average', nan_policy='omit')[-1] - 1) / (len(x) - 1 + 1e-8) if len(x) > 1 else np.nan, raw=False
        )
    
    def bimin(x, y):
        """处理NaN的最小值"""
        return np.minimum(np.where(pd.isna(x), np.inf, x), np.where(pd.isna(y), np.inf, y))
    
    # Step 1: 对open, low, high, close进行ts_zscore
    for col in ['open', 'low', 'high', 'close']:
        z_col = f'Z_{col}'
        df[z_col] = df.groupby('symbol')[col].transform(lambda x: ts_zscore(x, window=n_z))
    
    # Step 2: 计算排名组合项Term_1
    df['Term1'] = (
        df.groupby('symbol')['Z_open'].transform(lambda x: ts_rank(x, n_rank1)) +
        df.groupby('symbol')['Z_low'].transform(lambda x: ts_rank(x, n_rank1)) -
        df.groupby('symbol')['Z_high'].transform(lambda x: ts_rank(x, n_rank1)) -
        df.groupby('symbol')['Z_close'].transform(lambda x: ts_rank(x, n_rank1))
    )
    
    # Step 3: 应用线性衰减加权
    df['DL1'] = df.groupby('symbol')['Term1'].transform(lambda x: decay_linear(x, n_decay1))
    
    # Step 4: 对DL1进行横截面排名R1
    df['R1'] = df.groupby('trade_date')['DL1'].transform(lambda x: 
        (rankdata(x, method='average', nan_policy='omit') - 1) / (len(x) - 1 + 1e-8) if len(x) > 1 else np.nan
    )
    
    # Step 5: 计算收盘价的ts_rank
    df['TSR_close'] = df.groupby('symbol')['close'].transform(lambda x: ts_rank(x, n_rank1))
    
    # Step 6: 计算成交量均值的ts_rank
    df['MAV_volume'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=mav_window, min_periods=w).mean())
    df['TSR_MAV'] = df.groupby('symbol')['MAV_volume'].transform(lambda x: ts_rank(x, n_rank2))
    
    # Step 7: 计算相关系数Corr_2
    def calc_corr(group):
        # Ensure no constant series or inf/nan issues in rolling corr
        corr_result = group['TSR_close'].rolling(window=n_corr, min_periods=w).corr(group['TSR_MAV'])
        # Replace inf, -inf, and nan from corr with 0
        return corr_result.replace([np.inf, -np.inf, np.nan], 0)
    
    df['Corr2'] = df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)
    
    # Step 8: 应用线性衰减加权
    df['DL2'] = df.groupby('symbol')['Corr2'].transform(lambda x: decay_linear(x, n_decay2))
    
    # Step 9: 计算DL2的ts_rank
    df['R2'] = df.groupby('symbol')['DL2'].transform(lambda x: ts_rank(x, n_rank3))
    
    # Step 10: 计算最终因子值
    df['factor'] = df.apply(lambda row: bimin(row['R1'], row['R2']), axis=1)
    
    # Step 11: 替换无效值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    
    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()
    
    # 严格恢复日期和时间格式
    output_df['trade_date'] = pd.to_datetime(output_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    output_df['time'] = pd.to_datetime(output_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    return output_df.dropna()

