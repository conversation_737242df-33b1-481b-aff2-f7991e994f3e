# Alpha299因子 - factor_371
# 原始因子编号: 371
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_371(data_df, w: int | None = 16, uni_col: str | None = None):
    """
    计算Alpha224因子：16周期内成交量对数值与开盘收盘价最小值的相关系数

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为16天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 本因子不涉及单一基础列替换，故默认为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 16    # TS_CORR窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']

    df = data_df.copy()

    # 1. 计算LOG(VOLUME)
    # 添加对volume的保护，避免log(0)或log(负数)
    df['log_volume'] = np.log(np.abs(df['volume']) + 1e-8)

    # 2. 计算GP_MIN(OPEN, CLOSE)
    df['gp_min'] = df[['open', 'close']].min(axis=1)

    # 3. 按symbol分组计算TS_CORR(16, log_volume, gp_min)
    def _rolling_corr(group):
        # 确保时间序列有序
        group = group.sort_values('time')
        # 计算滚动相关系数
        # 在计算corr之前，对可能导致std为0的情况进行处理，例如常数序列
        # 这里的corr函数内部会处理NaN，但对于常数序列，std为0，corr无意义
        # 可以考虑在计算corr后对结果进行后处理
        rolling_corr_result = group['log_volume'].rolling(window=corr_window, min_periods=w).corr(group['gp_min'])
        # 处理滚动相关系数计算结果中的NaN和inf，将其替换为0
        rolling_corr_result = rolling_corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)
        return rolling_corr_result

    df['factor'] = df.groupby('symbol').apply(_rolling_corr).reset_index(level=[0,1], drop=True)

    # 4. 处理无效值 (已在_rolling_corr函数中处理，但保留此步骤以防万一)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 5. 严格遵循日期时间格式要求
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 6. 构建输出DataFrame
    # 保留原始的dropna行为
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

