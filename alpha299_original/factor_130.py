# Alpha299因子 - factor_130
# 原始因子编号: 130
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_130(data_df, w: int | None = 4, uni_col: str | None = None):
    """
    计算Alpha因子：基于VWAP、成交量相关性和价格低点的复合因子
    
    参数:
        data_df (pd.DataFrame): 输入数据
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列（此处不适用，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 4,   # VWAP和Volume相关性窗口
        'n2': 20,  # 标准化窗口
        'n3': 50,  # 成交量移动平均窗口
        'n4': 12   # 最终相关性窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']

    df = data_df.copy()

    # 计算VWAP
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 计算CorrVWAPVol_t：VWAP和Volume的n1期滚动相关系数
    def rolling_corr(group):
        corr_result = group['vwap'].rolling(window=n1, min_periods=w).corr(group['volume'])
        return corr_result.replace([float('inf'), float('-inf')], float('nan')).fillna(0) # 应对滚动corr可能产生的inf/nan
    
    df['CorrVWAPVol_t'] = df.groupby('symbol').apply(rolling_corr).reset_index(level=0, drop=True)

    # 对CorrVWAPVol_t进行横截面百分比排序
    df['R1'] = df.groupby('time')['CorrVWAPVol_t'].transform(lambda x: x.rank(pct=True))

    # 计算MA_n3(Volume)
    df['MA_n3_Volume'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=n3, min_periods=w).mean()
    )

    # 对MA_n3_Volume进行n2期滚动标准化
    def rolling_zscore(group):
        mean = group.rolling(window=n2, min_periods=w).mean()
        std = group.rolling(window=n2, min_periods=w).std(ddof=0)
        zscore = (group - mean) / (std + 1e-8)
        return zscore.replace([float('inf'), float('-inf')], float('nan')) # 应对标准化可能产生的inf/nan
    
    df['ZScoreMAVol50_t'] = df.groupby('symbol')['MA_n3_Volume'].transform(rolling_zscore)

    # 对Low进行n2期滚动标准化
    df['ZScoreLow_t'] = df.groupby('symbol')['low'].transform(
        lambda x: (x - x.rolling(window=n2, min_periods=w).mean()) / (x.rolling(window=n2, min_periods=w).std(ddof=0) + 1e-8)
    ).replace([float('inf'), float('-inf')], float('nan')) # 应对标准化可能产生的inf/nan

    # 对ZScoreLow_t和ZScoreMAVol50_t进行横截面百分比排序
    df['RankZScoreLow_t'] = df.groupby('time')['ZScoreLow_t'].transform(lambda x: x.rank(pct=True))
    df['RankZScoreMAVol50_t'] = df.groupby('time')['ZScoreMAVol50_t'].transform(lambda x: x.rank(pct=True))

    # 计算RankZScoreLow_t和RankZScoreMAVol50_t的n4期滚动相关系数
    def rolling_corr_z(group):
        corr_result = group['RankZScoreLow_t'].rolling(window=n4, min_periods=w).corr(group['RankZScoreMAVol50_t'])
        return corr_result.replace([float('inf'), float('-inf')], float('nan')).fillna(0) # 应对滚动corr可能产生的inf/nan
    
    df['CorrZScoreLowMAVol_t'] = df.groupby('symbol').apply(rolling_corr_z).reset_index(level=0, drop=True)

    # 对CorrZScoreLowMAVol_t进行横截面百分比排序
    df['R2'] = df.groupby('time')['CorrZScoreLowMAVol_t'].transform(lambda x: x.rank(pct=True))

    # 计算最终因子值
    df['factor'] = df['R1'] * df['R2']

    # 处理±inf为NaN
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], float('nan'))

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去重
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

