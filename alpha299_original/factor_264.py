# Alpha299因子 - factor_264
# 原始因子编号: 264
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_264(data_df, w: int | None = 4, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'low_pct_window': 4,    # 低价变化窗口
        'vwap_pct_window': 12   # VWAP变化窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    low_pct_window = window_sizes['low_pct_window']      # 低价变化窗口
    vwap_pct_window = window_sizes['vwap_pct_window']    # VWAP变化窗口

    # 检查必要列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'low', 'amount', 'volume']
    missing_cols = [col for col in required_columns if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    # 按symbol和trade_date分组，计算累计金额和成交量，得到VWAP
    df = data_df.copy()
    df['cum_amount'] = df.groupby(['symbol', 'trade_date'])['amount'].cumsum()
    df['cum_volume'] = df.groupby(['symbol', 'trade_date'])['volume'].cumsum()
    # 避免除以零
    df['VWAP'] = df['cum_amount'] / (df['cum_volume'] + 1e-8)

    # 按symbol分组计算百分比变化
    # pct_change本身会处理0值，但为了鲁棒性，可以考虑对输入进行微小调整，不过对于价格和成交量通常为正，此处保持原样
    df['low_pct'] = df.groupby('symbol')['low'].pct_change(periods=low_pct_window)
    df['vwap_pct'] = df.groupby('symbol')['VWAP'].pct_change(periods=vwap_pct_window)

    # 逐元素取最小值
    df['factor'] = df[['low_pct', 'vwap_pct']].min(axis=1)

    # 替换无穷大值为NaN
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], float('nan'))

    # 严格恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去除NaN
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

