# Alpha299因子 - factor_211
# 原始因子编号: 211
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_211(data_df, w=8, uni_col=None,
               alpha: float = 1.0, w_max: float = 300.0, lambda_rate: float = 0.1):
    # 定义所有窗口的基准值
    window_configs = {
        'base_window': 8,        # 基础窗口
        'cov_window': 16,        # 协方差窗口 (2*w)
        'zscore_window': 16,     # zscore窗口 (2*w)
        'min_window': 6          # 最小值窗口 (0.75*w)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        nonlocal w_max, lambda_rate, alpha
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    base_window = window_sizes['base_window']           # 基础窗口
    cov_window = window_sizes['cov_window']             # 协方差窗口
    zscore_window = window_sizes['zscore_window']       # zscore窗口
    min_window = window_sizes['min_window']             # 最小值窗口

    """
    计算Alpha127因子，使用动态窗口系统。核心参数w代表基础时间窗口，其他窗口参数基于动态系统进行调整。

    参数:
        data_df (pd.DataFrame): 输入数据，必须包含指定的列
        w (int): 基础时间窗口，用于动态窗口系统，默认值8
        uni_col (str): 本因子不依赖单一基础列，因此设置为None
        alpha: 动态窗口的非线性调整参数（默认1.0）
        w_max: 动态窗口的绝对上限（默认300.0）
        lambda_rate: 动态窗口的增长率（默认0.1）

    返回:
        pd.DataFrame: 包含因子值的DataFrame，包含列['trade_date', 'time', 'symbol', 'factor']
    """

    # 确保数据按symbol和时间排序
    df = data_df.copy()
    df = df.sort_values(by=['symbol', 'time'])

    # 计算delta(high, 使用动态窗口)
    df['delta_high'] = df.groupby('symbol')['high'].transform(
        lambda x: x - x.shift(base_window)
    )

    # 计算ts_zscore(delta_high, 使用动态窗口)
    # 增加对std为0的处理
    df['zscore_delta_high'] = df.groupby('symbol')['delta_high'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                 (x.rolling(window=zscore_window, min_periods=w).std() + 1e-8)
    )

    # 计算ts_cov(使用动态窗口, volume, close)
    # pandas rolling.cov会自动处理nan，但常数序列cov为nan，这里不做额外处理，后续统一处理nan
    df['ts_cov'] = df.groupby('symbol').apply(
        lambda group: group['volume'].rolling(window=cov_window, min_periods=w).cov(
            group['close']
        )
    ).reset_index(level=0, drop=True)

    # 计算ts_min(ts_cov, 使用动态窗口)
    df['ts_min'] = df.groupby('symbol')['ts_cov'].transform(
        lambda x: x.rolling(window=min_window, min_periods=w).min()
    )

    # 计算arctan(ts_min)
    # arctan定义域为R，不需要特殊处理0，但为了统一风格，可以使用apply
    df['arctan_ts_min'] = df['ts_min'].apply(lambda x: atan(x) if pd.notna(x) else np.nan)

    # 合并两个无量纲项
    df['factor'] = df['zscore_delta_high'] + df['arctan_ts_min']

    # 处理无穷大值和NaN值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    # 因子计算过程中产生的NaN保留

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 检查必要列
    required_columns = ['trade_date', 'time', 'symbol', 'factor']
    missing_cols = [col for col in required_columns if col not in df.columns]
    if missing_cols:
        raise ValueError(f"输出DataFrame缺少必要列: {missing_cols}")

    return df[required_columns]

