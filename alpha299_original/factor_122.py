# Alpha299因子 - factor_122
# 原始因子编号: 122
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_122(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha 170因子

    参数:
        data_df (pd.DataFrame): 输入数据
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列（此处不适用，设为None）

    返回:
        pd.DataFrame: 包含 ['trade_date', 'time', 'symbol', 'factor'] 的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'ma_volume_window': 20,  # TermA的MA窗口 = 4*w (默认20天)
        'ma_high_window': 5      # TermB的MA窗口 = w (默认5天)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    w_ma_volume = window_sizes['ma_volume_window']
    w_ma_high = window_sizes['ma_high_window']

    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'close', 'volume', 'high', 'amount']
    missing_cols = [col for col in required_cols if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {', '.join(missing_cols)}")

    df = data_df.copy()

    # 按品种和时间排序
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 计算VWAP（成交金额/成交量）
    df['vwap'] = df.groupby('symbol')['amount'].transform(lambda x: x.cumsum()) / \
                (df.groupby('symbol')['volume'].transform(lambda x: x.cumsum()) + 1e-8)

    # 计算TermA
    df['1_close'] = 1 / (df['close'] + 1e-8)
    df['R_1_C'] = df.groupby(['trade_date', 'time'])['1_close'].transform(lambda x: x.rank(pct=True))

    # 计算MA_volume
    df['MA_volume'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=w_ma_volume, min_periods=w_ma_volume).mean()
    )

    # TermA计算
    df['TermA'] = (df['volume'] * df['R_1_C']) / (df['MA_volume'] + 1e-8)

    # 计算TermB
    df['high_minus_close'] = df['high'] - df['close']
    df['R_HC'] = df.groupby(['trade_date', 'time'])['high_minus_close'].transform(lambda x: x.rank(pct=True))

    # 计算MA_high
    df['MA_high'] = df.groupby('symbol')['high'].transform(
        lambda x: x.rolling(window=w_ma_high, min_periods=w_ma_high).mean()
    )

    # TermB计算
    df['TermB'] = (df['high'] * df['R_HC']) / (df['MA_high'] + 1e-8)

    # 计算TermC
    df['VWAP_shifted'] = df.groupby('symbol')['vwap'].shift(5)
    df['VWAP_diff'] = df['vwap'] - df['VWAP_shifted']
    df['R_VWAP_diff'] = df.groupby(['trade_date', 'time'])['VWAP_diff'].transform(lambda x: x.rank(pct=True))

    # TermC计算
    df['TermC'] = df['R_VWAP_diff']

    # 应用tanh变换
    df['TermA_tanh'] = np.tanh(df['TermA'])
    df['TermB_tanh'] = np.tanh(df['TermB'])
    df['TermC_tanh'] = np.tanh(df['TermC'])

    # 组合最终因子
    df['factor'] = df['TermA_tanh'] * df['TermB_tanh'] - df['TermC_tanh']

    # 处理无效值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

