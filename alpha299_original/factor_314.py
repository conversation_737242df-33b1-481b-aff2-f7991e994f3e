# Alpha299因子 - factor_314
# 原始因子编号: 314
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_314(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha82因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest', 'industry_name']
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 用于替换计算的基础数据列，此处设为None
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 5,                # ts_rank窗口
        'n2': 6,                # ts_cov窗口
        'zscore_window': 20     # 标准化窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    zscore_window = window_sizes['zscore_window']

    df = data_df.copy()
    
    # 确保数据按symbol和time排序
    df = df.sort_values(by=['symbol', 'time'])
    
    # 计算vwap（假设数据中没有现成的vwap列）
    # 保护分母不为0
    df['vwap'] = df.groupby('symbol').apply(
        lambda group: (group['close'] * group['volume']).cumsum() / (group['volume'].cumsum() + 1e-8)
    ).reset_index(level=0, drop=True)
    
    # Step 1-2: 计算标准化后的low和volume
    # 保护分母不为0
    df['low_z'] = df.groupby('symbol')['low'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) 
        / (x.rolling(window=zscore_window, min_periods=w).std(ddof=1) + 1e-8)
    )
    df['volume_z'] = df.groupby('symbol')['volume'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) 
        / (x.rolling(window=zscore_window, min_periods=w).std(ddof=1) + 1e-8)
    )
    
    # Step 3: gp_max(low_z, volume_z)
    df['T1'] = df[['low_z', 'volume_z']].max(axis=1)
    
    # Step 4: ts_rank(T1, 5)
    # rankdata的nan_policy='omit'已经处理了NaN，但需要确保窗口长度足够
    df['X1'] = df.groupby('symbol')['T1'].transform(
        lambda x: x.rolling(window=n1, min_periods=w).apply(
            lambda y: (rankdata(-y, method='max', nan_policy='omit')[-1] / n1) if len(y) >= n1 and not np.all(np.isnan(y)) else np.nan
        )
    )
    
    # Step 5: rank(volume_z) (截面排名)
    # rankdata的nan_policy='omit'已经处理了NaN
    df['T2'] = df.groupby('time')['volume_z'].transform(
        lambda x: rankdata(x, method='average', nan_policy='omit')
    )
    
    # Step 6-7: high_z = ts_zscore(high, 20) 和 T3 = high_z * volume_z
    # 保护分母不为0
    df['high_z'] = df.groupby('symbol')['high'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) 
        / (x.rolling(window=zscore_window, min_periods=w).std(ddof=1) + 1e-8)
    )
    df['T3'] = df['high_z'] * df['volume_z']
    
    # Step 8: T4 = T2 / T3
    # 保护分母不为0
    df['T4'] = df['T2'] / (df['T3'] + 1e-8)
    
    # Step 9-10: vwap_z和close_z
    # 保护分母不为0
    df['vwap_z'] = df.groupby('symbol')['vwap'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) 
        / (x.rolling(window=zscore_window, min_periods=w).std(ddof=1) + 1e-8)
    )
    df['close_z'] = df.groupby('symbol')['close'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) 
        / (x.rolling(window=zscore_window, min_periods=w).std(ddof=1) + 1e-8)
    )
    
    # Step 11: T5 = vwap_z + close_z
    df['T5'] = df['vwap_z'] + df['close_z']
    
    # Step 12: X2 = gp_max(T4, T5)
    df['X2'] = df[['T4', 'T5']].max(axis=1)
    
    # Step 13: ts_cov(6, X1, X2)
    # 使用apply直接计算X1和X2的滚动协方差
    # pandas的.cov会自动处理NaN，但如果窗口内数据不足或常数，结果可能为NaN，这里不额外处理
    df['factor'] = df.groupby('symbol').apply(
        lambda group: group['X1'].rolling(window=n2, min_periods=w).cov(group['X2'])
    ).reset_index(level=0, drop=True)
    
    # Step 14: 替换无穷大值为NaN
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], np.nan)
    
    # 保持日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 选择输出列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    return output_df

