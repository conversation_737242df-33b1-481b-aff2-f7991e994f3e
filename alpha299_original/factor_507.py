# Alpha299因子 - factor_507
# 原始因子编号: 507
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_507(data_df, w: int | None = 14, uni_col: str | None = None):
    """
    计算正向趋向移动 (Plus Directional Movement, +DM) 因子

    参数:
    data_df: DataFrame - 输入数据
    w: int - 基准参数，单位为天，代表因子内部最小窗口期。默认为14天（Wilder推荐的周期）。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: str | None - 本因子不使用单一列，设为None

    返回:
    DataFrame - 包含 ['trade_date', 'time', 'symbol', 'factor'] 的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'wilder_window': 14  # Wilder平滑窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    wilder_window = window_sizes['wilder_window']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组并排序
    df = df.sort_values(['symbol', 'time'])

    # 计算单周期的向上移动值和向下移动值
    df['prev_high'] = df.groupby('symbol')['high'].shift(1)
    df['prev_low'] = df.groupby('symbol')['low'].shift(1)

    df['UpMove'] = df['high'] - df['prev_high']
    df['DownMove'] = df['prev_low'] - df['low']

    # 计算单周期的正向趋向移动 (+DM1)
    df['DM1'] = np.where(
        (df['UpMove'] > df['DownMove']) & (df['UpMove'] > 0),
        df['UpMove'],
        0
    )

    # 初始化因子列
    df['factor'] = np.nan

    # 对每个symbol单独计算Wilder平滑
    for symbol, group in df.groupby('symbol'):
        # 获取DM1序列
        dm1_series = group['DM1'].values
        n = len(dm1_series)

        # 初始化结果数组
        result = np.full(n, np.nan)

        # 需要至少wilder_window个数据点
        if n >= wilder_window:
            # 计算第一个平滑值（前wilder_window个值的和）
            # 确保求和结果不是nan或inf
            first_sum = np.sum(dm1_series[:wilder_window])
            if np.isfinite(first_sum):
                result[wilder_window-1] = first_sum

                # 使用递归公式计算后续值
                for i in range(wilder_window, n):
                    # 确保前一个结果和当前DM1值是有限的
                    if np.isfinite(result[i-1]) and np.isfinite(dm1_series[i]):
                        # 确保除数不为0，虽然这里wilder_window是整数，但为了通用性考虑
                        result[i] = result[i-1] - (result[i-1] / (wilder_window + 1e-8)) + dm1_series[i]
                    else:
                        # 如果遇到非有限值，则后续结果也设为nan
                        result[i] = np.nan
        # 将结果赋值回DataFrame
        df.loc[group.index, 'factor'] = result

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

