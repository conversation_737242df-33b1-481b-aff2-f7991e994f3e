# Alpha299因子 - factor_96
# 原始因子编号: 96
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_96(data_df, w: int | None = 3, uni_col: str | None = None):
    # 三段式混合模型窗口配置
    window_configs = {
        'ma_40_window': 40,     # MA(Volume, 40)
        'corr_9_window': 9,     # 9期滚动相关系数
        'decay_10_window': 10,  # 10期线性衰减
        'zscore_20_window': 20, # 20期Z-score
        'corr_7_window': 7,     # 7期滚动相关系数
        'decay_3_window': 3,    # 3期线性衰减
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        ma_40_window = window_sizes.loc[w, 'ma_40_window']
        corr_9_window = window_sizes.loc[w, 'corr_9_window']
        decay_10_window = window_sizes.loc[w, 'decay_10_window']
        zscore_20_window = window_sizes.loc[w, 'zscore_20_window']
        corr_7_window = window_sizes.loc[w, 'corr_7_window']
        decay_3_window = window_sizes.loc[w, 'decay_3_window']
    else:
        ma_40_window = window_configs['ma_40_window']
        corr_9_window = window_configs['corr_9_window']
        decay_10_window = window_configs['decay_10_window']
        zscore_20_window = window_configs['zscore_20_window']
        corr_7_window = window_configs['corr_7_window']
        decay_3_window = window_configs['decay_3_window']
    
    df = data_df.copy()

    # Step 1: Calculate Mid_t
    df['Mid_t'] = (df['high'] + df['low']) / 2

    # Step 2: Compute 40-day MA of Volume
    df['MA_40_volume'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(ma_40_window).mean())

    # Step 3: Compute 9-period rolling correlation between Mid_t and MA_40_volume
    def group_corr(g):
        # Handle potential constant series or NaNs/Infs in rolling window
        corr_result = g['Mid_t'].rolling(corr_9_window).corr(g['MA_40_volume'])
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)
    df['Corr_1'] = df.groupby('symbol').apply(group_corr).reset_index(level=0, drop=True)

    # Step 4: Apply 10-period linear decay
    def decay_linear(s, window):
        weights = np.arange(1, window + 1)
        # Handle potential NaNs in the rolling window before applying dot product
        return s.rolling(window).apply(lambda x: np.dot(np.nan_to_num(x, 0), weights) / (weights.sum() + 1e-8), raw=True)
    df['DL_1'] = df.groupby('symbol')['Corr_1'].transform(lambda x: decay_linear(x, decay_10_window))

    # Step 5: Cross-sectional rank of DL_1
    df['R_1'] = df.groupby('time')['DL_1'].transform(lambda x: x.rank(pct=True))

    # Step 6: Compute 20-period z-score of VWAP and Volume
    # Calculate VWAP from amount and volume
    df['VWAP_t'] = df['amount'] / (df['volume'] + 1e-8)
    df['StdVWAP_t'] = df.groupby('symbol')['VWAP_t'].transform(lambda x: (x - x.rolling(zscore_20_window).mean()) / (x.rolling(zscore_20_window).std() + 1e-8))
    df['StdVol_t'] = df.groupby('symbol')['volume'].transform(lambda x: (x - x.rolling(zscore_20_window).mean()) / (x.rolling(zscore_20_window).std() + 1e-8))

    # Step 7: Cross-sectional rank of z-scores
    df['RankStdVWAP_t'] = df.groupby('time')['StdVWAP_t'].transform(lambda x: x.rank(pct=True))
    df['RankStdVol_t'] = df.groupby('time')['StdVol_t'].transform(lambda x: x.rank(pct=True))

    # Step 8: Compute 7-period rolling correlation between RankStdVWAP_t and RankStdVol_t
    def group_corr_2(g):
        # Handle potential constant series or NaNs/Infs in rolling window
        corr_result = g['RankStdVWAP_t'].rolling(corr_7_window).corr(g['RankStdVol_t'])
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)
    df['Corr_2'] = df.groupby('symbol').apply(group_corr_2).reset_index(level=0, drop=True)

    # Step 9: Apply 3-period linear decay
    df['DL_2'] = df.groupby('symbol')['Corr_2'].transform(lambda x: decay_linear(x, decay_3_window))

    # Step 10: Cross-sectional rank of DL_2
    df['R_2'] = df.groupby('time')['DL_2'].transform(lambda x: x.rank(pct=True))

    # Step 11: Compute final factor
    df['factor'] = df['R_1'] / (df['R_2'] + 1e-8)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # Step 12: Restore trade_date and time to string format
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # Output the required columns
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

