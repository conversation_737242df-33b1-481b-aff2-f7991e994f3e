# Alpha299因子 - factor_298
# 原始因子编号: 298
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_298(data_df, w: int | None = 2, uni_col: str | None = None):
    """
    计算Alpha55因子，处理局部基准和量纲混加问题
    参数：
        data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为2天（delay_amount_2窗口）。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 本因子不依赖单一基础列，故设为None
    返回：
        - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'w2': 2,   # delay_amount_2窗口
        'w5': 5,   # delay_vwap_5窗口
        'w7': 7,   # ts_regres窗口
        'w10': 10, # ts_zscore窗口
        'w13': 13, # ts_pctchg窗口
        'w20': 20  # volume_zscore_20窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    w2 = window_sizes['w2']
    w5 = window_sizes['w5']
    w7 = window_sizes['w7']
    w10 = window_sizes['w10']
    w13 = window_sizes['w13']
    w20 = window_sizes['w20']

    df = data_df.copy()

    # 先计算vwap（因为输入数据中没有该字段）
    df['vwap'] = df['amount'] / (df['volume'].replace(0, 1e-8) + 1e-8)

    # 1. 计算delay(amount, 2)
    df['delay_amount_2'] = df.groupby('symbol')['amount'].shift(w2)

    # 2. 计算log(delay_amount_2)
    df['log_delay_amount'] = np.log(np.abs(df['delay_amount_2']) + 1e-8)

    # 3. 计算delay(vwap, 5)
    df['delay_vwap_5'] = df.groupby('symbol')['vwap'].shift(w5)

    # 4. 计算ts_zscore(volume, 20)
    def rolling_zscore(x, window):
        # 增加对常数序列的处理
        std_val = x.rolling(window=window, min_periods=w).std()
        mean_val = x.rolling(window=window, min_periods=w).mean()
        # 避免除以0，如果std为0，zscore为0
        return (x - mean_val) / (std_val + 1e-8)

    df['volume_zscore_20'] = df.groupby('symbol')['volume'].transform(lambda x: rolling_zscore(x, w20))
    # 处理zscore可能产生的inf/nan
    df['volume_zscore_20'] = df['volume_zscore_20'].replace([np.inf, -np.inf], np.nan)

    # 5. 计算rank(volume_zscore_20)
    df['volume_rank'] = df.groupby('trade_date')['volume_zscore_20'].transform(
        lambda x: rankdata(x, method='average', nan_policy='omit') / (len(x) + 1e-8)
    )

    # 6. 计算ts_zscore(volume_rank, 10)
    df['volume_rank_zscore'] = df.groupby('symbol')['volume_rank'].transform(
        lambda x: rolling_zscore(x, w10)
    )
    # 处理zscore可能产生的inf/nan
    df['volume_rank_zscore'] = df['volume_rank_zscore'].replace([np.inf, -np.inf], np.nan)

    # 7. 计算ts_zscore(close, 10)
    df['close_zscore'] = df.groupby('symbol')['close'].transform(
        lambda x: rolling_zscore(x, w10)
    )
    # 处理zscore可能产生的inf/nan
    df['close_zscore'] = df['close_zscore'].replace([np.inf, -np.inf], np.nan)

    # 8. 计算add(volume_rank_zscore, close_zscore)
    df['add_result'] = df['volume_rank_zscore'] + df['close_zscore']

    # 9. 计算ts_regres(delay_vwap_5, add_result, 7)
    def rolling_regression(X, Y, window):
        result = np.nan * np.zeros(len(X))
        for i in range(window, len(X)):
            X_win = X.iloc[i-window:i].values
            Y_win = Y.iloc[i-window:i].values

            # 过滤掉窗口内的nan值
            valid_indices = ~np.isnan(X_win) & ~np.isnan(Y_win)
            X_win_valid = X_win[valid_indices]
            Y_win_valid = Y_win[valid_indices]

            # 确保窗口内有足够的数据进行回归
            if len(X_win_valid) < window:
                result[i] = np.nan
                continue

            # 手动计算线性回归系数
            X_mean = np.mean(X_win_valid)
            Y_mean = np.mean(Y_win_valid)
            numerator = np.sum((X_win_valid - X_mean) * (Y_win_valid - Y_mean))
            denominator = np.sum((X_win_valid - X_mean)**2)

            # 避免除以0
            beta = numerator / (denominator + 1e-8) if denominator != 0 else np.nan

            # 计算残差（使用最后时刻的值，如果最后时刻的值有效）
            if not np.isnan(beta) and not np.isnan(X.iloc[i-1]) and not np.isnan(Y.iloc[i-1]):
                result[i] = Y.iloc[i-1] - beta * X.iloc[i-1]
            else:
                result[i] = np.nan

        return pd.Series(result, index=X.index)

    df['vwap_add_regres'] = df.groupby('symbol').apply(
        lambda g: rolling_regression(g['delay_vwap_5'], g['add_result'], w7)
    ).reset_index(level=0, drop=True)
    # 处理回归残差可能产生的inf/nan
    df['vwap_add_regres'] = df['vwap_add_regres'].replace([np.inf, -np.inf], np.nan)

    # 10. 计算ts_zscore(log_delay_amount, 10)
    df['log_amount_zscore'] = df.groupby('symbol')['log_delay_amount'].transform(
        lambda x: rolling_zscore(x, w10)
    )
    # 处理zscore可能产生的inf/nan
    df['log_amount_zscore'] = df['log_amount_zscore'].replace([np.inf, -np.inf], np.nan)

    # 11. 计算ts_zscore(vwap_add_regres, 10)
    df['regres_zscore'] = df.groupby('symbol')['vwap_add_regres'].transform(
        lambda x: rolling_zscore(x, w10)
    )
    # 处理zscore可能产生的inf/nan
    df['regres_zscore'] = df['regres_zscore'].replace([np.inf, -np.inf], np.nan)

    # 12. 计算gp_min(log_amount_zscore, regres_zscore)
    df['min_result'] = df[['log_amount_zscore', 'regres_zscore']].min(axis=1)

    # 13. 计算ts_pctchg(min_result, 13)
    df['factor'] = df.groupby('symbol')['min_result'].transform(
        lambda x: (x - x.shift(w13)) / (x.shift(w13) + 1e-8)
    )

    # 14. 处理无穷大值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 格式化日期时间字段
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

