# Alpha299因子 - factor_114
# 原始因子编号: 114
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_114(data_df, w: int | None = None, uni_col: str | None = 'close'):
    """
    计算Alpha 158因子：价格振幅与收盘价比率因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列['high', 'low', 'close']
        w (int | None): 无时间窗口参数，设为None
        uni_col (str | None): 使用'close'作为基础列
        
    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 检查必要列是否存在
    required_cols = ['high', 'low', 'close']
    for col in required_cols:
        if col not in data_df.columns:
            raise ValueError(f"数据中缺少必要列: {col}")
    
    df = data_df.copy()
    
    # 计算价格振幅与收盘价比率，对分母添加微小值避免除以零
    df['factor'] = (df['high'] - df['low']) / (df['close'] + 1e-8)
    
    # 处理除以零和无穷大情况
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    
    # 严格遵循日期时间格式要求
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 选择输出列并删除无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    return result_df

