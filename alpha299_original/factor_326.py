# Alpha299因子 - factor_326
# 原始因子编号: 326
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_326(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha107因子

    参数:
    - data_df: 输入数据DataFrame
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 不适用于此因子，设为None

    返回:
    - 包含trade_date, time, symbol和factor列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window_zscore': 20,    # 标准化窗口
        'window_corr': 15,      # 相关系数窗口
        'window_pctchg': 8      # 百分比变化窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window_zscore = window_sizes['window_zscore']
    window_corr = window_sizes['window_corr']
    window_pctchg = window_sizes['window_pctchg']

    df = data_df.copy()

    # 确保数据包含必要的列
    required_cols = ['symbol', 'trade_date', 'time', 'low', 'high', 'volume', 'amount']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"输入数据缺少必要的列: {col}")

    # 计算vwap
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 按symbol分组进行计算
    result_dfs = []
    for symbol, group in df.groupby('symbol'):
        # 确保时间序列有序
        group = group.sort_values('time')

        # 1. 取成交量和最高价中逐元素的较大值: T1 = gp_max(volume, high)
        group['T1'] = np.maximum(group['volume'], group['high'])

        # 2. 对最低价进行window_zscore周期滚动标准化: Z1 = ts_zscore(low, window_zscore)
        group['Z1'] = (group['low'] - group['low'].rolling(window=window_zscore, min_periods=w).mean()) / \
                      (group['low'].rolling(window=window_zscore, min_periods=w).std() + 1e-8)

        # 3. 对T1进行window_zscore周期滚动标准化: Z2 = ts_zscore(T1, window_zscore)
        group['Z2'] = (group['T1'] - group['T1'].rolling(window=window_zscore, min_periods=w).mean()) / \
                      (group['T1'].rolling(window=window_zscore, min_periods=w).std() + 1e-8)

        # 4. 计算Z1与Z2的和: X1 = add(Z1, Z2)
        group['X1'] = group['Z1'] + group['Z2']

        # 5. 计算vwap的绝对值的自然对数: T2 = log(vwap)
        group['T2'] = np.log(np.abs(group['vwap']) + 1e-8)

        # 6. 计算T2在过去window_pctchg个周期内的百分比变化率: X2 = ts_pctchg(T2, window_pctchg)
        group['X2'] = group['T2'].pct_change(periods=window_pctchg)

        # 7. 计算X1和X2在过去window_corr个周期内的滚动相关系数得到Alpha107
        # 使用滚动相关系数，min_periods=w
        group['factor'] = group['X1'].rolling(window=window_corr, min_periods=w).corr(group['X2'])

        # 8. 将结果中的无穷大值替换为NaN，并将NaN替换为0，因为corr为常数时结果为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan).fillna(0)

        result_dfs.append(group[['trade_date', 'time', 'symbol', 'factor']])

    # 合并所有结果
    result_df = pd.concat(result_dfs)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = pd.to_datetime(result_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = pd.to_datetime(result_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df

