# Alpha299因子 - factor_220
# 原始因子编号: 220
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_220(data_df, w: int | None = 9, uni_col: str | None = None):
    """
    计算Alpha138因子：基于open差分Z-score和low-volume相关性的复合因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n_1': 9,    # delta和zscore窗口
        'n_2': 12    # ts_corr窗口 (4/3 * 9)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n_1 = window_sizes['n_1']  # delta的窗口
    n_2 = window_sizes['n_2']  # ts_corr的窗口

    df = data_df.copy()

    # 1. 计算delta(open_price, n_1)
    df['delta_open'] = df.groupby('symbol')['open'].transform(lambda x: x - x.shift(n_1))

    # 2. 计算ts_zscore(delta_open, n_1)
    def rolling_zscore(x, window):
        # 增加对常数序列的保护，避免std为0
        std_val = x.rolling(window, min_periods=w).std()
        mean_val = x.rolling(window, min_periods=w).mean()
        # 当std接近0时，zscore为0
        return (x - mean_val) / (std_val + 1e-8)

    df['ts_zscore_delta'] = df.groupby('symbol')['delta_open'].transform(lambda x: rolling_zscore(x, n_1))

    # 3. 计算log(volume)
    # 对volume加一个微小值，避免log(0)
    df['log_volume'] = np.log(df['volume'] + 1e-8)

    # 4. 计算ts_corr(n_2, low, volume)
    # 使用apply处理滚动相关性，并在计算后处理NaN和inf
    df['ts_corr'] = df.groupby('symbol').apply(
        lambda g: g['low'].rolling(n_2, min_periods=w).corr(g['volume'])
    ).reset_index(level=0, drop=True)

    # 对ts_corr的结果进行后处理，将NaN和inf替换为0
    df['ts_corr'] = df['ts_corr'].fillna(0).replace([np.inf, -np.inf], 0)

    # 5. 取log_volume和ts_corr的较大值
    df['gp_max'] = df[['log_volume', 'ts_corr']].max(axis=1)

    # 6. 取负数
    df['neg_gp_max'] = -df['gp_max']

    # 7. 最终因子计算
    df['factor'] = df['ts_zscore_delta'] - df['neg_gp_max']

    # 8. 替换无穷大值为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 9. 恢复日期和时间格式（修正astype错误）
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S')

    # 10. 选择输出列并去除NaN
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

