# Alpha299因子 - factor_39
# 原始因子编号: 39
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_39(data_df, w: int | None = 7, uni_col: str | None = 'close',
              alpha: float = 1.0, w_max: float = 300.0, lambda_rate: float = 0.1):
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 7,      # 差分窗口
        'rank_window': 60,      # 排名窗口
        'adv_window': 20        # ADV窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        nonlocal w_max, lambda_rate, alpha
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']     # 差分窗口
    rank_window = window_sizes['rank_window']       # 排名窗口
    adv_window = window_sizes['adv_window']         # ADV窗口

    """
    计算factor_39因子，使用动态窗口系统

    参数:
        data_df (pd.DataFrame): 输入数据，包含所需列
        w (int | None): 基础窗口大小 (默认7)
        uni_col (str | None): 用于计算的价格列 (默认'close')
        alpha: 动态窗口的非线性调整参数（默认1.0）
        w_max: 动态窗口的绝对上限（默认300.0）
        lambda_rate: 动态窗口的增长率（默认0.1）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """

    df = data_df.copy()
    df = df.sort_values(['symbol', 'time'])

    # 计算ADV（使用动态窗口）
    df['adv'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=adv_window, min_periods=w).mean())

    # 计算delta（使用动态窗口进行差分）
    df['delta'] = df.groupby('symbol')[uni_col].transform(lambda x: x.diff(delta_window))

    # 计算abs_delta
    df['abs_delta'] = df['delta'].abs()

    # 计算ts_rank（使用动态窗口进行rolling rank）
    # 这里的rankdata函数本身可以处理nan，但为了更稳健，可以考虑在apply前对数据进行预处理，
    # 但考虑到rankdata的nan_policy='omit'已经处理了nan，这里不做额外处理。
    df['ts_rank'] = df.groupby('symbol')['abs_delta'].transform(
        lambda x: x.rolling(window=rank_window, min_periods=w).apply(
            lambda y: rankdata(y, method='average', nan_policy='omit')[-1] if len(y) > 0 and not np.all(np.isnan(y)) else np.nan
        )
    )

    # 计算sign(delta)
    df['sign'] = np.sign(df['delta'])

    # 计算factor
    # 确保volume和adv不是nan或inf，虽然rolling mean本身会处理nan，但为了安全起见
    condition = (df['volume'].fillna(0) > df['adv'].fillna(0)) & (~df['volume'].isna()) & (~df['adv'].isna())
    # 确保ts_rank和sign不是nan或inf
    df['factor'] = np.where(condition, -df['ts_rank'].fillna(0) * df['sign'].fillna(0), -1)

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列，并dropna
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

