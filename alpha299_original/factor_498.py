# Alpha299因子 - factor_498
# 原始因子编号: 498
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_498(data_df, w: int | None = 14, uni_col: str | None = None):
    """
    计算下降方向线 (Minus Directional Indicator, -DI)

    参数:
    data_df: 输入的DataFrame，包含价格数据
    w: 时间周期参数，默认为14。该参数将作为最小窗口，所有平滑窗口将根据三段式混合模型动态调整。
    uni_col: 此因子不使用单一基础列，设为None

    返回:
    包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'wilder_window': 14
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    wilder_window = window_sizes['wilder_window']

    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组并按时间排序
    df = df.sort_values(['symbol', 'time'])

    # 计算单周期真实波幅 (TR1) 和单周期下降动向值 (-DM1)
    groups = []

    for symbol, group in df.groupby('symbol'):
        # 计算前一天的价格
        group['prev_high'] = group['high'].shift(1)
        group['prev_low'] = group['low'].shift(1)
        group['prev_close'] = group['close'].shift(1)

        # 计算单周期真实波幅 (TR1)
        group['tr1'] = np.maximum(
            group['high'] - group['low'],
            np.maximum(
                np.abs(group['high'] - group['prev_close']),
                np.abs(group['low'] - group['prev_close'])
            )
        )

        # 计算向上和向下变化量
        group['up_move'] = group['high'] - group['prev_high']
        group['down_move'] = group['prev_low'] - group['low']

        # 计算单周期下降动向值 (-DM1)
        mask = (group['down_move'] > 0) & (group['down_move'] > group['up_move'])
        group['minus_dm1'] = np.where(mask, group['down_move'], 0)

        groups.append(group)

    df = pd.concat(groups)

    # 计算N周期平滑下降动向值 (-DMN) 和N周期平滑真实波幅 (TRN)
    result_groups = []

    for symbol, group in df.groupby('symbol'):
        if wilder_window <= 1:
            # 特殊情况：当窗口<=1时
            group['minus_di'] = group['down_move'] / (group['tr1'].replace(0, 1e-8) + 1e-8)
        else:
            group = group.reset_index(drop=True)
            if len(group) < wilder_window + 1:
                group['minus_di'] = np.nan
                result_groups.append(group)
                continue
            group['minus_dmn'] = np.nan
            group['trn'] = np.nan
            if wilder_window > 1:
                init_minus_dm_sum = group['minus_dm1'].iloc[1:wilder_window].sum()
                init_tr_sum = group['tr1'].iloc[1:wilder_window].sum()
            else:
                init_minus_dm_sum = 0
                init_tr_sum = 0
            for i in range(wilder_window, len(group)):
                if i == wilder_window:
                    minus_dmn = init_minus_dm_sum + group['minus_dm1'].iloc[i]
                    trn = init_tr_sum + group['tr1'].iloc[i]
                else:
                    minus_dmn = group.loc[i-1, 'minus_dmn'] - (group.loc[i-1, 'minus_dmn'] / (wilder_window + 1e-8)) + group['minus_dm1'].iloc[i]
                    trn = group.loc[i-1, 'trn'] - (group.loc[i-1, 'trn'] / (wilder_window + 1e-8)) + group['tr1'].iloc[i]
                group.loc[i, 'minus_dmn'] = minus_dmn
                group.loc[i, 'trn'] = trn
            group['minus_di'] = (group['minus_dmn'] / (group['trn'].replace(0, 1e-8) + 1e-8)) * 100
        result_groups.append(group)

    df = pd.concat(result_groups)

    df['factor'] = df['minus_di']

    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    result_df = df[['trade_date', 'time', 'symbol', 'factor']]
    result_df['factor'] = result_df['factor'].replace([np.inf, -np.inf], np.nan)
    result_df = result_df.dropna()

    return result_df

