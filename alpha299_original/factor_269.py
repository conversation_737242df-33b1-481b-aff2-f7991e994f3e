# Alpha299因子 - factor_269
# 原始因子编号: 269
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_269(data_df, w: int | None = 5, max_window: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha217因子：TS_MAX(TS_CORR(16, VOLUME, VWAP), 5)
    参数:
        data_df: 输入数据DataFrame
        w: TS_CORR的周期 (默认16)
        max_window: TS_MAX的周期 (默认5)
        uni_col: 本因子不涉及单一基础列，因此设为None
    返回:
        包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 16,       # TS_CORR计算窗口
        'max_window': 5          # TS_MAX计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']      # TS_CORR计算窗口
    max_window = window_sizes['max_window']        # TS_MAX计算窗口

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'volume', 'amount']
    missing_cols = [col for col in required_cols if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    df = data_df.copy()

    # 按symbol分组，按时间排序
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 计算VWAP（成交量加权平均价）
    df['cum_amount'] = df.groupby('symbol')['amount'].transform(lambda x: x.cumsum())
    # 确保cum_volume不为0，避免除以0
    df['cum_volume'] = df.groupby('symbol')['volume'].transform(lambda x: x.cumsum())
    df['VWAP'] = df['cum_amount'] / (df['cum_volume'] + 1e-8)

    # 计算TS_CORR(corr_window, VOLUME, VWAP)
    def compute_corr(group):
        # 在计算corr之前，对volume和VWAP进行处理，避免常数序列导致std=0
        # 这里的处理方式是给常数序列添加微小噪声，或者直接返回0
        # 考虑到rolling.corr的内部实现，更稳健的方式是后处理结果
        corr_result = group['volume'].rolling(window=corr_window, min_periods=w).corr(group['VWAP'])
        # 将计算出的NaN或Inf替换为0，因为常数序列的corr没有意义，或者窗口内有NaN/Inf导致无法计算
        corr_result = corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)
        return corr_result

    df['corr'] = df.groupby('symbol').apply(compute_corr).reset_index(level=0, drop=True)

    # 计算TS_MAX(max_window, corr)
    def compute_max(group):
        return group.rolling(window=max_window, min_periods=w).max()

    df['factor'] = df.groupby('symbol')['corr'].apply(compute_max).reset_index(level=0, drop=True)

    # 处理无穷大值
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

