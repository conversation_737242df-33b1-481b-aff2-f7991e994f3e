# Alpha299因子 - factor_205
# 原始因子编号: 205
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_205(data_df, w: int | None = 8, uni_col: str | None = 'volume'):
    """
    计算Alpha 120因子
    参数：
        data_df: 输入DataFrame，包含必要的列
        w: 核心可调参数，对应delta的周期（默认12）
        uni_col: 单一基础列，对应volume（默认'volume')
    返回：
        包含因子值的DataFrame
    """
    # 窗口配置
    window_configs = {
        'n1': 12.0,   # w，delta周期
        'n2': 8.0     # int(8*w/12) = int(8*12/12) = 8，ts_std周期
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']

    # 检查必要列是否存在
    if uni_col not in data_df.columns:
        raise ValueError(f"列 {uni_col} 不存在于输入数据中。")

    # 复制数据以避免修改原始DataFrame
    df = data_df.copy()

    # 第一步：计算成交量绝对值的自然对数
    # 添加对0的处理
    df['log_volume'] = np.log(np.abs(df[uni_col]) + 1e-8)

    # 第二步：计算n1周期的delta（使用实际窗口大小）
    # 按symbol分组计算，使用shift实现差值
    df['delta_log_volume'] = df.groupby('symbol')['log_volume'].transform(
        lambda x: x - x.shift(n1)
    )

    # 第三步：计算n2周期的滚动标准差（使用实际窗口大小）
    # 对滚动标准差的结果进行后处理，避免nan或inf
    df['factor'] = df.groupby('symbol')['delta_log_volume'].transform(
        lambda x: x.rolling(window=n2, min_periods=w).std(ddof=1)
    )

    # 第四步：处理无穷大值和nan值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    # 滚动标准差为0的情况，即窗口内值为常数，此时标准差为0，保持为0即可
    # df['factor'] = df['factor'].fillna(0) # 保持真实的缺值情况，不填充0

    # 转换日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构造输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

