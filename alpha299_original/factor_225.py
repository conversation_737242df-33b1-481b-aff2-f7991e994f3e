# Alpha299因子 - factor_225
# 原始因子编号: 225
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_225(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha146因子
    参数:
        data_df: 输入数据DataFrame
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为15天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一基础数据列参数，此处设为None
    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 9,    # ts_cov窗口
        'mean_window': 15,  # ts_mean窗口
        'std_window': 8,    # ts_std窗口
        'corr_window': 15   # ts_corr窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n1 = window_sizes['cov_window']   # ts_cov窗口
    n2 = window_sizes['mean_window']  # ts_mean窗口
    n3 = window_sizes['std_window']   # ts_std窗口
    n4 = window_sizes['corr_window']  # ts_corr窗口

    # 1. 计算vwap
    # 保护：分母加1e-8防止除以0
    data_df['vwap'] = data_df['amount'] / (data_df['volume'] + 1e-8)

    # 2. 计算ts_cov(n1, vwap, amount)
    def ts_cov(group):
        # 保护：处理可能出现的inf/nan
        cov_result = group['vwap'].rolling(n1, min_periods=w).cov(group['amount'])
        return cov_result.replace([float('inf'), -float('inf')], float('nan'))

    data_df['T1'] = data_df.groupby('symbol').apply(ts_cov).reset_index(level=0, drop=True)

    # 3. 计算ts_mean(n2, low)
    # mean操作本身对nan有一定鲁棒性，但为了后续计算，确保输入没有inf
    data_df['T2'] = data_df.groupby('symbol')['low'].transform(
        lambda x: x.replace([float('inf'), -float('inf')], float('nan')).rolling(n2, min_periods=w).mean()
    )

    # 4. 计算X1 = T1 * T2
    # 乘法可能产生inf/nan，后续处理
    data_df['X1'] = data_df['T1'] * data_df['T2']

    # 5. 计算abs(amount)
    # abs操作不会产生inf/nan，但确保输入没有inf
    data_df['T3'] = data_df['amount'].replace([float('inf'), -float('inf')], float('nan')).abs()

    # 6. 计算ts_std(n3, T3)
    # std操作对常数序列std=0，对nan有一定鲁棒性，但确保输入没有inf
    data_df['X2'] = data_df.groupby('symbol')['T3'].transform(
        lambda x: x.replace([float('inf'), -float('inf')], float('nan')).rolling(n3, min_periods=w).std()
    )
    # 保护：std为0时可能导致后续corr计算问题，此处不直接处理，留给corr函数内部或后处理

    # 7. 计算ts_corr(n4, X1, X2)
    def ts_corr(group):
        # 保护：corr操作在输入为常数序列或包含inf/nan时可能产生nan或inf
        # 确保输入没有inf
        x1_clean = group['X1'].replace([float('inf'), -float('inf')], float('nan'))
        x2_clean = group['X2'].replace([float('inf'), -float('inf')], float('nan'))

        # 计算相关性
        corr_result = x1_clean.rolling(n4, min_periods=w).corr(x2_clean)

        # 保护：将corr出来的结果nan值或者+-inf填0
        # 注意：这里根据要求，corr结果的nan填0，而不是整个因子的nan填0
        corr_result = corr_result.replace([float('inf'), -float('inf')], 0).fillna(0)
        return corr_result

    data_df['factor'] = data_df.groupby('symbol').apply(ts_corr).reset_index(level=0, drop=True)

    # 8. 处理无穷大值 (在corr函数内部已经处理，此处保留作为额外检查)
    data_df['factor'] = data_df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 9. 恢复日期时间格式
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 10. 构建输出DataFrame
    # 最终结果dropna，符合要求
    result_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

