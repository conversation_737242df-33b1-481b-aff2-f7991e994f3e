# Alpha299因子 - factor_147
# 原始因子编号: 147
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_147(data_df, w: int | None = 3, uni_col: str | None = None):
    """
    计算Alpha41因子：VWAP变化最大值的负排名因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数，默认为None（因VWAP计算需要多列）

    返回:
        pd.DataFrame: 包含因子值的结果DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 3,  # 差分窗口
        'rolling_window': 5  # 滚动最大值窗口 (w + 2)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']
    rolling_window = window_sizes['rolling_window']

    # 1. 计算VWAP
    # 增加对volume为0的处理，避免除以零
    data_df['VWAP'] = data_df['amount'] / (data_df['volume'] + 1e-8)

    # 2. 计算差分 (Δ VWAP)
    data_df['Delta_VWAP'] = data_df.groupby('symbol')['VWAP'].transform(
        lambda x: x - x.shift(delta_window)
    )
    # 增加对inf和nan的处理
    data_df['Delta_VWAP'] = data_df['Delta_VWAP'].replace([np.inf, -np.inf], np.nan)

    # 3. 计算滚动最大值 (MaxΔVWAP)
    data_df['Max_Delta_VWAP'] = data_df.groupby('symbol')['Delta_VWAP'].transform(
        lambda x: x.rolling(window=rolling_window, min_periods=w).max()
    )
    # 增加对inf和nan的处理
    data_df['Max_Delta_VWAP'] = data_df['Max_Delta_VWAP'].replace([np.inf, -np.inf], np.nan)

    # 4. 横截面百分比排名
    data_df['cs_rank'] = data_df.groupby(['trade_date', 'time'])['Max_Delta_VWAP'].transform(
        lambda x: x.rank(pct=True)
    )
    # 增加对inf和nan的处理
    data_df['cs_rank'] = data_df['cs_rank'].replace([np.inf, -np.inf], np.nan)

    # 5. 取负并处理无效值
    data_df['factor'] = -data_df['cs_rank']

    # 6. 恢复日期和时间格式
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 7. 返回结果DataFrame
    result_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

