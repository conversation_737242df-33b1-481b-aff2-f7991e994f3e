# Alpha299因子 - factor_391
# 原始因子编号: 391
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_391(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha73因子

    Alpha73 = arctan(mul(ts_std(volume, 14), ts_regbeta(amount, vwap, 10)))

    参数:
    - data_df: 输入数据
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为14天（window2窗口）。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，本因子不使用单一列，设为None

    返回:
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window1': 14,  # 成交量标准差窗口
        'window2': 10   # 回归贝塔系数窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window1 = window_sizes['window1']
    window2 = window_sizes['window2']

    df = data_df.copy()

    # 计算vwap（成交量加权平均价）
    # 保护：volume可能为0，导致除以0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 1. 计算成交量在过去window1个周期内的滚动标准差
    df['vol_std'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=window1, min_periods=w).std()
    )
    # 保护：滚动标准差可能为NaN或0，后续乘法需要注意
    df['vol_std'] = df['vol_std'].fillna(0) # 标准差为0或NaN时，认为波动为0

    # 2. 计算amount对vwap在过去window2个周期内的滚动回归贝塔系数
    def rolling_regbeta(amount_series, vwap_series, window):
        """计算滚动回归贝塔系数"""
        result = pd.Series(index=amount_series.index, dtype=np.float64)

        for i in range(len(amount_series)):
            if i < window - 1:
                # 窗口不足时，至少需要2个点才能计算回归
                if i > 0:
                    x_values = amount_series.iloc[:i+1].values  # amount作为自变量X
                    y_values = vwap_series.iloc[:i+1].values    # vwap作为因变量Y
                    # 保护：确保x值不全相同，且没有NaN或Inf
                    if len(set(x_values)) > 1 and np.isfinite(x_values).all() and np.isfinite(y_values).all():
                        slope, _, _, _, _ = stats.linregress(x_values, y_values)
                        result.iloc[i] = slope
                    else:
                        result.iloc[i] = 0 # 无法计算回归时，beta设为0
            else:
                x_values = amount_series.iloc[i-window+1:i+1].values  # amount作为自变量X
                y_values = vwap_series.iloc[i-window+1:i+1].values    # vwap作为因变量Y
                # 保护：确保x值不全相同，且没有NaN或Inf
                if len(set(x_values)) > 1 and np.isfinite(x_values).all() and np.isfinite(y_values).all():
                    slope, _, _, _, _ = stats.linregress(x_values, y_values)
                    result.iloc[i] = slope
                else:
                    result.iloc[i] = 0 # 无法计算回归时，beta设为0

        return result

    # 按symbol分组计算回归贝塔系数
    beta_results = []
    for symbol, group in df.groupby('symbol'):
        group_sorted = group.sort_values('time')
        group_sorted['beta'] = rolling_regbeta(
            group_sorted['amount'],
            group_sorted['vwap'],
            window2
        )
        beta_results.append(group_sorted)

    df = pd.concat(beta_results, ignore_index=True)

    # 3. 计算vol_std与beta的乘积
    # 保护：vol_std或beta可能为NaN或Inf，乘积可能为NaN或Inf
    df['product'] = df['vol_std'] * df['beta']
    df['product'] = df['product'].replace([np.inf, -np.inf], np.nan) # 乘积为inf时设为nan

    # 4. 计算乘积的反正切值
    # 保护：arctan的输入可以是任意实数，但为了后续处理方便，将NaN保留
    df['factor'] = np.arctan(df['product'])

    # 5. 将无穷大值替换为NaN (冗余保护，arctan不会产生inf)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 日期格式处理
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]
    return result_df

