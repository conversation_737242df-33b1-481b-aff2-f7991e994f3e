# Alpha299因子 - factor_333
# 原始因子编号: 333
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_333(data_df, w: int | None = 9, uni_col: str | None = None):
    """
    计算Alpha118因子

    Args:
        data_df: 输入数据
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 由于该因子使用多个数据列，设为None

    Returns:
        包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 9,   # ts_pctchg窗口
        'n2': 12,  # ts_min窗口
        'n3': 20   # ts_zscore窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期和时间列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按品种分组计算
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 按时间排序
        group = group.sort_values('time')

        # 1. 计算最高价(high)在过去n1个周期内的百分比变化率
        # pct_change本身会处理NaN，但如果high是常数，结果会是NaN，这是正常的
        group['high_pct_change'] = group['high'].pct_change(periods=n1)

        # 2. 计算成交量(volume)在过去n2个周期内的滚动最小值
        # volume可能为0，但min操作不会产生inf/nan，除非窗口内全为nan
        group['volume_min'] = group['volume'].rolling(window=n2, min_periods=w).min()

        # 3. 对volume_min进行过去n3个周期内的滚动Z-score标准化
        def rolling_zscore(x, window):
            # 避免对全NaN或全常数的窗口计算zscore产生NaN/inf
            roll_mean = x.rolling(window=window, min_periods=w).mean()
            roll_std = x.rolling(window=window, min_periods=w).std()
            # 避免除以0，如果标准差为0，说明窗口内是常数，zscore应为0
            zscore = (x - roll_mean) / (roll_std + 1e-8)
            # 如果原始标准差为0，将zscore设为0
            zscore = np.where(roll_std.fillna(0) < 1e-8, 0, zscore)
            return zscore

        group['volume_min_zscore'] = rolling_zscore(group['volume_min'], n3)

        # 4. 取high_pct_change和volume_min_zscore中逐元素的较大值
        # 使用fillna(-np.inf)是为了在比较时将NaN视为最小值，然后恢复NaN
        group['max_value'] = np.maximum(
            group['high_pct_change'].fillna(-np.inf),
            group['volume_min_zscore'].fillna(-np.inf)
        )
        # 将填充的-np.inf恢复为NaN
        group['max_value'] = group['max_value'].replace(-np.inf, np.nan)

        # 5. 计算最低价(low)与max_value的乘积得到Alpha118
        # low或max_value为NaN时，乘积为NaN
        # low为0时，乘积为0
        # low为inf/-inf时，乘积可能为inf/-inf/NaN，需要后续处理
        group['factor'] = group['low'] * group['max_value']

        # 6. 将结果中的无穷大值(inf, -inf)替换为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        # 只保留需要的列
        result_dfs.append(group[['trade_date', 'time', 'symbol', 'factor']])

    # 合并所有结果
    result_df = pd.concat(result_dfs, ignore_index=True)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df

