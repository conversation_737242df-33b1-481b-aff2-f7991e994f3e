# Alpha299因子 - factor_74
# 原始因子编号: 74
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_74(data_df, w: int | None = 7, uni_col: str | None = None):
    # 三段式混合模型窗口配置
    window_configs = {
        'n1': 20,               # 20期求和
        'n2': 40,               # 40期MA
        'corr_window1': 7,      # 7期相关系数
        'corr_window2': 7,      # 6期相关系数
        'vol_window': 20,       # 成交量窗口
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        n1 = window_sizes.loc[w, 'n1']
        n2 = window_sizes.loc[w, 'n2']
        corr_window1 = window_sizes.loc[w, 'corr_window1']
        corr_window2 = window_sizes.loc[w, 'corr_window2']
        vol_window = window_sizes.loc[w, 'vol_window']
    else:
        n1 = window_configs['n1']
        n2 = window_configs['n2']
        corr_window1 = window_configs['corr_window1']
        corr_window2 = window_configs['corr_window2']
        vol_window = window_configs['vol_window']
    
    # 检查必要列是否存在
    required_cols = ['low', 'amount', 'volume']
    for col in required_cols:
        if col not in data_df.columns:
            raise ValueError(f"缺少必要列: {col}")

    # 计算VWAP（Volume Weighted Average Price）
    # 保护分母不为0
    data_df['vwap'] = data_df['amount'] / (data_df['volume'] + 1e-8)

    # 按symbol和time排序
    data_df = data_df.sort_values(by=['symbol', 'time'])

    # 计算混合价格 P_mix
    data_df['P_mix'] = 0.35 * data_df['low'] + 0.65 * data_df['vwap']

    # 参数定义（现在使用动态计算的值）
    # n1 = w  # 20期求和
    # n2 = 2 * w  # 40期MA
    # corr_window1 = int(w / 20 * 7)  # 7期相关系数
    # corr_window2 = int(w / 20 * 7)   # 6期相关系数

    # 计算P_mix的20期滚动求和
    data_df['SumP_mix'] = data_df.groupby('symbol')['P_mix'].transform(
        lambda x: x.rolling(window=n1, min_periods=w).sum()
    )

    # 计算成交量的40期MA
    data_df['MAVol_40'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=n2, min_periods=w).mean()
    )

    # 计算MAVol的20期滚动求和
    data_df['SumMAVol'] = data_df.groupby('symbol')['MAVol_40'].transform(
        lambda x: x.rolling(window=n1, min_periods=w).sum()
    )

    # 计算SumP_mix和SumMAVol的7期滚动相关系数
    def calc_corr(group):
        # 对可能导致corr计算失败的NaN或inf进行处理，这里选择在计算后将NaN或inf填充为0
        corr_result = group['SumP_mix'].rolling(window=corr_window1, min_periods=w).corr(group['SumMAVol'])
        return corr_result.fillna(0).replace([float('inf'), float('-inf')], 0)

    data_df['Corr1'] = data_df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)

    # 对Corr1进行横截面排名
    data_df['R1'] = data_df.groupby(['trade_date', 'time'])['Corr1'].transform(lambda x: x.rank(pct=True))

    # 计算VWAP的横截面排名
    data_df['R_vwap'] = data_df.groupby(['trade_date', 'time'])['vwap'].transform(lambda x: x.rank(pct=True))

    # 计算成交量的20期滚动标准化
    data_df['volume_mean'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=vol_window, min_periods=w).mean()
    )
    data_df['volume_std'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=vol_window, min_periods=w).std()
    )
    # 保护分母不为0
    data_df['StdVol'] = (data_df['volume'] - data_df['volume_mean']) / (data_df['volume_std'] + 1e-8)

    # 对标准化后的成交量进行横截面排名
    data_df['R_stdVol'] = data_df.groupby(['trade_date', 'time'])['StdVol'].transform(lambda x: x.rank(pct=True))

    # 计算R_vwap和R_stdVol的6期滚动相关系数
    def calc_corr2(group):
        # 对可能导致corr计算失败的NaN或inf进行处理，这里选择在计算后将NaN或inf填充为0
        corr_result = group['R_vwap'].rolling(window=corr_window2, min_periods=w).corr(group['R_stdVol'])
        return corr_result.fillna(0).replace([float('inf'), float('-inf')], 0)

    data_df['Corr2'] = data_df.groupby('symbol').apply(calc_corr2).reset_index(level=0, drop=True)

    # 对Corr2进行横截面排名
    data_df['R2'] = data_df.groupby(['trade_date', 'time'])['Corr2'].transform(lambda x: x.rank(pct=True))

    # 最终因子值为R1 + R2
    data_df['factor'] = data_df['R1'] + data_df['R2']

    # 处理±∞为NaN
    data_df['factor'] = data_df['factor'].replace([float('inf'), float('-inf')], float('nan'))

    # 恢复日期和时间格式
    data_df['trade_date'] = data_df['trade_date'].astype('string')
    data_df['time'] = data_df['time'].astype('string')

    # 选择所需列并处理NaN
    result_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

