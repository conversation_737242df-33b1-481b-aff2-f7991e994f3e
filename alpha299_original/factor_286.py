# Alpha299因子 - factor_286
# 原始因子编号: 286
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_286(data_df, w: int | None = 4, uni_col: str | None = None):
    """
    计算Alpha42因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'high', 'volume']等列
        w (int | None): 核心窗口参数，单位为天，默认值4
        uni_col (str | None): 单一基础列参数，此处设为None（因使用high和volume两列）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 4,       # DELTA窗口
        'corr_window': 5         # ts_corr窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']       # DELTA窗口
    corr_window = window_sizes['corr_window']         # ts_corr窗口

    # 检查必要列是否存在
    if not {'high', 'volume'}.issubset(data_df.columns):
        raise ValueError("输入数据必须包含 'high' 和 'volume' 列")

    df = data_df.copy()

    # 计算delta(high, delta_window)
    df['delta_high'] = df.groupby('symbol')['high'].transform(lambda x: x - x.shift(delta_window))

    # 计算neg(volume)
    df['neg_volume'] = -df['volume']

    # 计算滚动相关系数ts_corr(corr_window, delta_high, neg_volume)
    # 使用groupby + apply + rolling.corr实现
    def calc_corr(group):
        # 在计算相关系数前，对可能导致std=0的常数序列进行处理，或者对inf/nan进行处理
        # 这里选择在计算corr后对结果进行处理，将nan或inf填充为0
        corr_result = group['delta_high'].rolling(window=corr_window, min_periods=w).corr(group['neg_volume'])
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    df['factor'] = df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return output_df

