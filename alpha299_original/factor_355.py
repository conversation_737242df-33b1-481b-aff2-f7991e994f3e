# Alpha299因子 - factor_355
# 原始因子编号: 355
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_355(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha192因子
    
    Alpha192 = TS_REGBETA(HIGH, TS_STD(ABS(CLOSE), 8), 18)
    
    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为18天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 由于该因子使用high和close两列，因此设置为None
    
    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'regbeta_window': 18,     # TS_REGBETA窗口
        'std_period': 8           # TS_STD窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    regbeta_window = window_sizes['regbeta_window']
    std_period = window_sizes['std_period']
    
    # 复制数据，避免修改原始数据
    df = data_df.copy()
    
    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')
    
    # 计算绝对值
    df['abs_close'] = df['close'].abs()
    
    # 按symbol分组计算
    result_dfs = []
    
    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values(['trade_date', 'time'])
        
        # 计算TS_STD(ABS(CLOSE), 8)
        group['std_abs_close'] = group['abs_close'].rolling(window=std_period, min_periods=w).std()
        
        # 初始化因子列
        group['factor'] = np.nan
        
        # 计算TS_REGBETA(HIGH, TS_STD(ABS(CLOSE), 8), 18)
        for i in range(regbeta_window-1, len(group)):
            high_values = group['high'].iloc[i-regbeta_window+1:i+1].values
            std_values = group['std_abs_close'].iloc[i-regbeta_window+1:i+1].values
            
            # 排除无效值
            valid_indices = ~(np.isnan(high_values) | np.isnan(std_values))
            if sum(valid_indices) > 1:  # 至少需要两个有效点
                x = high_values[valid_indices]
                y = std_values[valid_indices]
                
                try:
                    # 计算线性回归的斜率（beta系数）
                    # 添加除法保护
                    denominator = np.sum((x - np.mean(x))**2)
                    if denominator < 1e-8: # 避免除以接近0的值
                        slope = np.nan
                    else:
                        slope = np.sum((x - np.mean(x)) * (y - np.mean(y))) / denominator
                    group.iloc[i, group.columns.get_loc('factor')] = slope
                except:
                    # 如果回归计算失败，设为NaN
                    group.iloc[i, group.columns.get_loc('factor')] = np.nan
        
        result_dfs.append(group)
    
    # 合并结果
    df = pd.concat(result_dfs)
    
    # 将无穷大和负无穷大替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    
    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 返回结果DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

