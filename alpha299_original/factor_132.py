# Alpha299因子 - factor_132
# 原始因子编号: 132
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_132(data_df, w: int | None = 6, uni_col: str | None = 'close'):
    """
    计算Alpha_189因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str): 用于计算的单一基础列，默认为'close'

    返回:
        pd.DataFrame: 包含因子计算结果的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'sma_window': 6  # SMA移动平均窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    sma_window = window_sizes['sma_window']

    # 使用副本进行计算以避免修改原始数据
    df = data_df.copy()

    # 检查必要列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'close']
    missing_cols = [col for col in required_columns if col not in df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    # 第一步：计算收盘价的SMA
    # 确保输入到rolling.mean的数据没有inf/-inf
    df[uni_col] = df[uni_col].replace([float('inf'), float('-inf')], pd.NA)
    df['sma'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=sma_window, min_periods=w).mean()
    )

    # 第二步：计算收盘价与SMA的绝对偏差
    df['abs_dev'] = abs(df[uni_col] - df['sma'])

    # 第三步：计算绝对偏差的SMA
    # 确保输入到rolling.mean的数据没有inf/-inf
    df['abs_dev'] = df['abs_dev'].replace([float('inf'), float('-inf')], pd.NA)
    df['factor'] = df.groupby('symbol')['abs_dev'].transform(
        lambda x: x.rolling(window=sma_window, min_periods=w).mean()
    )

    # 第四步：处理无穷大值和NaN值
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], pd.NA)
    # 这里的dropna()在最后一步，所以不需要额外的fillna(0)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame并过滤无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

