# Alpha299因子 - factor_119
# 原始因子编号: 119
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_119(data_df, w: int | None = 12, uni_col: str | None = 'close'):
    """
    计算Alpha 167因子：累积上涨幅度因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'close']等列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为12天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str): 用于计算的基础列，默认值'close'

    返回:
        pd.DataFrame: 包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'rolling_sum_window': 12  # 滚动求和窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    rolling_sum_window = window_sizes['rolling_sum_window']

    df = data_df.copy()

    # 确保基础列存在
    if uni_col not in df.columns:
        raise ValueError(f"缺失必要列 {uni_col}")

    # 按symbol分组计算
    def process_group(group):
        # 将trade_date和time转换为datetime对象
        group['trade_date_dt'] = pd.to_datetime(group['trade_date'])
        group['time_dt'] = pd.to_datetime(group['time'])

        # 排序确保时间序列正确
        group = group.sort_values(['trade_date_dt', 'time_dt'])

        # 计算UpMove
        group['prev_close'] = group[uni_col].shift(1)
        # 添加对prev_close和uni_col中可能存在的inf/nan的保护
        group['UpMove'] = group[uni_col] - group['prev_close']
        group['UpMove'] = group['UpMove'].where(group['UpMove'] > 0, 0)

        # 计算滚动求和
        # 滚动求和本身对inf/nan有一定鲁棒性，但为了确保结果健康，可以在求和前对UpMove进行处理
        group['UpMove'] = group['UpMove'].replace([float('inf'), -float('inf')], np.nan)
        group['UpMove'] = group['UpMove'].fillna(0) # 将NaN填充为0，避免影响求和结果

        group['factor'] = group['UpMove'].rolling(window=rolling_sum_window, min_periods=w).sum()

        # 处理无效值（无穷大和NaN）
        group['factor'] = group['factor'].replace([float('inf'), -float('inf')], np.nan)
        # 保留NaN，不在这里填充0，以便后续dropna处理

        # 保留必要列并恢复格式
        result = group[['trade_date', 'time', 'symbol', 'factor']].copy()
        return result

    # 按symbol分组处理
    result_df = df.groupby('symbol', group_keys=False).apply(process_group)

    # 清理无效值（保留非空值）
    result_df = result_df.dropna(subset=['factor'])

    return result_df

