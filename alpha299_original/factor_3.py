# Alpha299因子 - factor_3
# 原始因子编号: 3
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_3(data_df, w: int | None = 3, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 3,       # DELTA窗口
        'corr_window': 10        # 相关性窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']       # DELTA窗口
    corr_window = window_sizes['corr_window']         # 相关性窗口

    # 按symbol和time排序以确保时间序列正确性
    data_df = data_df.sort_values(by=['symbol', 'time'])

    # 计算RETURNS（假设为close的收益率）
    data_df['RETURNS'] = data_df.groupby('symbol')['close'].pct_change()

    # 计算delta_window期差分
    data_df['delta_RETURNS'] = data_df.groupby('symbol')['RETURNS'].transform(lambda x: x - x.shift(delta_window))

    # 计算delta_RETURNS的滚动窗口排名（替代全局排序）
    data_df['rank_delta_RETURNS'] = data_df.groupby('symbol')['delta_RETURNS'].apply(
        lambda x: x.rolling(window=corr_window).rank()
    ).reset_index(level=0, drop=True) * (-1)

    # 计算OPEN与VOLUME的滚动相关系数（按symbol分组）
    # 处理滚动相关系数可能产生的NaN或inf
    data_df['correlation'] = data_df.groupby('symbol').apply(
        lambda x: x['open'].rolling(window=corr_window).corr(x['volume']).fillna(0).replace([np.inf, -np.inf], 0)
    ).reset_index(level=0, drop=True)

    # 计算最终因子值
    data_df['factor'] = data_df['rank_delta_RETURNS'] * data_df['correlation']

    # 恢复日期和时间格式
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去除无效值
    output_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

