# Alpha299因子 - factor_112
# 原始因子编号: 112
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_112(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha 15隔夜跳空因子
    参数:
        data_df: 输入的DataFrame，包含['symbol', 'trade_date', 'time', 'open', 'close']等列
        w: 本因子不涉及天数参数，故默认为None
        uni_col: 本因子不依赖单一基础数据列，故默认为None
    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    df = data_df.copy()

    # 按symbol分组，对每个品种的时间序列进行处理
    df['close_prev'] = df.groupby('symbol')['close'].shift(1)

    # 计算隔夜跳空因子
    # 避免除以0，对close_prev加一个很小的数
    df['factor'] = (df['open'] / (df['close_prev'] + 1e-8)) - 1

    # 处理除零错误和数值不稳定情况
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去除NaN
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

