# Alpha299因子 - factor_422
# 原始因子编号: 422
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_422(data_df, w: int | None = 14, uni_col: str | None = 'high'):
    """
    计算阿隆上升指标(Aroon Up)

    参数:
    data_df: DataFrame, 包含交易数据
    w: int, 计算阿隆指标的时间窗口长度，默认为14。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    uni_col: str, 用于计算最高价的列，默认为'high'

    返回:
    DataFrame, 包含trade_date, time, symbol和factor列
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'aroon_window': 14
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    aroon_window = window_sizes['aroon_window']

    # 创建数据副本
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组计算阿隆上升指标
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')

        # 计算滚动窗口内最高价的位置
        def rolling_highest_position(series, window=aroon_window):
            result = np.zeros(len(series))
            for i in range(window - 1, len(series)):
                window_data = series.iloc[i-window+1:i+1]
                # 找到窗口内最高价的位置（如果有多个相同的最高价，取最近的一个）
                highest_value = window_data.max()
                # 从后往前找，确保取最近的位置
                position_from_start = -1 # Initialize with a value indicating not found
                for j in range(window-1, -1, -1):
                    # Add a small epsilon to handle potential floating point issues when comparing
                    if abs(window_data.iloc[j] - highest_value) < 1e-9:
                        position_from_start = j
                        break
                # 计算自最高价出现以来经过的周期数
                if position_from_start != -1:
                    periods_since_high = window - 1 - position_from_start
                    result[i] = periods_since_high
                else:
                    # If highest value not found (shouldn't happen with max), default to window-1
                    result[i] = window - 1
            return result

        # 计算每个窗口内最高价出现后经过的周期数
        periods_since_high = rolling_highest_position(group[uni_col])

        # 计算阿隆上升指标
        aroon_up = np.zeros(len(group))
        for i in range(aroon_window - 1, len(group)):
            # Ensure denominator is not zero
            denominator = aroon_window
            if denominator < 1e-8:
                 aroon_up[i] = np.nan # Or some other appropriate value like 0
            else:
                 aroon_up[i] = ((aroon_window - periods_since_high[i]) / denominator) * 100

        # 创建结果DataFrame
        result = group.copy()
        result['factor'] = aroon_up

        # 只保留需要的列
        result = result[['trade_date', 'time', 'symbol', 'factor']]

        # 添加到结果列表
        result_dfs.append(result)

    # 合并所有结果
    if result_dfs:
        result_df = pd.concat(result_dfs)
    else:
        result_df = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    # Restore date format
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # Handle potential NaN or Inf values in the final factor column
    result_df['factor'] = result_df['factor'].replace([np.inf, -np.inf], np.nan)
    result_df = result_df.dropna()

    return result_df

