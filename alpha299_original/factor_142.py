# Alpha299因子 - factor_142
# 原始因子编号: 142
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_142(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算Alpha因子：基于开盘价变化和开盘价-成交量相关性的复合因子
    
    参数:
        data_df (pd.DataFrame): 输入数据
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为15天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列参数（本因子不适用，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'decay_linear_1': 15,  # DecayLinear窗口1
        'correlation_window': 17,  # 相关性窗口
        'decay_linear_2': 7    # DecayLinear窗口2
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    decay_linear_1 = window_sizes['decay_linear_1']
    correlation_window = window_sizes['correlation_window']
    decay_linear_2 = window_sizes['decay_linear_2']

    df = data_df.copy()

    # 计算Delta(OPEN, 1)
    df['delta_open'] = df.groupby('symbol')['open'].transform(lambda x: x.diff(1))

    # 自定义线性衰减加权求和函数
    def linear_decay_weighted_sum(series, N):
        @njit
        def _weighted_sum(arr):
            # 根据实际数组长度创建权重
            actual_length = len(arr)
            if actual_length == 0:
                return np.nan
            weights = np.array([2 * i / (actual_length * (actual_length + 1) + 1e-8) for i in range(1, actual_length + 1)])
            # 手动处理nan和inf
            arr_cleaned = np.where(np.isnan(arr) | np.isinf(arr), 0.0, arr)
            return np.sum(arr_cleaned * weights)
        
        # 使用max(1, w//3)作为最小窗口期，避免过小的窗口
        min_periods_adjusted = max(1, w//3 if w is not None else 1)
        return series.rolling(window=N, min_periods=min_periods_adjusted).apply(_weighted_sum, raw=True).replace([np.inf, -np.inf], np.nan)

    # 计算DL1 = DecayLinear(DELTA(OPEN, 1), decay_linear_1)
    df['DL1'] = df.groupby('symbol')['delta_open'].transform(lambda x: linear_decay_weighted_sum(x, decay_linear_1))

    # R1 = rank(DL1)
    df['R1'] = df.groupby(['trade_date', 'time'])['DL1'].transform(lambda x: x.rank(pct=True))

    # 计算CORR(OPEN, VOLUME, correlation_window)
    df['Corr_OV'] = df.groupby('symbol').apply(
        lambda g: g['open'].rolling(window=correlation_window, min_periods=w).corr(g['volume']).fillna(0).replace([np.inf, -np.inf], 0)
    ).reset_index(level=0, drop=True)

    # 计算DL2 = DecayLinear(Corr_OV, decay_linear_2)
    df['DL2'] = df.groupby('symbol')['Corr_OV'].transform(lambda x: linear_decay_weighted_sum(x, decay_linear_2))

    # R2 = rank(DL2)
    df['R2'] = df.groupby(['trade_date', 'time'])['DL2'].transform(lambda x: x.rank(pct=True))

    # 最终因子计算
    df['factor'] = -df[['R1', 'R2']].min(axis=1)

    # 处理无效值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 格式化日期时间字段
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

