# Alpha299因子 - factor_372
# 原始因子编号: 372
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_372(data_df, w: int | None = 4, uni_col: str | None = 'close'):
    """
    计算Alpha 23因子

    Alpha23 = add(ts_zscore(delta(delta(close, 15), 4), 20), ts_max(rank(amount), 13))

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为15天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 主要使用的价格列，默认为'close'
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delta1_window': 15,    # 第一个delta窗口
        'delta2_window': 4,     # 第二个delta窗口
        'zscore_window': 20,    # zscore窗口
        'tsmax_window': 13      # ts_max窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delta1_window = window_sizes['delta1_window']
    delta2_window = window_sizes['delta2_window']
    zscore_window = window_sizes['zscore_window']
    tsmax_window = window_sizes['tsmax_window']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按照symbol分组计算
    result_dfs = []
    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')

        # 1. 计算收盘价在过去delta1_window个周期内的差值：T1 = delta(close, delta1_window)
        group['T1'] = group[uni_col] - group[uni_col].shift(delta1_window)

        # 2. 计算T1在过去delta2_window个周期内的差值：X1 = delta(T1, delta2_window)
        group['X1'] = group['T1'] - group['T1'].shift(delta2_window)

        # 3. 对X1进行滚动标准化（窗口期zscore_window）：X1' = ts_zscore(X1, zscore_window)
        # 使用更安全的方式计算滚动z-score
        rolling_mean = group['X1'].rolling(window=zscore_window, min_periods=w).mean()
        rolling_std = group['X1'].rolling(window=zscore_window, min_periods=w).std()
        # 避免除以0，并处理inf/nan
        group['X1_zscore'] = (group['X1'] - rolling_mean) / (rolling_std + 1e-8)
        group['X1_zscore'] = group['X1_zscore'].replace([float('inf'), float('-inf')], float('nan'))


        # 4. 计算成交额的截面排名：T2 = rank(amount)
        # 由于我们是按symbol分组处理，无法直接计算截面排名
        # 先保存每个时间点的amount值，后续再统一计算截面排名
        result_dfs.append(group[['trade_date', 'time', 'symbol', 'amount', 'X1_zscore']])

    # 合并所有分组结果
    merged_df = pd.concat(result_dfs)

    # 4. 计算成交额的截面排名：T2 = rank(amount)
    # 对每个时间点的amount进行截面排名
    merged_df['T2'] = merged_df.groupby('time')['amount'].transform(
        lambda x: x.rank(method='average', pct=True)
    )

    # 5. 计算T2在过去tsmax_window个周期内的滚动最大值：X2 = ts_max(T2, tsmax_window)
    # 需要按symbol和时间重新分组计算
    result_dfs = []
    for symbol, group in merged_df.groupby('symbol'):
        group = group.sort_values('time')
        group['X2'] = group['T2'].rolling(window=tsmax_window, min_periods=w).max()

        # 6. 计算X1'与X2的和得到Alpha23：Alpha23 = add(X1', X2)
        group['factor'] = group['X1_zscore'] + group['X2']

        # 7. 将结果中的无穷大值(inf, -inf)替换为NaN
        group['factor'] = group['factor'].replace([float('inf'), float('-inf')], float('nan'))

        result_dfs.append(group[['trade_date', 'time', 'symbol', 'factor']])

    # 合并最终结果
    result_df = pd.concat(result_dfs)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df[['trade_date', 'time', 'symbol', 'factor']]

