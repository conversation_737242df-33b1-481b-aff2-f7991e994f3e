# Alpha299因子 - factor_280
# 原始因子编号: 280
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_280(data_df, w: int | None = 6, uni_col: str | None = 'close'):
    # 定义所有窗口的基准值
    window_configs = {
        'std_window': 6,         # ts_std窗口
        'beta_window': 14        # ts_regbeta窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    std_window = window_sizes['std_window']         # ts_std窗口
    beta_window = window_sizes['beta_window']       # ts_regbeta窗口

    # 参数校验：确保uni_col列存在
    if uni_col not in data_df.columns:
        raise ValueError(f"输入数据中缺少必要的列: {uni_col}。实际列: {data_df.columns.tolist()}")

    # 确保输入为DataFrame并按symbol和time排序
    df = data_df.sort_values(by=['symbol', 'time']).copy()

    # 计算T1 = abs(uni_col)
    df['T1'] = df[uni_col].abs()

    # 计算X1 = ts_std(T1, std_window)
    df['X1'] = df.groupby('symbol')['T1'].transform(lambda x: x.rolling(window=std_window, min_periods=w).std(ddof=1))

    # 使用pandas内置方法计算beta（协方差/方差）
    def calculate_rolling_beta(group):
        # 计算协方差和方差
        # 增加对输入数据的inf/nan处理，避免在计算协方差和方差时出现问题
        group_cleaned = group[[uni_col, 'X1']].replace([float('inf'), -float('inf')], float('nan'))
        group_cleaned = group_cleaned.dropna()

        if group_cleaned.empty or len(group_cleaned) < beta_window:
             return pd.Series(np.nan, index=group.index)

        cov = group_cleaned[uni_col].rolling(window=beta_window, min_periods=w).cov(group_cleaned['X1'])
        var = group_cleaned[uni_col].rolling(window=beta_window, min_periods=w).var(ddof=1)

        # 避免除以零，添加一个小的常数
        beta = cov / (var + 1e-8)

        # 将计算结果对齐回原始索引
        return beta.reindex(group.index)

    # 计算beta
    df['factor'] = df.groupby('symbol').apply(calculate_rolling_beta).droplevel('symbol')

    # 处理无穷大值和NaN值
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))
    # 考虑到beta的计算原理，当var接近0时，beta可能不稳定或无穷大。
    # 将NaN值保留，以便后续处理或分析。

    # 转换日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去除NaN
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna(subset=['factor'])

    return output_df

