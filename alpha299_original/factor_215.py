# Alpha299因子 - factor_215
# 原始因子编号: 215
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_215(
    data_df,
    w: int | None = 6,
    uni_col: str | None = None
):
    """
    计算Alpha132因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
                                 'volume', 'amount', 'open_interest', 'industry_name']
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列（本因子不适用，保留为None）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果
    """
    # 定义所有窗口的基准值
    window_configs = {
        'rank_window': 6,      # ts_rank窗口
        'cov_window': 9,       # ts_cov窗口
        'zscore_window': 20    # ts_zscore窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n1 = window_sizes['rank_window']     # ts_rank窗口
    n2 = window_sizes['cov_window']      # ts_cov窗口
    n3 = window_sizes['zscore_window']   # ts_zscore窗口

    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'low', 'volume', 'high', 'close', 'amount']
    missing_cols = [col for col in required_cols if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"数据缺失必要列: {missing_cols}")

    df = data_df.copy()

    # 按symbol和时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 计算vwap（成交量加权平均价）
    df['vwap'] = df.groupby('symbol')['amount'].cumsum() / (df.groupby('symbol')['volume'].cumsum() + 1e-8)

    # 步骤1: T1 = gp_max(low, volume)
    df['T1'] = df[['low', 'volume']].max(axis=1)

    # 步骤2: X1 = ts_rank(T1, n1)
    def ts_rank(x, window):
        # 避免窗口内常数导致排名问题，虽然rank函数本身处理常数，但为了鲁棒性
        return x.rolling(window=window, min_periods=w).apply(
            lambda s: (s.rank(ascending=False, method='average') / (window + 1e-8)).iloc[-1], raw=False
        )

    df['X1'] = df.groupby('symbol')['T1'].transform(lambda x: ts_rank(x, n1))

    # 步骤3: T2 = ts_zscore(volume, n3)
    def ts_zscore(x, window):
        rolling_mean = x.rolling(window=window, min_periods=w).mean()
        rolling_std = x.rolling(window=window, min_periods=w).std()
        # 避免除以0
        return (x - rolling_mean) / (rolling_std + 1e-8)

    df['T2'] = df.groupby('symbol')['volume'].transform(lambda x: ts_zscore(x, n3))

    # 步骤4: T3 = rank(T2) - 按时间截面排名
    # rank函数本身处理NaN，但为了鲁棒性，可以先填充一个临时值，排名后再恢复
    # 这里直接使用rank，其会跳过NaN
    df['T3'] = df.groupby('time')['T2'].transform(lambda x: x.rank(ascending=True, method='average')).astype('float64')

    # 步骤5: T4 = mul(high, volume)
    df['T4'] = df['high'] * df['volume']

    # 步骤6: T5 = div(T3, T4)
    # 避免除以0
    df['T5'] = df['T3'] / (df['T4'] + 1e-8)

    # 步骤7: T6 = add(vwap, close)
    df['T6'] = df['vwap'] + df['close']

    # 步骤8: X2 = gp_max(T5, T6)
    df['X2'] = df[['T5', 'T6']].max(axis=1)

    # 步骤9: ts_cov(n2, X1, X2)
    def ts_cov(x1, x2, window):
        # rolling().cov()会自动处理NaN，但如果窗口内数据不足或常数，结果可能为NaN
        # 这里不额外处理NaN，保留真实的NaN情况
        return x1.rolling(window=window, min_periods=w).cov(x2)

    # 使用apply处理分组，并确保结果正确对齐
    df['factor'] = df.groupby('symbol').apply(
        lambda group: ts_cov(group['X1'], group['X2'], n2)
    ).reset_index(level=0, drop=True)


    # 步骤10: 处理无穷大值和NaN
    # Rolling().cov()的结果不会产生inf，只会产生NaN。
    # 但为了鲁棒性，保留inf到nan的转换。
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], float('nan'))
    # 不对NaN进行填充，保留真实的缺失情况

    # 格式化日期和时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    # 最终结果只包含非NaN的因子值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna(subset=['factor'])

    return result_df

