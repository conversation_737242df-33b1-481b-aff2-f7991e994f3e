# Alpha299因子 - factor_266
# 原始因子编号: 266
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_266(data_df, w: int | None = 12, uni_col: str | None = None):
    """
    计算Alpha210因子：12周期内VWAP排名与成交金额排名的相关系数

    参数:
    data_df (pd.DataFrame): 输入数据
    w (int): 时间窗口长度（天）
    uni_col (str): 单一基础列（本因子不需要）

    返回:
    pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 12       # 相关系数计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']      # 相关系数计算窗口

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'volume', 'amount']
    if not all(col in data_df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in data_df.columns]
        raise KeyError(f"数据缺少必要列: {missing}")

    df = data_df.copy()

    # 严格转换数值列并验证
    df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
    df['amount'] = pd.to_numeric(df['amount'], errors='coerce')

    # 1. 计算每日VWAP（按交易日计算）
    # 确保分母不为0
    df['volume_sum'] = df.groupby(['symbol', 'trade_date'])['volume'].transform('sum')
    df['amount_sum'] = df.groupby(['symbol', 'trade_date'])['amount'].transform('sum')

    # 使用transform避免apply的性能问题，并处理分母为0的情况
    df['vwap'] = df.apply(
        lambda row: row['amount_sum'] / (row['volume_sum'] + 1e-8) if row['volume_sum'] > 0 else np.nan,
        axis=1
    )

    # 2. 计算截面排名（按交易日）
    df['rank_vwap'] = df.groupby('trade_date')['vwap'].rank(pct=False)
    df['rank_amount'] = df.groupby('trade_date')['amount'].rank(pct=False)

    # 3. 按symbol分组并按时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 4. 计算滚动相关系数（使用pandas内置方法）
    # 对滚动相关系数的结果进行后处理，将nan/inf/neg_inf填充为0
    df['factor'] = df.groupby('symbol').apply(
        lambda x: x['rank_vwap'].rolling(window=corr_window, min_periods=w).corr(x['rank_amount']).fillna(0).replace([np.inf, -np.inf], 0)
    ).reset_index(level=0, drop=True)


    # 5. 替换无效值 (这一步在滚动相关系数计算后已经处理了)
    # df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 6. 格式化日期时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

