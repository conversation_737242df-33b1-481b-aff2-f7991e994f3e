# Alpha299因子 - factor_190
# 原始因子编号: 190
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_190(data_df, w: int | None = 3, uni_col: str | None = 'close'):
    """
    计算Alpha 98因子：条件价格偏离因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'close']等列
        w (int | None): 核心时间窗口参数（默认100）
        uni_col (str | None): 单一基础数据列（默认'close'）

    返回:
        pd.DataFrame: 包含 ['trade_date', 'time', 'symbol', 'factor'] 的DataFrame
    """
    # 窗口配置
    window_configs = {
        'ma_window': 100.0,       # w，移动平均窗口
        'delta_window': 100.0,    # w，差分窗口
        'tsmin_window': 100.0,    # w，滚动最小值窗口
        'delta_3_window': 3.0     # 固定，3期差分窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    ma_window = window_sizes['ma_window']
    delta_window = window_sizes['delta_window']
    tsmin_window = window_sizes['tsmin_window']
    delta_3_window = window_sizes['delta_3_window']
    
    df = data_df.copy()

    # 按symbol分组计算
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 1. 计算移动平均（使用实际窗口大小）
    df['ma_100'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=ma_window, min_periods=w).mean()
    )

    # 2. 计算MA的差分（使用实际窗口大小）
    df['delta_ma_100'] = df.groupby('symbol')['ma_100'].transform(
        lambda x: x - x.shift(delta_window)
    )

    # 3. 获取期前的收盘价（使用实际窗口大小）
    df['close_100_lag'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.shift(delta_window)
    )

    # 4. 计算增长率（处理分母为0的情况）
    # 确保分母不为0，添加一个小的常数
    df['growth_rate'] = df['delta_ma_100'] / (df['close_100_lag'] + 1e-8)
    # 原始逻辑中，如果close_100_lag为0，则growth_rate为nan，这里保留这个逻辑
    df['growth_rate'] = df['growth_rate'].where(df['close_100_lag'] != 0, float('nan'))


    # 5. 计算滚动最小值（使用实际窗口大小）
    df['tsmin_100'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=tsmin_window, min_periods=w).min()
    )

    # 6. 计算差分（使用实际窗口大小）
    df['delta_3'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x - x.shift(delta_3_window)
    )

    # 7. 应用条件计算因子值
    mask = df['growth_rate'] <= 0.05
    df['factor'] = np.where(
        mask,
        - (df[uni_col] - df['tsmin_100']),
        - df['delta_3']
    )

    # 8. 替换±inf为NaN
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 保持日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

