# Alpha299因子 - factor_109
# 原始因子编号: 109
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_109(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha 150因子：成交量加权典型价格因子
    核心公式: (CLOSE+HIGH+LOW)/3 * VOLUME

    参数:
    data_df: 输入数据DataFrame，必须包含['close', 'high', 'low', 'volume']列
    w: 无天数参数（本因子不涉及时间窗口）
    uni_col: 无需指定单一基础列（使用三个价格列）

    返回:
    包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 检查必要列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'close', 'high', 'low', 'volume']
    missing_cols = [col for col in required_columns if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    df = data_df.copy()

    # 计算典型价格，分母加一个微小值防止除以0
    df['tp'] = (df['close'] + df['high'] + df['low']) / (3 + 1e-8)

    # 计算因子值
    df['factor'] = df['tp'] * df['volume']

    # 处理无穷大值和NaN值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    # 确保volume为非负数，虽然通常volume不会为负，但作为保护措施
    df['volume'] = df['volume'].apply(lambda x: x if x >= 0 else np.nan)
    # 重新计算因子值，确保volume为非负后
    df['factor'] = df['tp'] * df['volume']
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)


    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

