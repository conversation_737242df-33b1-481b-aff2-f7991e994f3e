# Alpha299因子 - factor_342
# 原始因子编号: 342
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_342(data_df, w: int | None = 12, uni_col: str | None = None):
    """
    计算Alpha148因子 - 优化版本

    参数：
    - data_df: 输入的DataFrame
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为14天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，本因子不使用单一列，设为None

    返回：
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window1': 20,  # 标准化窗口
        'window2': 18,  # 第一个回归窗口
        'window3': 12   # 第二个回归窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window1 = window_sizes['window1']
    window2 = window_sizes['window2']
    window3 = window_sizes['window3']

    # 减小最小周期要求，提高存活率
    min_periods1 = max(int(window1 * 0.5), 2)  # 标准化最小周期
    min_periods2 = max(int(window2 * 0.5), 2)  # 回归最小周期
    min_periods3 = max(int(window3 * 0.5), 2)  # 回归最小周期

    # 只保留必要的列以减少内存使用
    needed_columns = ['trade_date', 'time', 'symbol', 'open', 'amount', 'volume']
    if 'vwap' in data_df.columns:
        needed_columns.append('vwap')

    df = data_df[needed_columns].copy()

    # 确保数据类型正确
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 计算vwap (成交量加权平均价)
    if 'vwap' not in df.columns:
        if 'amount' in df.columns and 'volume' in df.columns:
            # 添加小常数防止除零
            df['vwap'] = df['amount'] / (df['volume'] + 1e-10)

    # 初始化因子列
    df['factor'] = np.nan

    # 按symbol分组计算
    for symbol, group in df.groupby('symbol'):
        # 计算成交额和vwap的滚动标准化
        # 1. 对成交额进行过去window1个周期的滚动标准化
        amount_mean = group['amount'].rolling(window=window1, min_periods=w).mean()
        amount_std = group['amount'].rolling(window=window1, min_periods=w).std()
        # 添加小常数防止除零
        amount_zscore = (group['amount'] - amount_mean) / (amount_std + 1e-10)

        # 2. 对vwap进行过去window1个周期的滚动标准化
        vwap_mean = group['vwap'].rolling(window=window1, min_periods=w).mean()
        vwap_std = group['vwap'].rolling(window=window1, min_periods=w).std()
        # 添加小常数防止除零
        vwap_zscore = (group['vwap'] - vwap_mean) / (vwap_std + 1e-10)

        # 3. 计算标准化后的成交额与标准化后的vwap的和
        T1 = amount_zscore + vwap_zscore

        # 4. 计算开盘价的余弦值 - 缩放开盘价以避免数值问题
        # 确保开盘价非负，并添加小常数防止cos(0)的潜在问题（虽然cos(0)是1，但为了统一处理）
        scaled_open = (group['open'] + 1e-8) * 0.001
        T2 = np.cos(scaled_open)

        # 5. 计算T1与T2在过去window2个周期内的滚动回归贝塔系数
        # 计算滚动协方差
        T1_T2_product = T1 * T2
        T1_T2_mean = T1_T2_product.rolling(window=window2, min_periods=w).mean()
        T1_mean = T1.rolling(window=window2, min_periods=w).mean()
        T2_mean = T2.rolling(window=window2, min_periods=w).mean()
        T1_T2_cov = T1_T2_mean - T1_mean * T2_mean

        # 计算T1的滚动方差
        T1_squared = T1 * T1
        T1_squared_mean = T1_squared.rolling(window=window2, min_periods=w).mean()
        T1_var = T1_squared_mean - T1_mean * T1_mean

        # 计算第一个回归贝塔系数
        # 添加小常数防止除零
        X1 = T1_T2_cov / (T1_var + 1e-10)

        # 6. 计算volume与vwap在过去window3个周期内的滚动回归贝塔系数
        # 计算滚动协方差
        vol_vwap_product = group['volume'] * group['vwap']
        vol_vwap_mean = vol_vwap_product.rolling(window=window3, min_periods=w).mean()
        vol_mean = group['volume'].rolling(window=window3, min_periods=w).mean()
        vwap_mean_short = group['vwap'].rolling(window=window3, min_periods=w).mean()
        vol_vwap_cov = vol_vwap_mean - vol_mean * vwap_mean_short

        # 计算volume的滚动方差
        vol_squared = group['volume'] * group['volume']
        vol_squared_mean = vol_squared.rolling(window=window3, min_periods=w).mean()
        vol_var = vol_squared_mean - vol_mean * vol_mean

        # 计算第二个回归贝塔系数
        # 添加小常数防止除零
        X2 = vol_vwap_cov / (vol_var + 1e-10)

        # 7. 取X1和X2中逐元素的较大值
        # 使用np.fmax处理NaN值
        factor_values = np.fmax(X1, X2)

        # 更新原始DataFrame
        df.loc[group.index, 'factor'] = factor_values

    # 8. 将结果中的无穷大值替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]
    return result_df

