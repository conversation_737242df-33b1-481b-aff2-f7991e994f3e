# Alpha299因子 - factor_43
# 原始因子编号: 43
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_43(data_df, w: int | None = 2.70756, uni_col: str | None = None):
    """
    计算Alpha94因子（修复版）
    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为2.70756天（ts_rank_correlation窗口）。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 本因子涉及VWAP和ADV60两个基础列，因此设为None
    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'ts_min_window': 11.5783,
        'ts_rank_vwap_window': 19.6462,
        'ts_rank_adv_window': 4.02992,
        'correlation_window': 18.0926,
        'ts_rank_correlation_window': 2.70756
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    ts_min_window = window_sizes['ts_min_window']
    ts_rank_vwap_window = window_sizes['ts_rank_vwap_window']
    ts_rank_adv_window = window_sizes['ts_rank_adv_window']
    correlation_window = window_sizes['correlation_window']
    ts_rank_correlation_window = window_sizes['ts_rank_correlation_window']

    df = data_df.copy()

    # 计算VWAP
    df['vwap'] = df.groupby('symbol')['amount'].transform(lambda x: x.cumsum()) / \
                 (df.groupby('symbol')['volume'].transform(lambda x: x.cumsum()) + 1e-8)

    # 计算ADV60
    df['adv60'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=int(w), min_periods=int(w)).mean())

    # 计算VWAP的11.5783期最小值
    df['ts_min_vwap'] = df.groupby('symbol')['vwap'].transform(
        lambda x: x.rolling(window=ts_min_window, min_periods=int(w)).min()
    )

    # 计算VWAP - ts_min(VWAP)
    df['vwap_diff'] = df['vwap'] - df['ts_min_vwap']

    # 对vwap_diff进行滚动窗口排序
    df['rank_vwap_diff'] = df.groupby('symbol')['vwap_diff'].transform(
        lambda x: x.rolling(window=ts_rank_vwap_window, min_periods=int(w)).apply(
            lambda y: rankdata(y, method='average', nan_policy='omit')[-1] / (len(y) + 1e-8) if len(y) > 0 else np.nan
        )
    )

    # 计算VWAP的ts_rank(19.6462)
    df['ts_rank_vwap'] = df.groupby('symbol')['vwap'].transform(
        lambda x: x.rolling(window=ts_rank_vwap_window, min_periods=int(w)).apply(
            lambda y: rankdata(y, method='average', nan_policy='omit')[-1] / (len(y) + 1e-8) if len(y) > 0 else np.nan
        )
    )

    # 计算ADV60的ts_rank(4.02992)
    df['ts_rank_adv'] = df.groupby('symbol')['adv60'].transform(
        lambda x: x.rolling(window=ts_rank_adv_window, min_periods=int(w)).apply(
            lambda y: rankdata(y, method='average', nan_policy='omit')[-1] / (len(y) + 1e-8) if len(y) > 0 else np.nan
        )
    )

    # 计算VWAP_ts_rank和ADV_ts_rank在18.0926期的相关性
    def rolling_corr_safe(x, y, window):
        x_safe = x.replace([np.inf, -np.inf], np.nan)
        y_safe = y.replace([np.inf, -np.inf], np.nan)
        corr_result = x_safe.rolling(window=window, min_periods=int(w)).corr(y_safe)
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    df['correlation'] = df.groupby('symbol').apply(
        lambda group: rolling_corr_safe(group['ts_rank_vwap'], group['ts_rank_adv'], window=correlation_window)
    ).reset_index(level=0, drop=True)

    # 对相关性结果进行2.70756期ts_rank
    df['ts_rank_correlation'] = df.groupby('symbol')['correlation'].transform(
        lambda x: x.rolling(window=ts_rank_correlation_window, min_periods=int(w)).apply(
            lambda y: rankdata(y, method='average', nan_policy='omit')[-1] / (len(y) + 1e-8) if len(y) > 0 else np.nan
        )
    )

    # 计算最终因子值
    df['factor'] = - (df['rank_vwap_diff'] ** df['ts_rank_correlation'])
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 格式化日期时间字段
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

