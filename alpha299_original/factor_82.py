# Alpha299因子 - factor_82
# 原始因子编号: 82
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_82(data_df, w: int | None = 6, uni_col: str | None = 'low'):
    """
    计算Alpha 103因子：最低价位置指标
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 窗口长度，默认为20天
        uni_col (str | None): 基础数据列，默认为'low'
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    df = data_df.copy()
    
    # 三段式混合模型窗口配置  
    window_configs = {
        'base_window': 20,      # 基础窗口
        'mean_window': 6,       # 均值窗口
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                
                # 处理所有窗口值相同的情况（避免除零错误）
                if max_base == min_base:
                    # 如果所有窗口值相同，直接使用w1作为结果
                    for name, base_value in window_configs.items():
                        row_data[name] = max(1, int(w1))
                else:
                    for name, base_value in window_configs.items():
                        position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                        target_range_size = max(0, dynamic_max - w1)
                        final_value = w1 + position * target_range_size
                        final_value = min(max(final_value, w1), w_max)
                        row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        base_window = window_sizes.loc[w, 'base_window']
        mean_window = window_sizes.loc[w, 'mean_window']
    else:
        base_window = window_configs['base_window']
        mean_window = window_configs['mean_window']
    
    # 计算LOWDAY指标
    # 确保 rolling 窗口内有足够的数据且没有inf/nan，这里通过 min_periods=w 已经处理了一部分
    # 对于 argmin，如果窗口内有 nan，结果可能是 nan。这里依赖 pandas 的 rolling.apply 的默认行为，
    # 如果 apply 函数返回 nan，则结果为 nan。
    df['lowday'] = (
        df.groupby('symbol')[uni_col]
        .transform(
            lambda x: x.rolling(window=base_window, min_periods=w)
            .apply(lambda y: base_window - y.argmin() if not np.isnan(y).all() else np.nan, raw=True)
        )
    )
    
    # 计算因子值
    # 分母 w 恒大于 0，无需特殊处理
    df['factor'] = (base_window - df['lowday']) / (base_window + 1e-8) * 100
    
    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 保留必要列并删除无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    return output_df

