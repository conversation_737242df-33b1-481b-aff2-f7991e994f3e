# Alpha299因子 - factor_77
# 原始因子编号: 77
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_77(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha 90因子：VWAP与标准化成交量排名相关性的负排名因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含指定的字段
        w (int | None): 核心可调参数，相关性窗口（默认5）
        uni_col (str | None): 单一基础列参数（本因子不适用，默认None）

    返回:
        pd.DataFrame: 包含因子结果的DataFrame
    """
    # 三段式混合模型窗口配置
    window_configs = {
        'corr_window': 5,       # 相关性窗口
        'zscore_window': 20,    # Z-score窗口
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        corr_window = window_sizes.loc[w, 'corr_window']
        zscore_window = window_sizes.loc[w, 'zscore_window']
    else:
        corr_window = window_configs['corr_window']
        zscore_window = window_configs['zscore_window']
    
    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'high', 'low', 'close', 'volume']
    for col in required_cols:
        if col not in data_df.columns:
            raise ValueError(f"缺失必要列：{col}")

    df = data_df.copy()

    # 计算VWAP（成交量加权平均价）
    df['vwap'] = (df['high'] + df['low'] + df['close']) / 3

    # 计算R_VWAP: VWAP的横截面百分比排名
    df['rank_vwap'] = df.groupby('time')['vwap'].transform(lambda x: x.rank(pct=True))

    # 计算Z-score窗口（现在使用动态计算的值）
    # zscore_window = 4 * w if w is not None else 20
    # 计算Volume的Z-score标准化
    df['volume_zscore'] = df.groupby('symbol')['volume'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std() + 1e-8)
    )

    # 计算R_ZVol: 标准化Volume的横截面百分比排名
    df['rank_zvol'] = df.groupby('time')['volume_zscore'].transform(lambda x: x.rank(pct=True))

    # 计算相关系数Corr_t（现在使用动态计算的值）
    # corr_window = w if w is not None else 5
    # 使用groupby和rolling计算滚动相关系数
    def calc_corr(group):
        # 在计算相关性之前，先处理可能导致NaN或Inf的值
        # 这里我们选择在计算corr之后将NaN和Inf替换为0，因为常数序列的corr是NaN，而我们希望在这种情况下结果为0
        corr_result = group['rank_vwap'].rolling(window=corr_window, min_periods=w).corr(group['rank_zvol'])
        return corr_result.fillna(0).replace([float('inf'), -float('inf')], 0)

    df['corr'] = df.groupby('symbol').apply(calc_corr).droplevel(0)

    # 计算Corr的横截面百分比排名并取负
    df['factor_temp'] = df.groupby('time')['corr'].transform(lambda x: x.rank(pct=True))
    df['factor'] = -df['factor_temp']

    # 处理无效值（±∞替换为NaN）
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame并删除无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

