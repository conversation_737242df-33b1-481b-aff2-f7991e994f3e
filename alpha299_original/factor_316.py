# Alpha299因子 - factor_316
# 原始因子编号: 316
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_316(data_df, w: int | None = 4, uni_col: str | None = None):
    """
    计算Alpha90因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为4天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 指定单一基础列，此处设为None
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'rolling_window': 4   # 滚动窗口（用于排名和回归beta计算）
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    rolling_window = window_sizes['rolling_window']

    # 检查必要列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'volume', 'amount', 'low']
    for col in required_columns:
        if col not in data_df.columns:
            raise KeyError(f"缺少必要列: {col}")

    # 计算vwap（成交量加权平均价）
    df = data_df.copy()
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
    df['vwap'].replace([np.inf, -np.inf], np.nan, inplace=True)  # 处理除以零的情况

    # 按symbol分组，并在每个组内按交易日期和时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 计算成交量的滚动排名并归一化
    # 确保分母不为零
    df['volume_rank'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=rolling_window, min_periods=w).rank(method='average', ascending=False) / (rolling_window + 1e-8)
    )

    # 计算Sigmoid值
    # np.exp(-x)在x很大时可能导致inf，但sigmoid函数本身不会产生inf或nan，除非输入是inf/nan
    df['sigmoid'] = 1 / (1 + np.exp(-df['volume_rank']))
    # 处理可能由volume_rank中的nan/inf导致的sigmoid中的nan/inf
    df['sigmoid'].replace([np.inf, -np.inf], np.nan, inplace=True)

    # 计算滚动回归贝塔系数
    def compute_beta(group):
        # 计算low对amount的贝塔系数：Cov(low, amount) / Var(amount)
        # 滚动计算时，如果窗口内数据为常数，var可能为0，需要处理
        # 如果窗口内有nan/inf，cov和var可能为nan/inf
        cov = group['low'].rolling(rolling_window, min_periods=w).cov(group['amount'])
        var = group['amount'].rolling(rolling_window, min_periods=w).var()

        # 处理var为0的情况，此时beta无意义，设为nan
        beta = cov / (var + 1e-8)

        # 处理cov或var为nan/inf导致beta为nan/inf的情况
        beta.replace([np.inf, -np.inf], np.nan, inplace=True)
        return beta

    df['beta'] = df.groupby('symbol').apply(compute_beta).reset_index(level=0, drop=True)

    # 计算绝对值
    df['abs_beta'] = np.abs(df['beta'])

    # 计算X3 = sigmoid / abs_beta
    # 确保分母不为零
    df['X3'] = df['sigmoid'] / (df['abs_beta'] + 1e-8)
    # 处理可能由sigmoid或abs_beta中的nan/inf导致的X3中的nan/inf
    df['X3'].replace([np.inf, -np.inf], np.nan, inplace=True)

    # 计算最终因子值
    df['factor'] = df['vwap'] * df['X3']

    # 处理无穷大值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并删除缺失值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

