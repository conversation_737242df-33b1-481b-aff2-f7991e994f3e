# Alpha299因子 - factor_504
# 原始因子编号: 504
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_504(data_df, w: int | None = None, uni_col: str | None = 'close'):
    """
    计算负成交量指标 (Negative Volume Index, NVI)

    参数:
    data_df: DataFrame, 包含交易数据
    w: int | None, 此因子不需要窗口参数，设为None
    uni_col: str | None, 用于计算的价格列，默认为'close'

    返回:
    DataFrame, 包含['trade_date', 'time', 'symbol', 'factor']列
    """
    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol和时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 初始化NVI计算
    initial_value = 1000  # NVI的初始值设为1000

    # 按symbol分组计算NVI
    def calculate_nvi(group):
        # 计算前一天的价格和成交量
        group['prev_close'] = group[uni_col].shift(1)
        group['prev_volume'] = group['volume'].shift(1)

        # 初始化NVI序列，第一个值设为初始值
        nvi = np.zeros(len(group))
        nvi[0] = initial_value

        # 逐日计算NVI
        for i in range(1, len(group)):
            prev_close = group['prev_close'].iloc[i]
            curr_close = group[uni_col].iloc[i]
            prev_volume = group['prev_volume'].iloc[i]
            curr_volume = group['volume'].iloc[i]

            # 根据NVI计算规则
            # 添加对prev_close为0或nan的保护
            if pd.isna(prev_close) or prev_close == 0:
                 # 如果前一天收盘价无效，则NVI保持不变
                 nvi[i] = nvi[i-1]
            elif curr_volume < prev_volume:
                # 当天成交量小于前一天
                nvi[i] = nvi[i-1] * (curr_close / prev_close)
            else:
                # 当天成交量大于或等于前一天
                nvi[i] = nvi[i-1]

            # 对计算出的NVI值进行inf和nan的保护
            if np.isinf(nvi[i]) or np.isnan(nvi[i]):
                 nvi[i] = nvi[i-1] if i > 0 else initial_value # 如果计算结果异常，使用前一个值或初始值

        group['factor'] = nvi
        return group

    # 按symbol分组应用NVI计算函数
    df = df.groupby('symbol', group_keys=False).apply(calculate_nvi)

    # 恢复日期格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 提取结果列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

