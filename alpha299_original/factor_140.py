# Alpha299因子 - factor_140
# 原始因子编号: 140
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_140(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha 2因子：日内价格振幅结构因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的价格和时间字段
        w (int | None): 以天为单位的核心可调参数（本因子无需调整，设为None）
        uni_col (str | None): 单一基础数据列参数（本因子使用多列，设为None）

    返回:
        pd.DataFrame: 包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']
    """
    df = data_df.copy()

    # 检查必要列
    required_columns = ['open', 'high', 'low', 'close']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"缺少必要列: {col}")

    # 计算日内价格结构项 S_t
    # 避免除以0的情况
    df['S_t'] = (2 * df['close'] - df['high'] - df['low']) / (df['high'] - df['low'] + 1e-8)
    df['S_t'] = df['S_t'].replace([np.inf, -np.inf], np.nan)

    # 按symbol分组并按时间排序
    df = df.sort_values(['symbol', 'time'])

    # 计算一期差分 ΔS_t
    df['delta_S_t'] = df.groupby('symbol')['S_t'].diff(1)

    # 计算最终因子值（取负）
    df['factor'] = -df['delta_S_t']

    # 处理无效值
    df = df.dropna(subset=['factor'])

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

