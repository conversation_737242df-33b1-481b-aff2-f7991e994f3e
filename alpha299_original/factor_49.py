# Alpha299因子 - factor_49
# 原始因子编号: 49
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_49(data_df, w: int | None = 5, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 5        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    df = data_df.copy()

    # 计算MA_5(Close)
    n_ma = w
    df['ma_close'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=n_ma, min_periods=w).mean()
    )

    # 计算Term_t
    df['term_t'] = (df['high'] - df['low']) / (df['ma_close'].replace(0, 1e-8) + 1e-8) # 避免ma_close为0

    # 计算Term_t_lagged (滞后2期)
    df['term_t_lagged'] = df.groupby('symbol')['term_t'].shift(2)

    # 计算R1：横截面排序
    df['R1'] = df.groupby('time')['term_t_lagged'].transform(
        lambda x: x.rank(pct=True)
    )

    # 计算ZVol_t：20日Z-score (4*w)
    n_zscore = 4 * w
    df['volume_mean'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=n_zscore, min_periods=w).mean()
    )
    df['volume_std'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=n_zscore, min_periods=w).std()
    )
    df['ZVol_t'] = (df['volume'] - df['volume_mean']) / (df['volume_std'].replace(0, 1e-8) + 1e-8) # 避免volume_std为0

    # 第一次横截面排序ZVol_t
    df['RankZVol_t'] = df.groupby('time')['ZVol_t'].transform(
        lambda x: x.rank(pct=True)
    )

    # 第二次横截面排序RankZVol_t
    df['RankRankZVol_t'] = df.groupby('time')['RankZVol_t'].transform(
        lambda x: x.rank(pct=True)
    )

    # 计算VWAP（按symbol和trade_date分组，按时间排序后累积计算）
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])
    df['cum_amount'] = df.groupby(['symbol', 'trade_date'])['amount'].cumsum()
    df['cum_volume'] = df.groupby(['symbol', 'trade_date'])['volume'].cumsum()
    df['vwap'] = df['cum_amount'] / (df['cum_volume'].replace(0, 1e-8) + 1e-8) # 避免cum_volume为0

    # 计算Denom_t
    df['denominator'] = (df['term_t'] / (df['vwap'] - df['close'] + 1e-8))
    df['denominator'] = df['denominator'].replace([float('inf'), -float('inf')], float('nan'))  # 处理除零

    # 计算最终因子
    df['factor'] = (df['R1'] * df['RankRankZVol_t']) / (df['denominator'].replace(0, 1e-8) + 1e-8) # 避免denominator为0

    # 处理无效值
    df = df.dropna(subset=['factor'])

    # 调整日期和时间格式
    df['trade_date'] = df['trade_date'].astype('string')
    df['time'] = df['time'].astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']]
    return output_df

