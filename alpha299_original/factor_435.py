# Alpha299因子 - factor_435
# 原始因子编号: 435
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_435(data_df, w: int | None = 14, uni_col: str | None = None):
    """
    计算平均真实波幅(ATR)因子

    参数:
    data_df: 输入的DataFrame
    w: ATR计算的周期长度，默认为14。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    uni_col: 单一数据列，本因子不使用单一列，设为None

    返回:
    包含['trade_date', 'time', 'symbol', 'factor']列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}
    # 定义所有窗口的基准值
    window_configs = {
        'atr_window': 14
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    atr_window = window_sizes['atr_window']

    # 复制数据以避免修改原始数据
    df = data_df.copy()

    # 检查必要的列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"输入数据缺少必要的列: {missing_columns}")

    # 转换日期列为datetime格式以便于排序和分组操作
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 确保数据按symbol和时间排序
    df = df.sort_values(['symbol', 'time'])

    # 按symbol分组计算真实波幅(TR)
    # TR = max(high - low, |high - prev_close|, |low - prev_close|)
    df['prev_close'] = df.groupby('symbol')['close'].shift(1)

    # 计算真实波幅的三个组成部分
    df['range1'] = df['high'] - df['low']  # 当日振幅
    df['range2'] = (df['high'] - df['prev_close']).abs()  # 当日最高价与昨日收盘价之差的绝对值
    df['range3'] = (df['low'] - df['prev_close']).abs()  # 当日最低价与昨日收盘价之差的绝对值

    # 计算真实波幅TR
    df['TR'] = df[['range1', 'range2', 'range3']].max(axis=1)

    # 计算ATR
    if atr_window <= 1:
        # 如果周期为1，ATR等于TR
        df['ATR'] = df['TR']
    else:
        # 首先计算初始ATR值（前N个TR的简单移动平均）
        # 确保TR值非负且非NaN
        df['TR'] = df['TR'].apply(lambda x: max(x, 0) if pd.notna(x) else np.nan)
        df['ATR'] = df.groupby('symbol')['TR'].transform(
            lambda x: x.rolling(window=atr_window, min_periods=w).mean()
        )

        # 使用威尔德平滑法计算后续ATR值
        # ATR_t = ((N-1) * ATR_{t-1} + TR_t) / N
        # 注意：这里我们已经计算了初始ATR，所以只需在有了初始值后应用威尔德平滑
        # 使用自定义函数进行威尔德平滑计算
        def wilder_smoothing(group):
            tr_series = group['TR'].copy()
            atr_series = group['ATR'].copy()

            # 找到第一个非NaN的ATR值的索引
            first_valid_idx = atr_series.first_valid_index()
            if first_valid_idx is None:
                return atr_series

            # 获取初始ATR值
            prev_atr = atr_series.loc[first_valid_idx]

            # 从初始ATR值后开始应用威尔德平滑
            for idx in group.index[group.index > first_valid_idx]:
                # 确保prev_atr和current_tr非NaN
                if pd.isna(prev_atr):
                    continue

                current_tr = tr_series.loc[idx]
                if pd.isna(current_tr):
                    # 如果当前TR是NaN，则当前ATR也为NaN
                    atr_series.loc[idx] = np.nan
                    continue

                # 威尔德平滑公式，分母加1e-8防止除以0
                atr_series.loc[idx] = ((atr_window - 1) * prev_atr + current_tr) / (atr_window + 1e-8)
                prev_atr = atr_series.loc[idx]

            return atr_series

        # 应用威尔德平滑
        # 使用apply代替for循环以提高效率
        df['ATR'] = df.groupby('symbol').apply(wilder_smoothing).reset_index(level=0, drop=True)


    # 对ATR进行滚动标准化处理
    def ts_zscore(series, window):
        """对时间序列进行滚动标准化"""
        # 确保输入序列没有inf值
        series = series.replace([np.inf, -np.inf], np.nan)
        rolling_mean = series.rolling(window=window, min_periods=w).mean()
        # 分母加1e-8防止标准差为0时除以0
        rolling_std = series.rolling(window=window, min_periods=w).std()
        # 处理标准差为0的情况，此时zscore为0
        zscore = (series - rolling_mean) / (rolling_std + 1e-8)
        # 将因为标准差为0导致的inf或nan替换为0
        zscore = zscore.replace([np.inf, -np.inf], np.nan).fillna(0)
        return zscore

    # 对每个symbol的ATR进行滚动标准化，使用与ATR计算相同的周期
    df['factor'] = df.groupby('symbol')['ATR'].transform(
        lambda x: ts_zscore(x, window=atr_window)
    )

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 只保留需要的列并删除含有NaN的行
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

