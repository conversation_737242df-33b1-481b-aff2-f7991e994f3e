# Alpha299因子 - factor_318
# 原始因子编号: 318
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_318(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算Alpha92因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 指定单一基础列，此处设为None
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n_delta': 7,   # delta差值窗口
        'n_corr': 10,   # 相关系数窗口
        'n_zscore': 20  # Z-score标准化窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n_delta = window_sizes['n_delta']
    n_corr = window_sizes['n_corr']
    n_zscore = window_sizes['n_zscore']

    df = data_df.copy()

    # 确保数据按时间排序（关键修复点1）
    df.sort_values(by=['trade_date', 'time'], inplace=True)

    # 1. 计算 delta(close, n_delta)
    df['delta_close'] = df.groupby('symbol')['close'].transform(
        lambda x: x - x.shift(n_delta)
    )

    # 2. 计算 ts_zscore(delta_close, n_zscore)
    mean_delta = df.groupby('symbol')['delta_close'].transform(
        lambda x: x.rolling(window=n_zscore, min_periods=w).mean()
    )
    std_delta = df.groupby('symbol')['delta_close'].transform(
        lambda x: x.rolling(window=n_zscore, min_periods=w).std()
    )
    # Add a small epsilon to the standard deviation to avoid division by zero
    df['zscore'] = (df['delta_close'] - mean_delta) / (std_delta + 1e-8)
    # Replace potential inf/nan from zscore calculation
    df['zscore'] = df['zscore'].replace([np.inf, -np.inf], np.nan)


    # 3. 计算 log(volume)
    # Add a small epsilon to volume before taking log to handle zero or negative volume
    df['log_volume'] = np.log(df['volume'].abs() + 1e-8)

    # 4. 计算 ts_corr(n_corr, low, volume)
    def calc_corr(group):
        # Ensure no division by zero in correlation calculation by handling constant series
        # Pandas corr handles NaNs internally, but we will replace resulting NaNs/infs
        corr_result = group['low'].rolling(window=n_corr, min_periods=w).corr(group['volume'])
        # Replace potential inf/nan from correlation calculation with 0
        return corr_result.replace([np.inf, -np.inf], 0).fillna(0)

    df['corr_low_volume'] = df.groupby('symbol', group_keys=False).apply(calc_corr)

    # 5. 取 log_volume 和 corr_low_volume 的较小值
    df['min_val'] = df[['log_volume', 'corr_low_volume']].min(axis=1)

    # 6. 取负数
    df['neg_min'] = -df['min_val']

    # 7. 计算最终因子值
    df['factor'] = df['zscore'] - df['neg_min']

    # 8. 处理无穷大值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 转换日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并处理NaN
    # Keep the original dropna behavior as requested
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

