# Alpha299因子 - factor_143
# 原始因子编号: 143
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_143(data_df, w: int | None = 2, uni_col: str | None = None):
    """
    计算Alpha因子：基于VWAP和成交量标准化排名相关性的因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列参数（本因子不适用，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 20,  # 标准化窗口
        'correlation_window': 6,  # 相关性窗口 (3 * w / 10)
        'sum_window': 2  # 滚动求和窗口 (w / 10)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    zscore_window = window_sizes['zscore_window']
    correlation_window = window_sizes['correlation_window']
    sum_window = window_sizes['sum_window']

    # 检查必需的列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'volume', 'amount']
    for col in required_columns:
        if col not in data_df.columns:
            raise ValueError(f"数据中缺少必需的列: {col}")

    # 确保数据按symbol和时间排序
    df = data_df.copy()
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 计算VWAP（成交量加权平均价）
    # 保护分母不为0
    df['vwap'] = df.groupby('symbol')['amount'].transform(lambda x: x.cumsum() / (df.groupby('symbol')['volume'].transform(lambda y: y.cumsum()) + 1e-8))

    # 计算ts_zscore(Volume)
    volume_ma = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=zscore_window, min_periods=w).mean())
    # 保护标准差不为0
    volume_std = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=zscore_window, min_periods=w).std(ddof=0))
    df['ZV'] = (df['volume'] - volume_ma) / (volume_std + 1e-8)

    # 计算ts_zscore(VWAP)
    vwap_ma = df.groupby('symbol')['vwap'].transform(lambda x: x.rolling(window=zscore_window, min_periods=w).mean())
    # 保护标准差不为0
    vwap_std = df.groupby('symbol')['vwap'].transform(lambda x: x.rolling(window=zscore_window, min_periods=w).std(ddof=0))
    df['ZVWAP'] = (df['vwap'] - vwap_ma) / (vwap_std + 1e-8)

    # 横截面百分比排名
    df['R_V'] = df.groupby(['trade_date', 'time'])['ZV'].transform(lambda x: x.rank(pct=True))
    df['R_VWAP'] = df.groupby(['trade_date', 'time'])['ZVWAP'].transform(lambda x: x.rank(pct=True))

    # 计算滚动相关系数
    def calc_corr(group):
        # 滚动相关系数可能出现nan或inf，后处理填0
        corr_result = group['R_V'].rolling(window=correlation_window, min_periods=w).corr(group['R_VWAP'])
        return corr_result.fillna(0).replace([float('inf'), float('-inf')], 0)
    df['C_t'] = df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)

    # 滚动求和
    df['S_C'] = df.groupby('symbol')['C_t'].transform(lambda x: x.rolling(window=sum_window, min_periods=w).sum())

    # 横截面百分比排名
    df['factor'] = df.groupby(['trade_date', 'time'])['S_C'].transform(lambda x: x.rank(pct=True))

    # 处理无效值 (inf, -inf)
    df[['ZV', 'ZVWAP', 'R_V', 'R_VWAP', 'C_t', 'S_C', 'factor']] = df[['ZV', 'ZVWAP', 'R_V', 'R_VWAP', 'C_t', 'S_C', 'factor']].replace([float('inf'), float('-inf')], float('nan'))

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回所需列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

