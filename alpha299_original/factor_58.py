# Alpha299因子 - factor_58
# 原始因子编号: 58
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_58(data_df, w: int | None = None, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 5        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    """
    计算Alpha 156因子：双重衰减排名最大值因子 III
    参数:
        data_df: 输入DataFrame，包含必要的列
        w: 可调参数（本因子中无需调整，设为None）
        uni_col: 单列参数（本因子使用多列，设为None）
    返回:
        包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']
    """
    # 检查必要列
    required_cols = ['open', 'low', 'high', 'close', 'volume']
    if not all(col in data_df.columns for col in required_cols):
        raise KeyError(f"Missing required columns: {', '.join(required_cols)}")

    # 确保数据按symbol和时间排序
    data_df = data_df.sort_values(by=['symbol', 'time'])

    # 动态计算VWAP
    data_df['typical_price'] = (data_df['high'] + data_df['low'] + data_df['close']) / (3 + 1e-8)
    data_df['tp_volume'] = data_df['typical_price'] * data_df['volume']
    data_df['cumulative_tp_volume'] = data_df.groupby('symbol')['tp_volume'].cumsum()
    data_df['cumulative_volume'] = data_df.groupby('symbol')['volume'].cumsum()
    data_df['vwap'] = data_df['cumulative_tp_volume'] / (data_df['cumulative_volume'] + 1e-8)
    data_df['vwap'] = data_df['vwap'].replace([np.inf, -np.inf], np.nan)


    # 计算VWAP的5期差分
    data_df['delta_vwap'] = data_df.groupby('symbol')['vwap'].transform(lambda x: x - x.shift(5))

    # 线性衰减函数
    def decay_linear(series, window):
        weights = np.arange(1, window + 1)
        weights = weights / (weights.sum() + 1e-8)
        # Ensure no NaNs in the window for rolling apply
        return series.rolling(window).apply(lambda x: np.dot(x, weights) if not np.isnan(x).any() else np.nan, raw=True)

    # 对Delta VWAP进行3期线性衰减
    data_df['dl1'] = data_df.groupby('symbol')['delta_vwap'].transform(lambda x: decay_linear(x, 3))

    # 横截面百分比排序R1
    data_df['r1'] = data_df.groupby(['trade_date', 'time'])['dl1'].transform(lambda x: x.rank(pct=True))

    # 计算混合价格P_mix
    data_df['p_mix'] = 0.15 * data_df['open'] + 0.85 * data_df['low']

    # 计算混合价格的2期变化率
    data_df['p_mix_shifted'] = data_df.groupby('symbol')['p_mix'].transform(lambda x: x.shift(2))
    data_df['roc_pmix'] = (data_df['p_mix'] - data_df['p_mix_shifted']) / (data_df['p_mix'] + 1e-8)
    data_df['roc_pmix'] = data_df['roc_pmix'].replace([np.inf, -np.inf], np.nan)

    # 对-ROC进行3期线性衰减
    data_df['negative_roc'] = -data_df['roc_pmix']
    data_df['dl2'] = data_df.groupby('symbol')['negative_roc'].transform(lambda x: decay_linear(x, 3))

    # 横截面百分比排序R2
    data_df['r2'] = data_df.groupby(['trade_date', 'time'])['dl2'].transform(lambda x: x.rank(pct=True))

    # 计算最终因子值
    data_df['max_rank'] = data_df[['r1', 'r2']].max(axis=1)
    data_df['factor'] = -data_df['max_rank'].replace([np.inf, -np.inf], np.nan)

    # 格式化日期和时间
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    return data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

