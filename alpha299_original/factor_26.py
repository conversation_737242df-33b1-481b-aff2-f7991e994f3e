# Alpha299因子 - factor_26
# 原始因子编号: 26
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_26(data_df, w: int | None = 9, uni_col: str | None = 'low'):
    """
    计算Alpha4因子：基于最低价跨截面排名的时间序列排名负值因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（默认为'low'）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'ts_rank_window': 9    # 时间序列排名窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    ts_rank_window = window_sizes['ts_rank_window']

    df = data_df.copy()

    # 1. 跨截面排名（每个时间点内的排名）
    # 按时间分组，对每个时间点的LOW进行排名
    # 添加对uni_col列的inf和nan处理，替换为np.nan，rankdata会忽略nan
    df[uni_col] = df[uni_col].replace([np.inf, -np.inf], np.nan)
    cross_section_rank = df.groupby('time')[uni_col].transform(lambda x: (x.rank(method='average') - 1) / (len(x) - 1 + 1e-8) if len(x) > 1 else np.nan)
    df['cross_rank'] = cross_section_rank

    # 2. 时间序列排名（每个品种的时间序列排名）
    # 按品种分组，使用rolling窗口计算排名
    def ts_rank(x):
        # 对每个窗口内的值进行排名，返回最后一个值的排名
        # rankdata(x, nan_policy='omit') 会忽略nan进行排名
        ranked_data = rankdata(x, nan_policy='omit')
        if len(ranked_data) == 0:
            return np.nan
        return ranked_data[-1] / (len(x) + 1e-8) # 使用原始窗口长度计算分母

    # 在进行rolling操作前，确保cross_rank列没有inf值
    df['cross_rank'] = df['cross_rank'].replace([np.inf, -np.inf], np.nan)
    df['ts_rank'] = df.groupby('symbol')['cross_rank'].rolling(window=ts_rank_window, min_periods=w).apply(ts_rank, raw=True).reset_index(level=0, drop=True)

    # 3. 取负
    df['factor'] = -df['ts_rank']

    # 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    # 保持原始的dropna行为
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

