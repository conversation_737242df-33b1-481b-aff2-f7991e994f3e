# Alpha299因子 - factor_289
# 原始因子编号: 289
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_289(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha45因子

    参数:
    data_df (pd.DataFrame): 输入数据
    w (int | None): 核心可调参数（窗口期13）
    uni_col (str | None): 基础数据列，此处设为None（使用多列）

    返回:
    pd.DataFrame: 包含因子值的结果DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 8,         # ts_cov窗口
        'mean_window': 13        # ts_mean窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    cov_window = window_sizes['cov_window']           # ts_cov窗口
    mean_window = window_sizes['mean_window']         # ts_mean窗口

    # 检查必要列
    required_columns = {'close', 'amount', 'high', 'volume', 'trade_date', 'time', 'symbol'}
    if not required_columns.issubset(data_df.columns):
        missing = required_columns - set(data_df.columns)
        raise ValueError(f"输入数据缺失必要列: {missing}")

    # 动态生成vwap（如果不存在）
    if 'vwap' not in data_df.columns:
        data_df['vwap'] = data_df['amount'] / (data_df['volume'] + 1e-8)

    df = data_df.copy()

    # 步骤1: T1 = -close
    df['T1'] = df.groupby('symbol')['close'].transform(lambda x: -x)

    # 步骤2: T2 = ts_mean(amount, mean_window)
    df['T2'] = df.groupby('symbol')['amount'].transform(
        lambda x: x.rolling(window=mean_window, min_periods=w).mean()
    )

    # 步骤3: X1 = T1 / T2
    df['X1'] = df['T1'] / (df['T2'] + 1e-8)
    # 处理X1中的inf和nan
    df['X1'] = df['X1'].replace([np.inf, -np.inf], np.nan)


    # 步骤4: T3 = gp_max(high, vwap)
    df['T3'] = np.maximum(df['high'], df['vwap'])

    # 步骤5: T4 = ts_cov(cov_window, T3, volume)
    def cov_func(group):
        # 确保输入列没有inf或nan影响计算
        temp_group = group[['T3', 'volume']].copy()
        temp_group = temp_group.replace([np.inf, -np.inf], np.nan)
        temp_group = temp_group.dropna() # 移除包含nan的行，避免cov计算受影响
        if len(temp_group) < cov_window: # 如果有效数据少于窗口期，无法计算协方差
            return pd.Series(np.nan, index=group.index)
        cov_result = temp_group['T3'].rolling(window=cov_window, min_periods=w).cov(temp_group['volume'])
        # 将计算结果对齐回原始索引
        return cov_result.reindex(group.index)

    df['T4'] = df.groupby('symbol').apply(cov_func).reset_index(level=0, drop=True)
    # 处理T4中的inf和nan
    df['T4'] = df['T4'].replace([np.inf, -np.inf], np.nan)


    # 步骤6: X2 = rank(T4)
    # 在rank之前处理T4中的nan，rank默认会跳过nan，但为了稳健性，可以考虑填充一个极小值或0
    # 这里选择直接rank，rank默认对nan的处理是返回nan
    df['X2'] = df.groupby('time')['T4'].transform(lambda x: x.rank(method='average', na_option='keep'))
    # 处理X2中的inf和nan，rank的结果通常不会是inf，但为了保险
    df['X2'] = df['X2'].replace([np.inf, -np.inf], np.nan)


    # 步骤7: Alpha45 = X1 / X2
    # 避免除以0，X2可能为0（当T4全为同一个值时）
    df['factor'] = df['X1'] / (df['X2'] + 1e-8)

    # 处理无穷大值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

