# Alpha299因子 - factor_144
# 原始因子编号: 144
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_144(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha因子：基于开盘价和收益率滚动求和乘积的差分排名因子
    
    参数:
        data_df (pd.DataFrame): 输入数据
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列参数（本因子不适用，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'rolling_window': 5,  # 滚动求和窗口
        'delta_window': 10    # 差分窗口 (2 * w)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    rolling_window = window_sizes['rolling_window']
    delta_window = window_sizes['delta_window']

    df = data_df.copy()

    # 确保按品种和时间排序
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 计算收益率RET
    df['ret'] = df.groupby('symbol')['close'].pct_change()

    # 计算S_Open和S_Ret
    df['S_Open'] = df.groupby('symbol')['open'].rolling(window=rolling_window).sum().reset_index(level=[0], drop=True)
    df['S_Ret'] = df.groupby('symbol')['ret'].rolling(window=rolling_window).sum().reset_index(level=[0], drop=True)

    # 计算X_t
    df['X_t'] = df['S_Open'] * df['S_Ret']

    # 计算差分（X_t - X_{t-delta_window}）
    df['delta_X'] = df.groupby('symbol')['X_t'].transform(lambda x: x - x.shift(delta_window))

    # 横截面百分比排序（修复版本）
    def rank_pct(group):
        # 直接对整个组进行排名，保持索引一致
        valid_mask = ~group.isna()
        ranked = pd.Series(np.nan, index=group.index)
        
        if valid_mask.sum() > 0:  # 有有效数据时才计算
            ranked[valid_mask] = rankdata(group[valid_mask], method='average') / valid_mask.sum()
        
        return ranked

    df['rank_pct'] = df.groupby(['trade_date', 'time'])['delta_X'].transform(rank_pct)

    # 最终因子值（取负）
    df['factor'] = -df['rank_pct']

    # 处理无穷值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 严格按要求恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 严格输出四列并去无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

