# Alpha299因子 - factor_201
# 原始因子编号: 201
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_201(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha112因子：基于volume和high的Z-score差值与low+close对数的滚动回归贝塔因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 10,    # Z-score计算窗口
        'regression_window': 10 # 滚动回归窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    zscore_window = window_sizes['zscore_window']
    regression_window = window_sizes['regression_window']

    df = data_df.copy()

    # 确保数据完整性
    if not all(col in df.columns for col in ['volume', 'high', 'low', 'close']):
        raise ValueError("数据缺少必要列: volume, high, low, close")

    # 按symbol分组处理
    grouped = df.groupby('symbol')

    # 步骤1-2: 计算log(volume)和high的滚动Z-score
    def rolling_zscore(series, window):
        # 避免除以0，并处理inf/nan
        series_clean = series.replace([np.inf, -np.inf], np.nan)
        rolling_mean = series_clean.rolling(window=window, min_periods=w).mean()
        rolling_std = series_clean.rolling(window=window, min_periods=w).std()
        return (series_clean - rolling_mean) / (rolling_std + 1e-8)

    # 对volume取对数，避免log(0)或log(负数)
    df['log_volume'] = np.log(np.abs(df['volume']) + 1e-8)
    df['z_log_volume'] = grouped['log_volume'].transform(lambda x: rolling_zscore(x, zscore_window))

    df['z_high'] = grouped['high'].transform(lambda x: rolling_zscore(x, zscore_window))

    # 步骤3: 构建X1 = Z(log(volume)) - Z(high)
    df['X1'] = df['z_log_volume'] - df['z_high']

    # 步骤4-5: 计算log(low + close)作为Y
    df['low_close_sum'] = df['low'] + df['close']
    # 对low + close取对数，避免log(0)或log(负数)
    df['log_low_close'] = np.log(np.abs(df['low_close_sum']) + 1e-8)

    # 步骤6: 计算滚动回归贝塔系数
    def rolling_beta(group):
        X = group['X1'].values
        Y = group['log_low_close'].values
        index = group.index
        betas = np.full(len(X), np.nan)

        for i in range(len(X)):
            if i < regression_window - 1:
                continue  # 保持默认的nan
            X_window = X[max(0, i-regression_window+1):i+1]
            Y_window = Y[max(0, i-regression_window+1):i+1]

            # 安全检查：确保窗口内有有效数据
            valid_mask = ~np.isnan(X_window) & ~np.isnan(Y_window)
            if not np.any(valid_mask):
                continue

            X_valid = X_window[valid_mask]
            Y_valid = Y_window[valid_mask]

            if len(X_valid) < 2:  # 至少需要两个数据点进行回归
                continue

            # 检查X_valid是否为常数，避免回归失败
            if np.std(X_valid) < 1e-8:
                 continue # X为常数，无法进行回归，保持nan

            try:
                X_valid_const = sm.add_constant(X_valid)  # 添加截距项
                model = sm.OLS(Y_valid, X_valid_const).fit()
                betas[i] = model.params[1]  # 取斜率（beta系数）
            except Exception as e:
                # 回归失败，保持nan
                continue

        return pd.Series(betas, index=index)

    # 使用apply并显式设置索引对齐
    df['factor'] = grouped.apply(rolling_beta).droplevel(0)

    # 处理无效值（回归结果可能出现inf/nan）
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 日期时间格式转换
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

