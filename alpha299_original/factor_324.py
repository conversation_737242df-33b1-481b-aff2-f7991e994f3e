# Alpha299因子 - factor_324
# 原始因子编号: 324
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_324(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha99因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 指定单一基础列，此处设为None
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n_rank': 8,           # ts_rank窗口
        'n_cov1': 6,           # ts_cov(6)窗口
        'n_cov2': 10,          # ts_cov(10)窗口
        'n_regbeta': 14        # ts_regbeta窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n_rank = window_sizes['n_rank']
    n_cov1 = window_sizes['n_cov1']
    n_cov2 = window_sizes['n_cov2']
    n_regbeta = window_sizes['n_regbeta']

    # 检查必要列是否存在
    required_columns = ['volume', 'close', 'high', 'open', 'amount', 'trade_date', 'symbol']
    if not all(col in data_df.columns for col in required_columns):
        raise ValueError(f"数据中缺少必要的列: {', '.join(required_columns)}")
    
    df = data_df.copy()
    
    # 动态生成vwap列（如果不存在）
    if 'vwap' not in df.columns:
        df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
    
    # 确保时间排序
    df['time'] = pd.to_datetime(df['time'])
    df.sort_values(['symbol', 'time'], inplace=True)
    df.reset_index(drop=True, inplace=True)
    
    # 步骤1: 计算sqrt(|volume|)
    df['T1'] = df['volume'].abs().pow(0.5)
    
    # 步骤2: ts_rank(close, 8)
    df['T2'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=n_rank, min_periods=w).rank(method='average', ascending=False) / (n_rank + 1e-8)
    )
    
    # 步骤3: gp_max(T2, vwap)
    df['T3'] = df[['T2', 'vwap']].max(axis=1)
    
    # 步骤4: ts_cov(6, T1, T3)
    def rolling_cov(group):
        # 填充可能导致cov计算异常的inf/nan
        group['T1_clean'] = group['T1'].replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill').fillna(0)
        group['T3_clean'] = group['T3'].replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill').fillna(0)
        return group['T1_clean'].rolling(window=n_cov1, min_periods=w).cov(group['T3_clean'])
    df['X1'] = df.groupby('symbol').apply(rolling_cov).reset_index(level=[0,1], drop=True)
    
    # 步骤5: ts_cov(10, high, vwap)
    def rolling_cov_high_vwap(group):
        # 填充可能导致cov计算异常的inf/nan
        group['high_clean'] = group['high'].replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill').fillna(0)
        group['vwap_clean'] = group['vwap'].replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill').fillna(0)
        return group['high_clean'].rolling(window=n_cov2, min_periods=w).cov(group['vwap_clean'])
    df['T4'] = df.groupby('symbol').apply(rolling_cov_high_vwap).reset_index(level=[0,1], drop=True)
    
    # 步骤6: gp_min(open, close)
    df['T5'] = df[['open', 'close']].min(axis=1)
    
    # 步骤7: ts_regbeta(T4, T5, 14)
    def rolling_beta(group):
        # 填充可能导致beta计算异常的inf/nan
        group['T4_clean'] = group['T4'].replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill').fillna(0)
        group['T5_clean'] = group['T5'].replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        cov = group['T5_clean'].rolling(window=n_regbeta, min_periods=w).cov(group['T4_clean'])
        var = group['T4_clean'].rolling(window=n_regbeta, min_periods=w).var(ddof=1)
        
        # 处理var为0的情况
        beta = cov / (var + 1e-8)
        
        # 替换计算结果中的inf/nan为0
        beta = beta.replace([np.inf, -np.inf], np.nan).fillna(0)
        return beta
        
    df['X2'] = df.groupby('symbol').apply(rolling_beta).reset_index(level=[0,1], drop=True)
    
    # 步骤8: gp_min(X1, X2)
    df['factor'] = df[['X1', 'X2']].min(axis=1)
    
    # 步骤9: 替换无穷大为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    
    # 转换日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    return output_df

