# Alpha299因子 - factor_30
# 原始因子编号: 30
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_30(
    data_df,
    w: int | None = 9,  # 差分周期参数（9天）
    uni_col: str | None = None  # 未使用单一基础列，设为None
) -> pd.DataFrame:
    """
    修复后的Alpha53因子：
    1. 计算 (CLOSE-LOW)-(HIGH-CLOSE) / (CLOSE-LOW)
    2. 计算9期差分
    3. 取差分结果的累积排名并取负（避免未来数据泄露）
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 9        # DELTA差分窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']       # DELTA差分窗口

    df = data_df.copy()

    # 按symbol分组处理数据
    def process_group(group: pd.DataFrame) -> pd.DataFrame:
        # 确保数据按时间排序
        group = group.sort_values('trade_date')

        # 计算分子部分 (CLOSE-LOW)-(HIGH-CLOSE)
        numerator = (group['close'] - group['low']) - (group['high'] - group['close'])
        # 计算分母部分 (CLOSE-LOW)
        denominator = group['close'] - group['low']

        # 计算比值（注意分母为0的情况会导致无穷大/NaN）
        # 添加1e-8处理分母为0的情况
        ratio = numerator / (denominator + 1e-8)

        # 计算delta_window期差分（当前值 - delta_window天前的值）
        diff_ratio = ratio - ratio.shift(delta_window)

        # 将差分结果保存为临时列
        group['diff_ratio'] = diff_ratio

        # 使用累积排名替代全局rankdata，避免未来数据泄露
        # 累积排名会自动处理NaN，但为了稳健，可以先对diff_ratio进行有限值检查
        group['diff_ratio'] = group['diff_ratio'].replace([np.inf, -np.inf], np.nan)
        group['factor'] = -group['diff_ratio'].expanding().rank(method='average')

        return group

    # 按symbol分组处理
    df = df.groupby('symbol', group_keys=False).apply(process_group)

    # 严格按要求处理日期格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择并重命名列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

