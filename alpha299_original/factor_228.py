# Alpha299因子 - factor_228
# 原始因子编号: 228
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_228(data_df, w=10, uni_col=None):
    """
    计算Alpha150因子

    参数:
        data_df: 输入数据，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
                 'volume', 'amount', 'open_interest', 'industry_name']列
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一基础数据列名称（默认'close'）

    返回:
        DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'rank_window': 20,  # ts_rank窗口
        'cov_window': 10,   # ts_cov窗口
        'zscore_window1': 10,  # ts_zscore窗口1
        'zscore_window2': 20   # ts_zscore窗口2
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n1 = window_sizes['rank_window']    # ts_rank窗口和ts_zscore窗口
    n2 = window_sizes['cov_window']     # ts_cov窗口和ts_zscore窗口

    # 确保输入为DataFrame副本进行计算
    df = data_df.copy()

    # 计算vwap（假设数据中没有vwap列）
    df['vwap'] = df.groupby('symbol')['amount'].cumsum() / (df.groupby('symbol')['volume'].cumsum() + 1e-8)

    # 1. 计算成交量的绝对值平方根
    df['sqrt_volume'] = np.sqrt(np.abs(df['volume']))

    # 2. 计算收盘价的滚动排名（ts_rank）
    def _ts_rank(x, n):
        return x.rolling(window=n, min_periods=w) \
                .apply(lambda y: (n - pd.Series(y).rank(method='first', ascending=False).values[0] + 1) / (n + 1e-8), raw=False)

    df['ts_rank_close'] = df.groupby('symbol')['close'].transform(lambda x: _ts_rank(x, n1))

    # 3. 取ts_rank和vwap的较大值
    df['gp_max_value'] = df[['ts_rank_close', 'vwap']].max(axis=1)

    # 4. 计算sqrt_volume和gp_max_value的滚动协方差
    def _ts_cov(x1, x2, n):
        # 增加对常数序列的处理，协方差为0
        cov_series = x1.rolling(window=n, min_periods=w).cov(x2)
        # 进一步处理可能出现的NaN或inf
        cov_series = cov_series.replace([np.inf, -np.inf], np.nan)
        return cov_series

    df['ts_cov_value'] = df.groupby('symbol').apply(
        lambda group: _ts_cov(group['sqrt_volume'], group['gp_max_value'], n2)
    ).reset_index(level=0, drop=True)

    # 5. 对协方差结果进行滚动标准化
    def _ts_zscore(x, n):
        rolling_mean = x.rolling(window=n, min_periods=w).mean()
        rolling_std = x.rolling(window=n, min_periods=w).std(ddof=1)
        # 避免除以0
        zscore = (x - rolling_mean) / (rolling_std + 1e-8)
        # 处理可能出现的NaN或inf
        zscore = zscore.replace([np.inf, -np.inf], np.nan)
        return zscore

    df['ts_zscore_X1'] = df.groupby('symbol')['ts_cov_value'].transform(lambda x: _ts_zscore(x, n2))

    # 6. 对收盘价进行滚动标准化
    df['ts_zscore_close'] = df.groupby('symbol')['close'].transform(lambda x: _ts_zscore(x, n1))

    # 7. 对标准化后的收盘价进行截面排名
    df['rank_T4'] = df.groupby('time')['ts_zscore_close'].transform(lambda x: x.rank(method='average', ascending=True))

    # 8. 计算最终因子值
    df['factor'] = df['ts_zscore_X1'] + df['rank_T4']

    # 处理无穷大值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

