# Alpha299因子 - factor_38
# 原始因子编号: 38
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_38(data_df, w: int | None = None, uni_col: str | None = None):
    # 检查必要列是否存在
    required_columns = ['high', 'low', 'close', 'volume', 'trade_date', 'symbol', 'time']
    for col in required_columns:
        if col not in data_df.columns:
            raise ValueError(f"Missing required column: {col}")

    df = data_df.copy()

    # 按symbol和时间排序
    df = df.sort_values(by=['symbol', 'time'])

    # 计算VWAP（成交量加权平均价）
    df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3.0
    # 确保volume不为负数
    df['volume'] = df['volume'].apply(lambda x: max(0, x))
    df['cum_tp_vol'] = df.groupby('symbol')['typical_price'].transform(lambda x: (x * df['volume']).cumsum())
    df['cum_vol'] = df.groupby('symbol')['volume'].transform(lambda x: x.cumsum())
    # 避免除以零
    df['vwap'] = df['cum_tp_vol'] / (df['cum_vol'] + 1e-8)

    # 计算40日平均成交量 (ADV40)
    df['ADV40'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(40, min_periods=w).mean())

    # 计算 (HIGH + LOW)/2
    df['hl2'] = (df['high'] + df['low']) / 2.0

    # 第一部分：((HIGH+LOW)/2 + HIGH) - (VWAP + HIGH) = (HIGH + LOW)/2 - VWAP
    df['part1'] = df['hl2'] - df['vwap']

    # 线性衰减处理：20.0451期（取整为20）
    def linear_decay(x):
        n = len(x)
        if n < 1:
            return np.nan
        weights = np.arange(1, n + 1)
        # 避免除以零
        sum_weights = weights.sum()
        if sum_weights == 0:
            return np.nan
        return np.dot(x, weights) / sum_weights

    decay_n1 = int(20.0451)
    df['part1_decay'] = df.groupby('symbol')['part1'].transform(lambda x: x.rolling(decay_n1, min_periods=w).apply(linear_decay, raw=False))

    # 第一部分的排名（使用滚动窗口内排名）
    df['part1_rank'] = df.groupby('symbol')['part1_decay'].transform(lambda x: x.rolling(decay_n1, min_periods=w).rank(pct=True))

    # 第二部分：(HIGH+LOW)/2 与 ADV40 的相关性（3.1614期，取整为3）
    corr_n = int(3.1614)
    # 使用apply处理分组，并在corr计算后处理可能的nan/inf
    df['corr'] = df.groupby('symbol').apply(lambda g: g['hl2'].rolling(corr_n, min_periods=w).corr(g['ADV40'])).reset_index(level=0, drop=True)
    # 填充corr结果中的nan和inf为0
    df['corr'] = df['corr'].fillna(0).replace([np.inf, -np.inf], 0)


    # 线性衰减处理相关性结果：5.64125期（取整为6）
    decay_n2 = int(5.64125)
    df['part2_decay'] = df.groupby('symbol')['corr'].transform(lambda x: x.rolling(decay_n2, min_periods=w).apply(linear_decay, raw=False))

    # 第二部分的排名（使用滚动窗口内排名）
    df['part2_rank'] = df.groupby('symbol')['part2_decay'].transform(lambda x: x.rolling(decay_n2, min_periods=w).rank(pct=True))

    # 取两部分的较小排名值
    df['factor'] = df[['part1_rank', 'part2_rank']].min(axis=1)

    # 日期和时间格式处理（修正数据类型转换）
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

