# Alpha299因子 - factor_245
# 原始因子编号: 245
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_245(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha_173因子：对过去5期成交量和最高价的滚动协方差进行截面排名

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的字段
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数，此处设为None因为涉及多列计算
    """
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 5    # 滚动协方差窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    cov_window = window_sizes['cov_window']
    
    # 检查必要列是否存在
    required_cols = ['volume', 'high']
    if not all(col in data_df.columns for col in required_cols):
        raise KeyError(f"数据中缺少必要列: {required_cols}")

    df = data_df.copy()

    # 对volume和high进行inf, nan处理，避免影响后续计算
    df['volume'] = df['volume'].replace([np.inf, -np.inf], np.nan)
    df['high'] = df['high'].replace([np.inf, -np.inf], np.nan)

    # 按symbol分组计算滚动协方差
    def calc_rolling_cov(group):
        # 使用min_periods=w，允许窗口内数据不足cov_window时计算
        # cov计算本身对nan有一定处理，但为了稳健，可以考虑对输入进行填充或过滤
        # 这里选择直接计算，依赖pandas的cov对nan的处理
        cov_result = group['volume'].rolling(window=cov_window, min_periods=w).cov(group['high'])
        # 对协方差结果中的inf和nan进行处理，填充为0，因为协方差为0表示无线性关系
        return cov_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    df['I_1'] = df.groupby('symbol').apply(calc_rolling_cov).reset_index(level=0, drop=True)

    # 截面排名（每个时间点独立计算）
    # rank函数默认对nan不参与排名，并返回nan，符合要求
    df['factor'] = df.groupby('time')['I_1'].rank()

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去除NaN
    # 注意：这里的dropna()会移除factor为NaN的行，这是符合要求的
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

