# Alpha299因子 - factor_196
# 原始因子编号: 196
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_196(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha102因子

    参数:
        data_df: 输入DataFrame，包含必要的列
        w: 基础窗口参数，用于推导其他窗口参数
        uni_col: 用于指定单一基础数据列（本因子不使用）

    返回:
        包含因子值的DataFrame
    """
    # 窗口配置
    window_configs = {
        'n1': 8.0,    # w，ts_max窗口
        'n2': 16.0,   # 2*w，ts_corr窗口
        'n3': 5.0     # int(5*w/8) = int(5*8/8) = 5，delta窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']

    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'amount', 'close', 'volume']
    if not all(col in data_df.columns for col in required_cols):
        raise ValueError(f"数据中缺少必要列: {required_cols}")

    df = data_df.copy()

    # 动态计算vwap（如果不存在）
    if 'vwap' not in df.columns:
        # 按symbol和trade_date分组计算vwap
        df['vwap'] = df.groupby(['symbol', 'trade_date']).apply(
            lambda group: (group['close'] * group['volume']).cumsum() / (group['volume'].cumsum() + 1e-8)
        ).reset_index(level=[0, 1], drop=True)


    # 1. 计算T1 = ts_max(amount, n1)（使用实际窗口大小）
    df['T1'] = df.groupby('symbol')['amount'].transform(
        lambda x: x.rolling(window=n1, min_periods=w).max()
    )

    # 2. 计算T2 = sqrt(|T1|)
    df['T2'] = np.sqrt(np.abs(df['T1']))

    # 3. 计算X1 = ts_corr(n2, vwap, T2)（使用实际窗口大小）
    def rolling_corr_safe(group):
        # 计算滚动相关性
        corr_result = group['vwap'].rolling(window=n2, min_periods=w).corr(group['T2'])
        # 填充NaN和Inf为0
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    df['X1'] = df.groupby('symbol').apply(rolling_corr_safe).reset_index(level=0, drop=True)

    # 4. 计算X2 = delta(close, n3)（使用实际窗口大小）
    df['X2'] = df.groupby('symbol')['close'].transform(
        lambda x: x - x.shift(n3)
    )

    # 5. 计算Alpha102 = gp_min(X1, X2)
    df['factor'] = df[['X1', 'X2']].min(axis=1)

    # 6. 处理无穷大值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 格式化日期时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

