# Alpha299因子 - factor_124
# 原始因子编号: 124
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_124(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha因子：基于ADX指标的技术分析因子
    
    参数:
        data_df (pd.DataFrame): 输入数据
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为14天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列（此处不适用，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'dm_tr_window': 14,  # DM和TR的滚动求和窗口
        'dx_ma_window': 6    # DX的移动平均窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    dm_tr_window = window_sizes['dm_tr_window']
    dx_ma_window = window_sizes['dx_ma_window']

    df = data_df.copy()

    # Sort by symbol and time to ensure correct rolling calculations
    df = df.sort_values(by=['symbol', 'time'])

    # Step 1: Calculate LD and HD
    df['LD'] = df.groupby('symbol')['low'].shift(1) - df['low']
    df['HD'] = df['high'] - df.groupby('symbol')['high'].shift(1)

    # Step 2: Calculate True Range (TR)
    df['prev_close'] = df.groupby('symbol')['close'].shift(1)
    df['TR1'] = df['high'] - df['low']
    df['TR2'] = (df['high'] - df['prev_close']).abs()
    df['TR3'] = (df['low'] - df['prev_close']).abs()
    df['TR'] = df[['TR1', 'TR2', 'TR3']].max(axis=1)

    # Step 3: Calculate +DM and -DM
    df['plus_DM'] = np.where(
        (df['LD'] > 0) & (df['LD'] > df['HD']),
        df['LD'],
        0
    )
    df['minus_DM'] = np.where(
        (df['HD'] > 0) & (df['HD'] > df['LD']),
        df['HD'],
        0
    )

    # Step 4: Rolling sums for DM and TR
    df['sum_plus_DM'] = df.groupby('symbol')['plus_DM'].rolling(window=dm_tr_window, min_periods=w).sum().reset_index(level=0, drop=True)
    df['sum_minus_DM'] = df.groupby('symbol')['minus_DM'].rolling(window=dm_tr_window, min_periods=w).sum().reset_index(level=0, drop=True)
    df['sum_TR'] = df.groupby('symbol')['TR'].rolling(window=dm_tr_window, min_periods=w).sum().reset_index(level=0, drop=True)

    # Step 5: Calculate +DI and -DI
    df['plus_DI'] = (df['sum_plus_DM'] / (df['sum_TR'] + 1e-8)) * 100
    df['minus_DI'] = (df['sum_minus_DM'] / (df['sum_TR'] + 1e-8)) * 100

    # Step 6: Calculate DX term
    df['DI_diff'] = (df['plus_DI'] - df['minus_DI']).abs()
    df['DI_sum'] = df['plus_DI'] + df['minus_DI']
    df['DX'] = (df['DI_diff'] / (df['DI_sum'] + 1e-8)) * 100


    # Step 7: 移动平均of DX
    df['factor'] = df.groupby('symbol')['DX'].rolling(window=dx_ma_window, min_periods=w).mean().reset_index(level=0, drop=True)

    # Clean up intermediate columns
    df.drop(['LD', 'HD', 'prev_close', 'TR1', 'TR2', 'TR3', 'TR', 'plus_DM', 'minus_DM', 'sum_plus_DM', 'sum_minus_DM', 'sum_TR', 'plus_DI', 'minus_DI', 'DI_diff', 'DI_sum', 'DX'], axis=1, inplace=True)

    # Format date and time columns
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # Final output
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

