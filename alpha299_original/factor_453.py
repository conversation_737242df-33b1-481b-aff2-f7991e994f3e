# Alpha299因子 - factor_453
# 原始因子编号: 453
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_453(data_df, w: int | None = 60, uni_col: str | None = 'close'):
    """
    计算希尔伯特变换瞬时相位 (Hilbert Transform InPhase Component, HT_INPHASE)

    参数:
    data_df: 输入数据DataFrame
    w: 初始化窗口期，确保滤波器和平滑计算趋于稳定，默认为60。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    uni_col: 用于计算的价格列，默认为'close'
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}
    # 定义所有窗口的基准值
    window_configs = {
        'hilbert_window': 60
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    hilbert_window = window_sizes['hilbert_window']

    # 创建数据副本
    df = data_df.copy()

    # 确保时间格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 希尔伯特变换的常数参数
    a = 0.0962
    b = 0.5769
    alpha_smooth = 0.2

    # 计算弧度到角度的转换系数
    rad2deg = 180.0 / (4.0 * np.arctan(1.0))

    # 按symbol分组计算因子
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 按时间排序
        group = group.sort_values('time')

        # 初始化各种变量和数组
        price = group[uni_col].values
        n = len(price)

        # 初始化输出数组和中间变量
        smoothed_price = np.zeros(n)
        detrender = np.zeros(n)
        ht_inphase = np.zeros(n)
        ht_quad = np.zeros(n)
        ji = np.zeros(n)
        jq = np.zeros(n)

        # 初始化状态变量
        period = np.ones(n) * 20  # 初始周期设为20
        i2 = np.zeros(n)
        q2 = np.zeros(n)
        re = np.zeros(n)
        im = np.zeros(n)

        # 计算4周期加权移动平均
        for i in range(3, n):
            # 价格平滑: 4周期加权移动平均
            smoothed_price[i] = 0.1 * (4 * price[i] + 3 * price[i-1] + 2 * price[i-2] + price[i-3])

        # 主循环：计算希尔伯特变换和相关组件
        for i in range(6, n):
            # 动态调整因子
            adjusted_period_factor = 0.075 * period[i-1] + 0.54

            # 计算去趋势分量
            detrender[i] = (a * smoothed_price[i] + b * smoothed_price[i-2] -
                           b * smoothed_price[i-4] - a * smoothed_price[i-6]) * adjusted_period_factor

            # 计算瞬时相位分量 (HT_INPHASE)
            if i >= 9:  # 确保有足够的历史数据
                ht_inphase[i] = detrender[i-3]

            # 计算正交分量 (HT_QUAD)
            if i >= 12:  # 确保有足够的历史数据
                ht_quad[i] = (a * detrender[i] + b * detrender[i-2] -
                             b * detrender[i-4] - a * detrender[i-6]) * adjusted_period_factor

            # 计算jI分量
            if i >= 18:  # 确保有足够的历史数据
                ji[i] = (a * ht_inphase[i] + b * ht_inphase[i-2] -
                        b * ht_inphase[i-4] - a * ht_inphase[i-6]) * adjusted_period_factor

            # 计算jQ分量
            if i >= 18:  # 确保有足够的历史数据
                jq[i] = (a * ht_quad[i] + b * ht_quad[i-2] -
                        b * ht_quad[i-4] - a * ht_quad[i-6]) * adjusted_period_factor

            # 平滑相位分量 I2, Q2
            if i >= 18:  # 确保有足够的历史数据
                i2[i] = alpha_smooth * (ht_inphase[i] - jq[i]) + (1 - alpha_smooth) * i2[i-1]
                q2[i] = alpha_smooth * (ht_quad[i] + ji[i]) + (1 - alpha_smooth) * q2[i-1]

            # 计算实部和虚部
            if i >= 19:  # 确保有足够的历史数据
                re[i] = alpha_smooth * (i2[i] * i2[i-1] + q2[i] * q2[i-1]) + (1 - alpha_smooth) * re[i-1]
                im[i] = alpha_smooth * (i2[i] * q2[i-1] - q2[i] * i2[i-1]) + (1 - alpha_smooth) * im[i-1]

            # 计算主周期
            if i >= 19:  # 确保有足够的历史数据
                # 避免除以零或接近零的情况
                denominator = (re[i] + 1e-8) * (rad2deg + 1e-8)
                if denominator != 0:
                    period_raw = 360.0 / (np.arctan(im[i] / (re[i] + 1e-8)) * (rad2deg + 1e-8))
                else:
                    period_raw = period[i-1] # 保持前一个周期值

                # 周期边界限制与调整
                upper_limit = 1.5 * period[i-1]
                lower_limit = 0.67 * period[i-1]
                period_candidate = min(max(period_raw, lower_limit), upper_limit)
                period_candidate = min(max(period_candidate, 6), 50)  # 绝对上下限

                # 周期平滑
                period[i] = alpha_smooth * period_candidate + (1 - alpha_smooth) * period[i-1]

        # 创建结果DataFrame
        result_df = pd.DataFrame({
            'trade_date': group['trade_date'],
            'time': group['time'],
            'symbol': symbol,
            'factor': ht_inphase
        })

        # 添加到结果列表
        result_dfs.append(result_df)

    # 合并所有结果
    result = pd.concat(result_dfs, ignore_index=True)

    # 仅保留有效数据（去除初始化窗口期的数据）
    result = result.dropna(subset=['factor'])

    # 将日期和时间转换回字符串格式
    result['trade_date'] = result['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result['time'] = result['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回最终结果
    return result[['trade_date', 'time', 'symbol', 'factor']]

