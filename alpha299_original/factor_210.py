# Alpha299因子 - factor_210
# 原始因子编号: 210
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_210(data_df, w: int | None = 10, uni_col: str | None = None):
    # 窗口配置
    window_configs = {
        'n1': 10.0,   # w，10 days for ts_corr
        'n2': 16.0    # int(1.6*w) = int(1.6*10) = 16，16 days for ts_pctchg
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']

    # 按symbol分组，并对每个组按时间排序
    df = data_df.copy()
    df = df.sort_values(['symbol', 'time'])

    # 计算abs(volume)
    df['abs_volume'] = df['volume'].abs()

    # 计算ts_pctchg(open_price, n2)（使用实际窗口大小）
    # 避免除以0
    df['open_pctchg'] = (df['open'] - df.groupby('symbol')['open'].shift(n2)) / (df.groupby('symbol')['open'].shift(n2).replace(0, 1e-8) + 1e-8)

    # 计算ts_corr(n1, abs_volume, open_pctchg)（使用实际窗口大小）
    # 对每个symbol分组，计算滚动相关系数
    def rolling_corr(group):
        # 确保输入到corr的数据没有inf或nan，或者在计算后处理
        # 这里选择后处理，将corr结果的nan和inf填0
        group['corr'] = group['abs_volume'].rolling(window=n1, min_periods=w).corr(group['open_pctchg'])
        group['corr'] = group['corr'].fillna(0).replace([float('inf'), -float('inf')], 0)
        return group

    df = df.groupby('symbol').apply(rolling_corr).reset_index(drop=True)

    # 重命名factor列
    df.rename(columns={'corr': 'factor'}, inplace=True)

    # 调整日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

