# Alpha299因子 - factor_505
# 原始因子编号: 505
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_505(data_df, w: int | None = None, uni_col: str | None = 'close'):
    """
    计算能量潮(On Balance Volume, OBV)指标

    参数:
    data_df: DataFrame, 包含交易数据
    w: int | None, 窗口期参数，对于OBV指标不需要窗口期，设为None
    uni_col: str | None, 用于比较价格变化的列，默认使用'close'收盘价

    返回:
    DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']列的DataFrame
    """
    # 创建数据副本
    df = data_df.copy()

    # 确保时间列为日期时间格式，用于排序
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol和time排序，确保按时间顺序计算OBV
    df = df.sort_values(['symbol', 'time'])

    # 计算每个symbol的价格变化方向
    # 增加对uni_col列中可能存在的inf/nan的处理，虽然diff本身会产生nan，但确保输入干净
    df['price_change'] = df.groupby('symbol')[uni_col].diff()

    # 根据价格变化方向确定成交量的符号
    # 增加对volume列中可能存在的inf/nan的处理，将其视为0
    df['signed_volume'] = np.where(df['price_change'] > 0, df['volume'].fillna(0).replace([np.inf, -np.inf], 0),
                                 np.where(df['price_change'] < 0, -df['volume'].fillna(0).replace([np.inf, -np.inf], 0), 0))

    # 计算OBV：按symbol分组，对signed_volume进行累计求和
    # 对于每个symbol的第一个数据点，OBV值等于该点的成交量
    # cumsum本身会处理nan，但我们已经在signed_volume中处理了inf/nan
    df['factor'] = df.groupby('symbol')['signed_volume'].transform(lambda x: x.fillna(0).cumsum())

    # 对于每个symbol的第一个点，将OBV设为该点的成交量
    # 增加对volume列中可能存在的inf/nan的处理，将其视为0
    first_points = df.groupby('symbol').apply(lambda x: x.index[0]).values
    df.loc[first_points, 'factor'] = df.loc[first_points, 'volume'].fillna(0).replace([np.inf, -np.inf], 0)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择所需列并返回
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

