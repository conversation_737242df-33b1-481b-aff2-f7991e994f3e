# Alpha299因子 - factor_1
# 原始因子编号: 1
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_1(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha101因子：(CLOSE - OPEN) / ((HIGH - LOW) + 0.001)
    """
    # 检查必要列是否存在
    required_cols = ['open', 'high', 'low', 'close']
    if not all(col in data_df.columns for col in required_cols):
        raise KeyError(f"输入数据缺少必要列: {required_cols}")

    df = data_df.copy()

    # 计算分子和分母，分母增加一个小的常数避免除以零
    df['factor'] = (df['close'] - df['open']) / (df['high'] - df['low'] + 0.001 + 1e-8)

    # 严格遵循日期时间格式要求
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d').dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S').dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    # 处理可能产生的inf和nan值，将inf替换为nan，然后去除nan
    result_df['factor'] = result_df['factor'].replace([np.inf, -np.inf], np.nan)
    result_df = result_df.dropna(subset=['factor'])

    return result_df

