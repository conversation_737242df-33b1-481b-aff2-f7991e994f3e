# Alpha299因子 - factor_401
# 原始因子编号: 401
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_401(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha 93因子

    参数:
    - data_df: 输入的DataFrame
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为14天（regbeta_window窗口）。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，此因子使用多列，设为None

    返回:
    - 包含trade_date, time, symbol和factor列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'regbeta_window': 14,
        'regres_window': 10
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    regbeta_window = window_sizes['regbeta_window']
    regres_window = window_sizes['regres_window']

    # 复制数据以避免修改原始数据
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组计算因子
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        group = group.sort_values('time')

        # 创建临时DataFrame用于存储计算结果
        temp_df = group.copy()

        # 1. 计算最低价对最高价在过去14个周期内的滚动回归贝塔系数
        def rolling_beta(y, x, window):
            """计算滚动回归的贝塔系数"""
            # 初始化结果数组
            betas = np.full(len(x), np.nan)

            # 对每个窗口计算贝塔
            for i in range(window-1, len(x)):
                x_window = x[i-window+1:i+1]
                y_window = y[i-window+1:i+1]

                # 过滤掉NaN和无穷值
                valid_mask = np.isfinite(x_window) & np.isfinite(y_window)
                x_valid = x_window[valid_mask]
                y_valid = y_window[valid_mask]

                # 计算协方差和方差
                if len(x_valid) >= 2:  # 确保有足够的数据点
                    try:
                        cov_matrix = np.cov(x_valid, y_valid, ddof=1)
                        if cov_matrix.size >= 4:  # 确保协方差矩阵大小正确
                            cov_xy = cov_matrix[0, 1]
                            var_x = cov_matrix[0, 0]
                            # 避免除以零
                            if var_x > 1e-8:
                                betas[i] = cov_xy / (var_x + 1e-8)
                    except:
                        # 如果计算协方差或方差失败，保持NaN
                        pass

            return betas

        # 计算ts_regbeta(high, low, regbeta_window)
        high_values = group['high'].values
        low_values = group['low'].values
        beta_values = rolling_beta(low_values, high_values, regbeta_window)
        temp_df['regbeta'] = beta_values

        # 2. 计算close / regbeta
        # 避免除以零或接近零的值
        temp_df['x1'] = temp_df['close'] / (temp_df['regbeta'] + 1e-8)
        # 将无穷大值替换为NaN
        temp_df['x1'] = temp_df['x1'].replace([np.inf, -np.inf], np.nan)


        # 3. 计算收盘价对X1在过去regres_window个周期内的滚动回归残差
        def rolling_residuals(y, x, window):
            """计算滚动回归的残差"""
            # 初始化结果数组
            residuals = np.full(len(x), np.nan)

            # 对每个窗口计算残差
            for i in range(window-1, len(x)):
                x_window = x[i-window+1:i+1]
                y_window = y[i-window+1:i+1]

                # 过滤掉NaN和无穷值
                valid_mask = np.isfinite(x_window) & np.isfinite(y_window)
                x_valid = x_window[valid_mask]
                y_valid = y_window[valid_mask]

                if len(x_valid) >= 2:  # 确保有足够的数据点进行回归
                    try:
                        # 使用线性回归
                        slope, intercept, r_value, p_value, std_err = stats.linregress(x_valid, y_valid)
                        # 计算残差
                        # 使用窗口最后一个x值进行预测
                        predicted = slope * x_window[-1] + intercept
                        residuals[i] = y_window[-1] - predicted
                    except:
                        # 如果回归失败，保持NaN
                        pass

            return residuals

        # 计算ts_regres(x1, close, regres_window)
        x1_values = temp_df['x1'].values
        close_values = temp_df['close'].values

        # 计算残差
        residuals = rolling_residuals(close_values, x1_values, regres_window)

        # 将结果存入DataFrame
        result_df = pd.DataFrame({
            'trade_date': temp_df['trade_date'],
            'time': temp_df['time'],
            'symbol': symbol,
            'factor': residuals
        })

        # 将无穷大值替换为NaN
        result_df['factor'] = result_df['factor'].replace([np.inf, -np.inf], np.nan)

        result_dfs.append(result_df)

    # 合并所有结果
    if result_dfs:
        result = pd.concat(result_dfs, ignore_index=True)
    else:
        result = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    # 恢复日期格式为字符串
    result['trade_date'] = result['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result['time'] = result['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 删除NaN值并返回最终结果
    # 保持真实的缺值情况，不填充0
    return result

