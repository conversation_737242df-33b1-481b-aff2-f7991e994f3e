# Alpha299因子 - factor_55
# 原始因子编号: 55
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_55(data_df, w: int | None = 10, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 10        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    """
    计算Alpha 142因子（三重排名乘积因子）

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'close', 'volume']等列
        w (int | None): 可调时间窗口基准参数（默认10）
        uni_col (str | None): 单一基础数据列参数（默认None，因涉及多列）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 严格验证输入数据列
    required_cols = ['symbol', 'trade_date', 'time', 'close', 'volume']
    if not all(col in data_df.columns for col in required_cols):
        raise KeyError(f"输入数据缺少必要列: {required_cols}")

    # 时序排序处理
    df = data_df.sort_values(by=['symbol', 'trade_date', 'time']).copy()

    # 1. 收盘价10期时序排名
    df['tsr_c'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=w, min_periods=w).rank(pct=True)
    )

    # 2. 横截面排名取负
    df['r1'] = df.groupby('trade_date')['tsr_c'].transform(
        lambda x: x.rank(pct=True) * -1
    )

    # 3. 二阶差分计算
    df['delta_1'] = df.groupby('symbol')['close'].transform(lambda x: x.diff(1))
    df['delta_2'] = df.groupby('symbol')['delta_1'].transform(lambda x: x.diff(1))

    # 4. 二阶差分横截面排名
    df['r2'] = df.groupby('trade_date')['delta_2'].transform(
        lambda x: x.rank(pct=True)
    )

    # 5. 成交量比率计算
    df['ma_vol'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=2*w, min_periods=w).mean()
    )
    # 保护分母不为0
    df['vol_ratio'] = df['volume'] / (df['ma_vol'] + 1e-8)
    # 保护inf和nan
    df['vol_ratio'] = df['vol_ratio'].replace([float('inf'), -float('inf')], float('nan'))


    # 6. 成交量比率5期时序排名
    df['tsr_vr'] = df.groupby('symbol')['vol_ratio'].transform(
        lambda x: x.rolling(window=w*2, min_periods=w).rank(pct=True)
    )

    # 7. 成交量比率横截面排名
    df['r3'] = df.groupby('trade_date')['tsr_vr'].transform(
        lambda x: x.rank(pct=True)
    )

    # 8. 最终因子计算
    df['factor'] = df['r1'] * df['r2'] * df['r3']
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 9. 格式化日期时间字段
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 10. 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

