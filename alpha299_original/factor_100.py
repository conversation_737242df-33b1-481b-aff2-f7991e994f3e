# Alpha299因子 - factor_100
# 原始因子编号: 100
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_100(data_df, w: int | None = 20, uni_col: str | None = 'close'):
    """
    计算Alpha 135因子：滞后价格相对强度EMA因子
    参数:
        data_df: 输入DataFrame，包含['symbol', 'trade_date', 'time', 'close']等列
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一基础列（默认为'close'）
    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'lag_window': 20,       # 价格滞后窗口
        'ema_span': 39          # EMA计算span (2*w - 1)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    lag_window = window_sizes['lag_window']     # 价格滞后窗口
    ema_span = window_sizes['ema_span']         # EMA计算span

    df = data_df.copy()

    # 1. 计算价格相对强度RelStr,t = Close_t / Close_{t-lag_window}
    # 按symbol分组，先计算滞后
    df['close_lag'] = df.groupby('symbol')['close'].transform(lambda x: x.shift(lag_window))
    # 处理分母为0的情况，当Close_{t-lag_window}=0时，RelStr,t为NaN
    # 避免除以0或接近0的数产生inf
    df['rel_str'] = df['close'] / (df['close_lag'] + 1e-8)
    # 原始逻辑保留，当滞后价格为0或NaN时，相对强度为NaN
    df['rel_str'] = np.where((df['close_lag'] == 0) | (df['close_lag'].isna()), 1e-8, df['rel_str'])

    # 2. 取RelStr,t的1期滞后值RelStr,t-1
    df['rel_str_lag1'] = df.groupby('symbol')['rel_str'].transform(lambda x: x.shift(1))

    # 3. 计算RelStr,t-1的EMA
    # EMA计算本身对NaN有处理，但为了稳健，确保输入没有inf
    df['rel_str_lag1'] = df['rel_str_lag1'].replace([np.inf, -np.inf], np.nan)
    df['ema'] = df.groupby('symbol')['rel_str_lag1'].transform(
        lambda x: x.ewm(span=ema_span, adjust=False, min_periods=w).mean()
    )

    # 4. 将±∞替换为NaN (EMA计算结果也可能出现inf，虽然不常见)
    df['factor'] = df['ema'].replace([np.inf, -np.inf], np.nan)

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择并重命名列
    # 原始逻辑保留，dropna()
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

