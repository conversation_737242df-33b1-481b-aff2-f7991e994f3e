# Alpha299因子 - factor_242
# 原始因子编号: 242
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_242(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha170因子：基于close标准差和volume最大值的复合Z-score标准差因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'ts_std_window': 5,        # close标准差窗口
        'ts_max_window': 10,       # volume最大值窗口 (2 * 5)
        'zscore_window': 14,       # Z-score计算窗口
        'final_std_window': 14     # 最终标准差窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    ts_std_window = window_sizes['ts_std_window']
    ts_max_window = window_sizes['ts_max_window']
    zscore_window = window_sizes['zscore_window']
    final_std_window = window_sizes['final_std_window']

    df = data_df.copy()

    # 按symbol分组计算
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 步骤1: 计算I_1 = ts_std(close,ts_std_window)
    # 滚动标准差可能为0，但后续用于Z-score的分母，因此需要保护
    df['I1'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=ts_std_window, min_periods=w).std()
    )

    # 步骤2: 计算I_2 = ts_max(volume,ts_max_window)
    df['I2'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=ts_max_window, min_periods=w).max()
    )

    # 步骤3: I_3 = gp_max(I1, I2)
    df['I3'] = df[['I1', 'I2']].max(axis=1)

    # 步骤4: I_4 = I1 (与I1相同)
    df['I4'] = df['I1']

    # 步骤5: 计算I3的Z-score
    def rolling_zscore(x, window):
        # 保护分母，避免除以0
        mean = x.rolling(window=window, min_periods=w).mean()
        std = x.rolling(window=window, min_periods=w).std()
        return (x - mean) / (std + 1e-8)

    df['I3_z'] = df.groupby('symbol')['I3'].transform(lambda x: rolling_zscore(x, zscore_window))
    df['I4_z'] = df.groupby('symbol')['I4'].transform(lambda x: rolling_zscore(x, zscore_window))

    # 步骤6: I_5 = I3_z + I4_z
    df['I5'] = df['I3_z'] + df['I4_z']

    # 步骤7: 最终ts_std(I5,final_std_window)
    # 最终的标准差可能为0，但这是最终结果，不需要特别保护分母
    df['factor'] = df.groupby('symbol')['I5'].transform(
        lambda x: x.rolling(window=final_std_window, min_periods=w).std()
    )

    # 处理无穷值和NaN值（滚动标准差可能产生NaN）
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

