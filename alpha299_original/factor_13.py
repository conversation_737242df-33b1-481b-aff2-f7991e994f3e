# Alpha299因子 - factor_13
# 原始因子编号: 13
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_13(data_df, w: int | None = 2, uni_col: str | None = None):
    """
    计算Alpha因子：基于成交量变化排名与价格变动排名的相关性因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'diff_window': 2,       # 差分窗口
        'corr_window': 6,       # 相关系数计算窗口
        'rank_window': 6        # 排名计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    diff_window = window_sizes['diff_window']
    corr_window = window_sizes['corr_window']
    rank_window = window_sizes['rank_window']

    # 检查必要列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'open', 'close', 'volume']
    for col in required_columns:
        if col not in data_df.columns:
            raise ValueError(f"数据中缺少必要列: {col}")

    # 按symbol和time排序
    data_df = data_df.sort_values(['symbol', 'time']).copy()

    # 计算log(VOLUME)的差分
    # 保护 log(0)
    log_volume = np.log(data_df['volume'] + 1e-8)
    log_volume_diff = log_volume.groupby(data_df['symbol']).diff(diff_window)
    data_df['log_volume_diff'] = log_volume_diff

    # 使用滚动窗口计算排名（修复未来数据泄露）
    data_df['rank_diff'] = data_df.groupby('symbol')['log_volume_diff'].transform(
        lambda x: x.rolling(window=rank_window, min_periods=w).rank()
    )

    # 计算价格变动比率 (CLOSE - OPEN)/OPEN
    # 保护除以0
    data_df['price_change_ratio'] = (data_df['close'] - data_df['open']) / (data_df['open'] + 1e-8)
    # 使用滚动窗口计算排名（修复未来数据泄露）
    data_df['rank_ratio'] = data_df.groupby('symbol')['price_change_ratio'].transform(
        lambda x: x.rolling(window=rank_window, min_periods=w).rank()
    )

    # 定义滚动相关系数计算函数
    def calc_rolling_corr(group, window, min_periods):
        # 计算滚动窗口内的Pearson相关系数
        # 对结果进行保护，将NaN和Inf填充为0
        corr_result = group['rank_diff'].rolling(window=window, min_periods=w).corr(group['rank_ratio'])
        return corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    # 应用滚动相关系数计算
    data_df['correlation'] = data_df.groupby('symbol').apply(calc_rolling_corr, window=corr_window, min_periods=w).reset_index(level=0, drop=True)

    # 取负作为最终因子值
    data_df['factor'] = -data_df['correlation']

    # 恢复日期和时间格式为字符串
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去除无效值
    # 保留原始的dropna，因为factor本身可能因为rolling window不足而产生NaN
    result_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

