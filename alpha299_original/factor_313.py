# Alpha299因子 - factor_313
# 原始因子编号: 313
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_313(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha80因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 指定单一基础列，此处设为None
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 6,   # 第一个回归beta窗口
        'n2': 14   # 第二个回归beta窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']

    df = data_df.copy()

    # 按symbol和time排序
    df = df.sort_values(by=['symbol', 'time'])

    # 计算第一个贝塔：volume对close的回归贝塔
    def compute_beta(X, Y, window):
        # 添加对X和Y中inf/nan的处理，以及对var为0的处理
        valid_mask = ~(X.isnull() | Y.isnull() | np.isinf(X) | np.isinf(Y))
        X_valid = X[valid_mask]
        Y_valid = Y[valid_mask]

        cov = X_valid.rolling(window=window, min_periods=w).cov(Y_valid)
        var = X_valid.rolling(window=window, min_periods=w).var()

        # 避免除以0，并将inf/nan结果替换为0
        beta = cov / (var + 1e-8)
        beta.replace([np.inf, -np.inf], np.nan, inplace=True)
        return beta

    df['beta1'] = df.groupby('symbol').apply(
        lambda group: compute_beta(group['volume'], group['close'], n1)
    ).reset_index(level=0, drop=True)

    # 应用tanh变换
    df['tanh_beta1'] = np.tanh(df['beta1'])

    # 计算截面排名
    # rank函数默认会处理nan，这里不需要额外处理
    df['rank_tanh'] = df.groupby('time')['tanh_beta1'].rank()

    # 计算第二个贝塔：close对low的回归贝塔
    df['beta2'] = df.groupby('symbol').apply(
        lambda group: compute_beta(group['close'], group['low'], n2)
    ).reset_index(level=0, drop=True)

    # 取负数
    df['neg_beta2'] = -df['beta2']

    # 计算因子
    df['factor'] = df['rank_tanh'] + df['neg_beta2']

    # 处理无穷大值 (compute_beta中已经处理了一部分，这里再做一次整体处理)
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

