# Alpha299因子 - factor_12
# 原始因子编号: 12
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_12(
    data_df,
    w: int = 1,
    uni_col: str = 'close'
):
    """
    计算Alpha29因子，基于复杂的时间序列嵌套运算。

    参数:
        data_df (pd.DataFrame): 输入数据，包含字段['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
                               'volume', 'amount', 'open_interest', 'industry_name']
        w (int): 核心可调参数（天数），用于联动其他时间窗口参数，默认值为5。
        uni_col (str): 单一基础数据列，默认为'close'.

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果。
    """
    # 窗口配置
    window_configs = {
        'n1': 5.0,   # w，Δ(CLOSE-1,5) 对应的5期差分
        'n2': 2.0,   # int(2*w/5) = int(2*5/5) = 2，ts_min窗口
        'n3': 1.0,   # int(1*w/5) = int(1*5/5) = 1，sum窗口
        'n4': 6.0,   # int(6*w/5) = int(6*5/5) = 6，delay的6期
        'n5': 5.0    # w，ts_rank窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']
    
    # 生成RETURNS列（对数收益率）
    df = data_df.copy()
    # 保护 log(x) - log(x.shift(1))，确保输入log的值大于0
    df['returns'] = df.groupby('symbol')['close'].transform(lambda x: np.log(x + 1e-8) - np.log(x.shift(1) + 1e-8))

    # 步骤1: 计算Δ(CLOSE-1, n1)（使用实际窗口大小）
    df['delta'] = df.groupby('symbol')[uni_col].transform(lambda x: x.diff(n1))

    # 步骤2: -rank(Δ(...)) - 使用滚动窗口n5（使用实际窗口大小）
    df['rank1'] = -df.groupby('symbol')['delta'].transform(
        lambda x: x.rolling(window=n5, min_periods=w).rank(pct=True, method='average')
    )

    # 步骤3: rank(rank(...)) - 使用滚动窗口n5（使用实际窗口大小）
    df['rank2'] = df.groupby('symbol')['rank1'].transform(
        lambda x: x.rolling(window=n5, min_periods=w).rank(pct=True, method='average')
    )

    # 步骤4: ts_min(..., n2)（使用实际窗口大小）
    df['ts_min'] = df.groupby('symbol')['rank2'].transform(lambda x: x.rolling(window=n2, min_periods=w).min())

    # 步骤5: sum(..., n3)（使用实际窗口大小）
    df['sum_val'] = df.groupby('symbol')['ts_min'].transform(lambda x: x.rolling(window=n3, min_periods=w).sum())

    # 步骤6: log(...)
    # 保护 log(df['sum_val'])，确保输入log的值大于0
    df['log_val'] = np.log(df['sum_val'] + 1e-8)

    # 步骤7: scale(...) - 使用滚动窗口n5的标准化（使用实际窗口大小）
    # 保护 rolling().std()，避免除以0
    rolling_std = df.groupby('symbol')['log_val'].transform(
        lambda x: x.rolling(window=n5, min_periods=w).std()
    )
    df['scale_val'] = (df['log_val'] - df.groupby('symbol')['log_val'].transform(
        lambda x: x.rolling(window=n5, min_periods=w).mean()
    )) / (rolling_std + 1e-8)

    # 步骤8: rank(rank(...)) - 使用滚动窗口n5（使用实际窗口大小）
    df['rank3'] = df.groupby('symbol')['scale_val'].transform(
        lambda x: x.rolling(window=n5, min_periods=w).rank(pct=True, method='average')
    )
    df['rank4'] = df.groupby('symbol')['rank3'].transform(
        lambda x: x.rolling(window=n5, min_periods=w).rank(pct=True, method='average')
    )

    # 步骤9: product(..., 1) (窗口为1，等价于原值)
    df['product_val'] = df['rank4']  # 直接使用原值

    # 步骤10: min(...,5)
    df['min_val'] = df['product_val'].clip(upper=5)

    # 步骤11: ts_rank(delay(-RETURNS, n4), n5)（使用实际窗口大小）
    df['delayed_returns'] = -df.groupby('symbol')['returns'].transform(lambda x: x.shift(n4))
    df['ts_rank'] = df.groupby('symbol')['delayed_returns'].rolling(window=n5, min_periods=w).rank(
        pct=True, method='average'
    ).reset_index(level=[0], drop=True)

    # 合并两部分结果
    df['factor'] = df['min_val'] + df['ts_rank']

    # 日期和时间格式处理
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 输出结果
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

