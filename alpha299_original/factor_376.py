# Alpha299因子 - factor_376
# 原始因子编号: 376
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_376(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha38因子

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，因为该因子使用volume和vwap两个不同列，所以设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'regbeta_window': 8    # 滚动回归贝塔系数窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    regbeta_window = window_sizes['regbeta_window']

    df = data_df.copy()

    # 确保数据包含必要的列
    required_cols = ['volume', 'amount', 'close']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"输入数据缺少必要列: {col}")

    # 计算vwap (成交量加权平均价)
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 1. 计算成交量的绝对值的自然对数
    df['log_volume'] = np.log(np.abs(df['volume']) + 1e-8)

    # 2. 计算vwap对log_volume在过去8个周期内的滚动回归贝塔系数
    def rolling_regbeta(group, y_col, x_col, window):
        """计算滚动回归贝塔系数"""
        y = group[y_col]
        x = group[x_col]

        # 初始化结果列
        result = np.full(len(y), np.nan)

        # 滚动窗口计算贝塔系数
        for i in range(window - 1, len(y)):
            y_window = y.iloc[i-window+1:i+1]
            x_window = x.iloc[i-window+1:i+1]

            # 检查是否有足够的有效数据
            if y_window.isna().any() or x_window.isna().any():
                continue

            # 检查窗口内x是否为常数，避免方差为0
            if x_window.nunique() <= 1:
                continue

            # 计算协方差和方差
            cov = np.cov(x_window, y_window)[0, 1]
            var_x = np.var(x_window)

            # 计算贝塔系数，避免除以零
            result[i] = cov / (var_x + 1e-8)

        return result

    # 按symbol分组计算滚动回归贝塔系数
    df['factor'] = np.nan
    for symbol, group in df.groupby('symbol'):
        beta_values = rolling_regbeta(group, 'vwap', 'log_volume', regbeta_window)
        df.loc[group.index, 'factor'] = beta_values

    # 3. 将无穷大值替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 处理日期格式
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 恢复日期格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()
    return result_df

