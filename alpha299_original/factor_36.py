# Alpha299因子 - factor_36
# 原始因子编号: 36
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_36(data_df, w: int | None = 3, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 3,          # ts_rank窗口1
        'n2': 12,         # ts_rank窗口2  
        'n3': 18,         # 相关性窗口
        'n4': 4,          # decay_linear窗口1
        'n5': 16,         # ts_rank窗口3
        'n6': 16,         # decay_linear窗口2
        'n7': 4,          # ts_rank窗口4
        'adv_window': 180 # ADV计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']
    n6 = window_sizes['n6']
    n7 = window_sizes['n7']
    adv_window = window_sizes['adv_window']

    df = data_df.copy()

    # 计算VWAP：按symbol和trade_date分组，累积amount和volume
    df['cum_amount'] = df.groupby(['symbol', 'trade_date'])['amount'].transform('cumsum')
    df['cum_volume'] = df.groupby(['symbol', 'trade_date'])['volume'].transform('cumsum')
    # 避免除以零
    df['vwap'] = df['cum_amount'] / (df['cum_volume'] + 1e-8)

    # 计算ADV：按symbol分组，计算移动平均
    df['adv180'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=adv_window, min_periods=w).mean())

    # 第一部分计算
    # ts_rank(close, n1)
    df['close_rank'] = df.groupby('symbol')['close'].transform(lambda x: x.rolling(n1, min_periods=w).rank())

    # ts_rank(adv180, n2)
    df['adv180_rank'] = df.groupby('symbol')['adv180'].transform(lambda x: x.rolling(n2, min_periods=w).rank())

    # correlation between close_rank and adv180_rank over n3
    # 处理滚动相关性可能出现的nan或inf
    df['corr'] = df.groupby('symbol').apply(
        lambda group: group['close_rank'].rolling(n3, min_periods=w).corr(group['adv180_rank'])
    ).reset_index(level=0, drop=True)

    # 填充相关性计算中的nan和inf为0
    df['corr'] = df['corr'].fillna(0).replace([np.inf, -np.inf], 0)

    # decay_linear over n4：线性加权平均
    weights1 = np.arange(1, n4 + 1)
    df['decay_linear1'] = df.groupby('symbol')['corr'].transform(
        lambda x: x.rolling(n4, min_periods=w).apply(
            lambda window: (window * weights1).sum() / (weights1.sum() + 1e-8) if len(window) == n4 else np.nan
        )
    )

    # ts_rank over n5
    df['part1'] = df.groupby('symbol')['decay_linear1'].transform(lambda x: x.rolling(n5, min_periods=w).rank())

    # 第二部分计算
    # (low + open) - 2*vwap
    df['diff'] = df['low'] + df['open'] - 2 * df['vwap']

    # 修复点：使用滚动窗口排序替代全局排序
    # 原问题代码：df.groupby('symbol')['diff'].transform('rank')
    df['rank_diff'] = df.groupby('symbol')['diff'].transform(
        lambda x: x.rolling(n5, min_periods=w).rank()  # 使用n5窗口参数
    )

    # square
    df['rank_diff_sq'] = df['rank_diff'] ** 2

    # decay_linear over n6
    weights2 = np.arange(1, n6 + 1)
    df['decay_linear2'] = df.groupby('symbol')['rank_diff_sq'].transform(
        lambda x: x.rolling(n6, min_periods=w).apply(
            lambda window: (window * weights2).sum() / (weights2.sum() + 1e-8) if len(window) == n6 else np.nan
        )
    )

    # ts_rank over n7
    df['part2'] = df.groupby('symbol')['decay_linear2'].transform(lambda x: x.rolling(n7, min_periods=w).rank())

    # 取两部分最大值
    df['factor'] = df['part1'].combine(df['part2'], max)

    # 格式化日期和时间列
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

