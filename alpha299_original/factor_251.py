# Alpha299因子 - factor_251
# 原始因子编号: 251
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_251(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha_182因子

    参数:
        data_df: 输入数据DataFrame，包含必需的列
        w: 核心窗口参数（默认6），用于推导其他窗口
        uni_col: 单一基础列参数（此处设为None因为因子涉及多列）
    """
    # 定义所有窗口的基准值
    window_configs = {
        'beta_window': 6,      # beta计算窗口
        'resid_window': 9      # 残差计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    beta_window = window_sizes['beta_window']      # beta计算窗口
    resid_window = window_sizes['resid_window']    # 残差计算窗口

    # 必需列检查
    required_cols = ['symbol', 'trade_date', 'time', 'volume', 'close', 'low']
    if not all(col in data_df.columns for col in required_cols):
        raise ValueError(f"输入数据缺少必要列: {required_cols}")

    df = data_df.copy()

    # 步骤1: 计算abs(volume)
    df['I1'] = df['volume'].abs()

    # 步骤2: 计算sqrt(abs(close))
    df['I2'] = np.sqrt(df['close'].abs())

    # 步骤3: 计算ts_regbeta(I1, I2, beta_window)
    def calc_beta(group):
        """分组计算滚动beta值（无截距）"""
        # 使用滚动协方差和方差计算beta
        # 增加对var为0的处理
        cov = group['I1'].rolling(window=beta_window, min_periods=w).cov(group['I2'])
        var = group['I1'].rolling(window=beta_window, min_periods=w).var()
        beta = cov / (var + 1e-8)
        # 替换可能出现的inf或nan为0
        beta = beta.replace([np.inf, -np.inf], np.nan).fillna(0)
        return beta

    df['I3'] = df.groupby('symbol').apply(calc_beta).reset_index(level=0, drop=True)

    # 步骤4: 应用sigmoid函数
    # np.exp(-df['I3']) 可能产生inf，但1/(1+inf)会变成0，这是可以接受的
    df['I4'] = 1 / (1 + np.exp(-df['I3']))
    # 确保结果不是nan或inf
    df['I4'] = df['I4'].replace([np.inf, -np.inf], np.nan).fillna(0)


    # 步骤5: 计算ts_regres(I4, low, resid_window)
    def calc_resid(group):
        """分组计算滚动残差（无截距）"""
        # 使用滚动协方差和方差计算beta
        # 增加对var为0的处理
        cov = group['I4'].rolling(window=resid_window, min_periods=w).cov(group['low'])
        var = group['I4'].rolling(window=resid_window, min_periods=w).var()
        beta = cov / (var + 1e-8)
        # 替换可能出现的inf或nan为0
        beta = beta.replace([np.inf, -np.inf], np.nan).fillna(0)
        # 计算残差
        resid = group['low'] - beta * group['I4']
        return resid

    df['I5'] = df.groupby('symbol').apply(calc_resid).reset_index(level=0, drop=True)

    # 步骤6: 应用arctan并处理无穷值
    # arctan的定义域是实数，不需要对输入进行处理，但输出可能因为输入是inf/nan而产生inf/nan
    df['factor'] = np.arctan(df['I5'])
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 格式恢复
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建最终结果
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

