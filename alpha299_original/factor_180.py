# Alpha299因子 - factor_180
# 原始因子编号: 180
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_180(data_df, w: int | None = 20, uni_col: str | None = 'close'):
    """
    计算Alpha 84因子：20日OBV变种累积因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'close', 'volume']等列
        w (int | None): 滚动窗口大小（天数），默认20
        uni_col (str | None): 基础计算列，默认'close'

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 窗口配置
    window_configs = {
        'w': 20.0  # 滚动窗口大小
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    actual_w = window_sizes['w']
    
    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'close', 'volume']
    if not all(col in data_df.columns for col in required_cols):
        raise ValueError(f"输入数据缺少必要列: {required_cols}")

    df = data_df.copy()

    # 按symbol分组并按时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 计算前一日收盘价
    df['prev_close'] = df.groupby('symbol')['close'].shift(1)

    # 计算带符号成交量
    # 确保volume不为负数，虽然通常不会
    df['volume_protected'] = df['volume'].apply(lambda x: max(0, x))

    conditions = [
        (df['close'] > df['prev_close']),
        (df['close'] < df['prev_close'])
    ]
    choices = [df['volume_protected'], -df['volume_protected']]
    df['signed_vol'] = np.select(conditions, choices, default=0)

    # 计算滚动求和（使用实际窗口大小）
    # 滚动求和本身对inf/nan有一定鲁棒性，但为了安全，可以在计算前对signed_vol进行一次inf/nan处理
    df['signed_vol'] = df['signed_vol'].replace([np.inf, -np.inf], np.nan)

    df['factor'] = df.groupby('symbol')['signed_vol'].transform(
        lambda x: x.rolling(window=actual_w, min_periods=w).sum()
    )

    # 处理无效值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 保留原始数据中close为NaN的位置的因子值为NaN
    df.loc[df['close'].isna(), 'factor'] = np.nan

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    return output_df

