# Alpha299因子 - factor_406
# 原始因子编号: 406
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_406(
    data_df,
    w: int | None = None,  # 这里的因子没有核心可调天数参数
    uni_col: str | None = None,  # 因子计算使用了多列数据，不只是单一列
    start_value: float = 0,  # 初始SAR值和方向
    offset_on_reverse: float = 0,  # 趋势反转时的调整百分比
    acceleration_init_long: float = 0.02,  # 上涨趋势的初始加速因子
    acceleration_long: float = 0.02,  # 上涨趋势的加速因子步长
    acceleration_max_long: float = 0.20,  # 上涨趋势的最大加速因子
    acceleration_init_short: float = 0.02,  # 下跌趋势的初始加速因子
    acceleration_short: float = 0.02,  # 下跌趋势的加速因子步长
    acceleration_max_short: float = 0.20  # 下跌趋势的最大加速因子
):
    """
    计算抛物线转向指标扩展版 (Parabolic Stop and Reverse Extended, SAREXT)

    参数:
    - data_df: 输入数据DataFrame
    - w: 无时间窗口参数，设为None
    - uni_col: 无单一列参数，设为None
    - start_value: 初始SAR值和方向，0表示自动检测
    - offset_on_reverse: 趋势反转时的调整百分比
    - acceleration_init_long: 上涨趋势的初始加速因子
    - acceleration_long: 上涨趋势的加速因子步长
    - acceleration_max_long: 上涨趋势的最大加速因子
    - acceleration_init_short: 下跌趋势的初始加速因子
    - acceleration_short: 下跌趋势的加速因子步长
    - acceleration_max_short: 下跌趋势的最大加速因子

    返回:
    - 包含因子值的DataFrame
    """
    # 参数一致性调整
    acceleration_init_long = max(0.0, acceleration_init_long) # Ensure non-negative
    acceleration_long = max(0.0, acceleration_long) # Ensure non-negative
    acceleration_max_long = max(acceleration_init_long, acceleration_max_long) # Ensure max is not less than init
    acceleration_max_long = max(acceleration_long, acceleration_max_long) # Ensure max is not less than step

    acceleration_init_short = max(0.0, acceleration_init_short) # Ensure non-negative
    acceleration_short = max(0.0, acceleration_short) # Ensure non-negative
    acceleration_max_short = max(acceleration_init_short, acceleration_max_short) # Ensure max is not less than init
    acceleration_max_short = max(acceleration_short, acceleration_max_short) # Ensure max is not less than step

    offset_on_reverse = max(0.0, offset_on_reverse) # Ensure non-negative

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 转换日期和时间格式为datetime
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 确保数据按symbol和时间排序
    df = df.sort_values(['symbol', 'time'])

    # 创建结果DataFrame
    result_df = pd.DataFrame()

    # 按品种分组计算SAREXT
    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')

        # 初始化变量
        n = len(group)
        if n < 2:  # 需要至少两个数据点
            continue

        # 创建结果数组
        sarext = np.full(n, np.nan)

        # 获取高低价数据
        high = group['high'].values
        low = group['low'].values

        # Handle potential NaN/Inf in high/low
        if np.any(np.isnan(high)) or np.any(np.isinf(high)) or np.any(np.isnan(low)) or np.any(np.isinf(low)):
             # Skip this symbol or handle appropriately, for now, skip
             continue

        # Initialize first SAR value and trend direction
        init_trend = 0  # 1表示上涨，-1表示下跌
        sar_initial = 0.0

        # 确定初始趋势和SAR值
        if start_value > 0:
            # 指定初始为上涨趋势
            init_trend = 1
            sar_initial = start_value
            ep = high[0]  # 第一个有效周期的最高价
            af = acceleration_init_long
        elif start_value < 0:
            # 指定初始为下跌趋势
            init_trend = -1
            sar_initial = abs(start_value)
            ep = low[0]  # 第一个有效周期的最低价
            af = acceleration_init_short
        else:
            # 自动检测初始趋势
            up_move = high[1] - high[0]
            down_move = low[0] - low[1]

            plus_dm = max(0, up_move) if up_move > down_move and up_move > 0 else 0
            minus_dm = max(0, down_move) if down_move > up_move and down_move > 0 else 0

            if minus_dm > plus_dm:
                init_trend = -1
                sar_initial = high[0]  # 前一周期最高价
                ep = low[1]
                af = acceleration_init_short
            else:
                init_trend = 1
                sar_initial = low[0]  # 前一周期最低价
                ep = high[1]
                af = acceleration_init_long

        # Set the first output value
        if init_trend == 1:
            sarext[1] = sar_initial
        else:
            sarext[1] = -sar_initial

        # For next period calculation of SAR
        if init_trend == 1:
            sar_next = sar_initial + af * (ep - sar_initial)
            sar_next = min(sar_next, low[0], low[1])
        else:
            sar_next = sar_initial + af * (ep - sar_initial)
            sar_next = max(sar_next, high[0], high[1])

        # Initialize current state
        sar_current = sar_next
        ep_prev = ep
        af_prev = af
        trend_prev = init_trend

        # Iterate to calculate the remaining SAREXT values
        for i in range(2, n):
            # Check for trend reversal
            if trend_prev == 1:  # Current is uptrend
                if low[i] <= sar_current:  # Trend reverses to downtrend
                    trend_current = -1
                    sar_current = ep_prev
                    sar_current = max(sar_current, high[i-1], high[i])  # Boundary limit
                    sar_current = sar_current * (1 + offset_on_reverse)  # Apply reversal offset
                    sarext[i] = -sar_current
                    af_current = acceleration_init_short  # Reset AF
                    ep_current = low[i]  # New extreme price
                else:  # Continue uptrend
                    trend_current = 1
                    sarext[i] = sar_current  # Output value is current SAR
                    if high[i] > ep_prev:  # Made a new high
                        ep_current = high[i]
                        af_current = min(af_prev + acceleration_long, acceleration_max_long)
                    else:  # Did not make a new high
                        ep_current = ep_prev
                        af_current = af_prev
            else:  # Current is downtrend
                if high[i] >= sar_current:  # Trend reverses to uptrend
                    trend_current = 1
                    sar_current = ep_prev
                    sar_current = min(sar_current, low[i-1], low[i])  # Boundary limit
                    sar_current = sar_current * (1 - offset_on_reverse)  # Apply reversal offset
                    sarext[i] = sar_current
                    af_current = acceleration_init_long  # Reset AF
                    ep_current = high[i]  # New extreme price
                else:  # Continue downtrend
                    trend_current = -1
                    sarext[i] = -sar_current  # Output value is negative of current SAR
                    if low[i] < ep_prev:  # Made a new low
                        ep_current = low[i]
                        af_current = min(af_prev + acceleration_short, acceleration_max_short)
                    else:  # Did not make a new low
                        ep_current = ep_prev
                        af_current = af_prev

            # Calculate the next period's SAR value
            sar_next = sar_current + af_current * (ep_current - sar_current)

            # Apply boundary limits
            if trend_current == 1:  # Uptrend
                sar_next = min(sar_next, low[i-1], low[i])
            else:  # Downtrend
                sar_next = max(sar_next, high[i-1], high[i])

            # Update state variables
            sar_current = sar_next
            ep_prev = ep_current
            af_prev = af_current
            trend_prev = trend_current

        # Create result DataFrame for this symbol
        symbol_result = group.copy()
        symbol_result['factor'] = sarext

        # Concatenate to total results
        result_df = pd.concat([result_df, symbol_result])

    # Check if we have any results before processing
    if not result_df.empty:
        # Select required columns and remove missing values
        result_df = result_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

        # Restore date and time format to string
        result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
        result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    else:
        # If no results, return empty DataFrame with correct columns
        result_df = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    return result_df

