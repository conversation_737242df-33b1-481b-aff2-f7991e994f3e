# Alpha299因子 - factor_358
# 原始因子编号: 358
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_358(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha199因子

    Alpha199 = ts_zscore(VOLUME, 20) + ts_zscore(TS_MEAN(TS_COV(8, AMOUNT, VWAP), 6), 20)

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 不适用于此因子，因为需要多列数据

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 20,      # 滚动标准化窗口
        'cov_window': 8,          # 协方差窗口
        'mean_window': 6          # 均值窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    zscore_window = window_sizes['zscore_window']
    cov_window = window_sizes['cov_window']
    mean_window = window_sizes['mean_window']

    # 复制数据以避免修改原始数据
    df = data_df.copy()

    # 确保时间格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 计算VWAP (成交量加权平均价)
    # 避免除以0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 步骤1: 计算TS_COV(8, AMOUNT, VWAP)
    def rolling_cov(group):
        # 确保输入到corr/cov的数据没有inf或nan，或者在计算后处理结果
        # 这里选择后处理，将nan/inf替换为0，因为协方差为0表示没有线性关系
        cov_result = group['amount'].rolling(window=cov_window, min_periods=w).cov(group['vwap'])
        return cov_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    df['cov_amount_vwap'] = df.groupby('symbol').apply(rolling_cov).reset_index(level=0, drop=True)

    # 步骤2: 计算TS_MEAN(TS_COV(8, AMOUNT, VWAP), 6)
    # mean操作本身对nan有处理，但为了稳健，确保输入没有inf
    df['mean_cov'] = df.groupby('symbol')['cov_amount_vwap'].transform(
        lambda x: x.replace([np.inf, -np.inf], np.nan).rolling(window=mean_window, min_periods=w).mean()
    )

    # 步骤3: 对VOLUME进行20周期滚动标准化
    def ts_zscore(series, window):
        """计算时间序列的z-score标准化"""
        # 确保输入到zscore的数据没有inf或nan
        cleaned_series = series.replace([np.inf, -np.inf], np.nan)
        mean = cleaned_series.rolling(window=window, min_periods=w).mean()
        std = cleaned_series.rolling(window=window, min_periods=w).std()
        # 避免除以0，并将结果中的inf/nan替换为0
        zscore_result = (cleaned_series - mean) / (std + 1e-8)
        return zscore_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    df['volume_zscore'] = df.groupby('symbol')['volume'].transform(
        lambda x: ts_zscore(x, zscore_window)
    )

    # 步骤4: 对步骤2的结果进行20周期滚动标准化
    df['mean_cov_zscore'] = df.groupby('symbol')['mean_cov'].transform(
        lambda x: ts_zscore(x, zscore_window)
    )

    # 步骤5: 将步骤3和步骤4的结果相加
    df['factor'] = df['volume_zscore'] + df['mean_cov_zscore']

    # 步骤6: 将无穷大和负无穷大替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期格式
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

