# Alpha299因子 - factor_45
# 原始因子编号: 45
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_45(data_df, w: int | None = 10, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 10        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    """
    计算Alpha 105因子
    参数:
        w: 滚动窗口大小（单位：天），默认10
        uni_col: 本因子不依赖单一基础列，故设为None
    """
    # 参数校验
    required_cols = ['open', 'volume']
    if not all(col in data_df.columns for col in required_cols):
        raise ValueError(f"缺少必要列: {required_cols}")

    df = data_df.copy()

    # 按symbol分组处理时间序列
    def compute_ts_zscore(group):
        # 滚动Z-score标准化 (窗口w)
        # 避免除以0，给std加一个小的常数
        group['open_zscore'] = (group['open'] - group['open'].rolling(window=w, min_periods=w).mean()) / (group['open'].rolling(window=w, min_periods=w).std() + 1e-8)
        group['volume_zscore'] = (group['volume'] - group['volume'].rolling(window=w, min_periods=w).mean()) / (group['volume'].rolling(window=w, min_periods=w).std() + 1e-8)
        return group

    df = df.groupby('symbol', group_keys=False).apply(compute_ts_zscore)

    # 横截面排名 (按时间点)
    def cs_rank(group):
        # 横截面百分比排名
        group['open_zscore'] = group['open_zscore'].replace([np.inf, -np.inf], np.nan)
        group['volume_zscore'] = group['volume_zscore'].replace([np.inf, -np.inf], np.nan)
        group['open_rank'] = group['open_zscore'].rank(pct=True)
        group['volume_rank'] = group['volume_zscore'].rank(pct=True)
        return group

    df = df.groupby(['trade_date', 'time'], group_keys=False).apply(cs_rank)

    # 滚动相关系数 (窗口w)
    def rolling_corr(group):
        # 计算滚动pearson相关系数
        open_rank = group['open_rank'].values
        volume_rank = group['volume_rank'].values

        group['corr'] = np.nan
        n = len(group)
        for i in range(w, n):
            # 计算窗口内的相关系数
            # 检查窗口内是否有足够的非NaN值进行计算
            window_open = open_rank[i-w:i]
            window_volume = volume_rank[i-w:i]

            if np.sum(~np.isnan(window_open)) >= w and np.sum(~np.isnan(window_volume)) >= w:
                try:
                    corr, _ = pearsonr(window_open, window_volume)
                    # 替换inf和nan为0，因为corr的结果应该在[-1, 1]之间
                    if np.isfinite(corr):
                        group.at[group.index[i], 'corr'] = corr
                    else:
                        group.at[group.index[i], 'corr'] = 0.0
                except ValueError:
                    # 如果窗口内数据导致pearsonr计算错误（例如常数序列），则相关系数为0
                    group.at[group.index[i], 'corr'] = 0.0
            else:
                 group.at[group.index[i], 'corr'] = 0.0 # 如果数据不足，相关系数为0

        return group

    df = df.groupby('symbol', group_keys=False).apply(rolling_corr)

    # 取负数并处理无效值
    df['factor'] = -df['corr']
    # 滚动相关系数的结果理论上在[-1, 1]之间，但为了安全，再次处理inf和nan
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    # 由于滚动相关系数在数据不足时可能为NaN，这里不填充，保留真实的缺失值

    # 保持原始日期时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去除无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

