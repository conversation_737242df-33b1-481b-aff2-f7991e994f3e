# Alpha299因子 - factor_120
# 原始因子编号: 120
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_120(data_df, w: int | None = 20, uni_col: str | None = 'volume'):
    """
    计算Alpha 168因子：成交量相对强度因子
    
    参数:
        data_df: 输入的DataFrame，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一基础数据列（默认'volume'）
        
    返回:
        包含因子结果的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'ma_window': 20  # 移动平均窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    ma_window = window_sizes['ma_window']

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'volume']
    for col in required_cols:
        if col not in data_df.columns:
            raise ValueError(f"Missing required column: {col}")
    
    df = data_df.copy()
    
    # 计算移动平均
    # 对volume进行处理，避免出现inf或nan影响rolling mean
    df['volume_processed'] = df['volume'].replace([np.inf, -np.inf], np.nan)
    df['ma'] = df.groupby('symbol')['volume_processed'].transform(
        lambda x: x.rolling(window=ma_window, min_periods=w).mean()
    )
    
    # 计算因子值
    # 对分母进行处理，避免除以0
    df['factor'] = -df['volume_processed'] / (df['ma'].replace(0, 1e-8) + 1e-8)
    
    # 替换±∞为NaN
    df['factor'].replace([np.inf, -np.inf], np.nan, inplace=True)
    
    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 选择需要的列并删除NaN
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    return result_df

