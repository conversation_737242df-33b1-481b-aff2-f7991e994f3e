# Alpha299因子 - factor_392
# 原始因子编号: 392
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_392(data_df, w: int | None = 2, uni_col: str | None = None):
    """
    计算Alpha75因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的字段
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为11天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列（本因子涉及多列，设为None）

    返回:
        pd.DataFrame: 包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'vwap_window': 20,      # VWAP计算窗口
        'cov_window': 11,       # ts_cov窗口
        'regbeta_window': 15,   # ts_regbeta窗口
        'mean_window': 4,       # ts_mean窗口
        'zscore_window': 11,    # ts_zscore窗口
        'delta_window': 2,      # delta差值窗口
        'std_window': 8,        # ts_std窗口
        'min_window': 9         # ts_min窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    vwap_window = window_sizes['vwap_window']
    cov_window = window_sizes['cov_window']
    regbeta_window = window_sizes['regbeta_window']
    mean_window = window_sizes['mean_window']
    zscore_window = window_sizes['zscore_window']
    delta_window = window_sizes['delta_window']
    std_window = window_sizes['std_window']
    min_window = window_sizes['min_window']

    df = data_df.copy()

    # 按symbol分组，确保时间序列计算在品种维度进行
    grouped = df.groupby('symbol')

    # 临时解决方案：如果数据中没有vwap列，尝试用close和volume计算vwap
    if 'vwap' not in df.columns:
        # 计算分子：close*volume的滚动和
        df['close_volume_product'] = grouped['close'].transform(
            lambda x: (x * df['volume']).rolling(window=vwap_window).sum()
        )
        # 计算分母：volume的滚动和
        df['volume_sum'] = grouped['volume'].transform(
            lambda x: x.rolling(window=vwap_window).sum() + 1e-8
        )
        # 计算vwap
        df['vwap'] = df['close_volume_product'] / (df['volume_sum'] + 1e-8)

    # 步骤1: 计算ts_cov(cov_window, amount, vwap)
    df['T1'] = grouped.apply(
        lambda x: x['amount'].rolling(window=cov_window).cov(x['vwap'])
    ).reset_index(level=0, drop=True).squeeze()
    # 处理cov结果中的nan/inf
    df['T1'] = df['T1'].replace([np.inf, -np.inf], np.nan)

    # 步骤2: 计算ts_regbeta(amount, low, regbeta_window)
    df['T2'] = grouped.apply(
        lambda x: x['low'].rolling(window=regbeta_window).cov(x['amount']) /
                 (x['amount'].rolling(window=regbeta_window).var() + 1e-8)
    ).reset_index(level=0, drop=True).squeeze()
    # 处理beta结果中的nan/inf
    df['T2'] = df['T2'].replace([np.inf, -np.inf], np.nan)

    # 步骤3: 计算ts_mean(T2, mean_window)
    df['T3'] = grouped['T2'].transform(
        lambda x: x.rolling(window=mean_window).mean()
    )

    # 步骤4: 计算gp_min(T1, T3)
    df['T4'] = df[['T1', 'T3']].min(axis=1)

    # 步骤5: 计算ts_zscore(T4, zscore_window)
    mean_val = grouped['T4'].transform(
        lambda x: x.rolling(window=zscore_window).mean()
    )
    std_dev = grouped['T4'].transform(
        lambda x: x.rolling(window=zscore_window).std()
    )
    # 避免除以0
    df['T4_standardized'] = (df['T4'] - mean_val) / (std_dev + 1e-8)
    # 处理zscore结果中的nan/inf
    df['T4_standardized'] = df['T4_standardized'].replace([np.inf, -np.inf], np.nan)

    # 步骤6: 截面排名（按时间点对所有symbol的T4_standardized进行排名）
    df = df.sort_values(['trade_date', 'time'])
    # 确保排名不会因为nan/inf导致问题，rankdata会自动处理nan
    df['X1'] = df.groupby(['trade_date', 'time'])['T4_standardized'].transform(
        lambda x: pd.Series(rankdata(x.dropna(), method='average'), index=x.dropna().index)
    )
    # 对于因为T4_standardized为nan而没有排名的位置，填充nan
    df['X1'] = df.groupby(['trade_date', 'time'])['X1'].transform(lambda x: x.reindex(df.loc[x.index, 'T4_standardized'].index))

    # 步骤7: 计算delta(amount, delta_window)
    df['T5'] = grouped['amount'].transform(lambda x: x - x.shift(delta_window))

    # 步骤8: 计算ts_std(close, std_window)
    df['T6'] = grouped['close'].transform(
        lambda x: x.rolling(window=std_window).std()
    )
    # 处理std结果中的nan
    df['T6'] = df['T6'].replace([np.inf, -np.inf], np.nan)

    # 步骤9: 计算div(T5, T6)
    # 避免除以0
    df['T7'] = df['T5'] / (df['T6'] + 1e-8)
    # 处理除法结果中的nan/inf
    df['T7'] = df['T7'].replace([np.inf, -np.inf], np.nan)

    # 步骤10: 计算ts_min(T7, min_window)
    df['X2'] = df.groupby('symbol')['T7'].transform(
        lambda x: x.rolling(window=min_window).min()
    )
    # 处理min结果中的nan/inf
    df['X2'] = df['X2'].replace([np.inf, -np.inf], np.nan)

    # 步骤11: 计算最终因子值并处理无效值
    df['factor'] = df['X1'] * df['X2']
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

