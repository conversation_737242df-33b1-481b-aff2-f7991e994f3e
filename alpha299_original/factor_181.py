# Alpha299因子 - factor_181
# 原始因子编号: 181
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_181(
    data_df,
    w: int | None = 7,
    uni_col: str | None = None
) -> pd.DataFrame:
    """
    计算Alpha 85因子：成交量强度与价格动量时序排名乘积因子

    参数:
        data_df (DataFrame): 输入数据，包含交易日期、时间、品种代码及基础数据列
        w (int): 基准时间窗口参数（单位：天），默认20
        uni_col (str): 单一基础数据列参数，此处设为None（因子涉及多个基础列）

    返回:
        DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果
    """
    # 窗口配置
    window_configs = {
        'delta_days': 7.0,      # w * 0.35 = 20 * 0.35 = 7天差分窗口
        'ts_rank_window1': 20.0,  # w = 20天卷积排名窗口
        'ts_rank_window2': 8.0   # w * 0.4 = 20 * 0.4 = 8天动量排名窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    delta_days = window_sizes['delta_days']
    ts_rank_window1 = window_sizes['ts_rank_window1']
    ts_rank_window2 = window_sizes['ts_rank_window2']

    df = data_df.copy()

    # 确保时间格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按品种分组处理
    def process_group(group):
        # 计算成交量均值（使用实际窗口大小）
        group['volume_ma20'] = group['volume'].rolling(window=ts_rank_window1, min_periods=w).mean()
        # 计算成交量比率
        # 保护分母不为0
        group['vol_ratio'] = group['volume'] / (group['volume_ma20'] + 1e-8)

        # 计算时序排名 P1（使用实际窗口大小）
        def ts_rank_vol_ratio(x):
            # 降序排名，返回最后元素的名次
            # 保护输入为常数或包含NaN/Inf的情况
            if x.std() == 0 or x.isnull().all():
                 return np.nan # 或者返回一个合理的值，这里选择NaN表示无法排名
            return x.rank(method='first', ascending=False).iloc[-1]
        group['p1'] = group['vol_ratio'].rolling(window=ts_rank_window1, min_periods=w).apply(ts_rank_vol_ratio, raw=False)

        # 计算价格差分（使用实际窗口大小）
        group['delta_7'] = group['close'] - group['close'].shift(delta_days)
        # 取负值并计算时序排名 P2（使用实际窗口大小）
        group['delta_neg'] = -group['delta_7']
        def ts_rank_delta(x):
            # 保护输入为常数或包含NaN/Inf的情况
            if x.std() == 0 or x.isnull().all():
                 return np.nan # 或者返回一个合理的值，这里选择NaN表示无法排名
            return x.rank(method='first', ascending=False).iloc[-1]
        group['p2'] = group['delta_neg'].rolling(window=ts_rank_window2, min_periods=w).apply(ts_rank_delta, raw=False)

        # 计算最终因子值
        group['factor'] = group['p1'] * group['p2']
        # 替换无穷值为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        return group

    df = df.groupby('symbol', group_keys=False).apply(process_group)

    # 恢复时间格式
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回所需列
    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

