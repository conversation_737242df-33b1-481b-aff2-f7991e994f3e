# Alpha299因子 - factor_11
# 原始因子编号: 11
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_11(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha因子：基于成交量与价格相关性和价格偏差的组合因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'adv_window': 20,       # ADV计算窗口
        'corr_window': 5,       # 相关系数计算窗口
        'scale_window': 5       # 标准化窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    adv_window = window_sizes['adv_window']
    corr_window = window_sizes['corr_window']
    scale_window = window_sizes['scale_window']

    df = data_df.copy()

    # 计算ADV（平均成交量）
    df['ADV'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=adv_window, min_periods=w).mean()
    )

    # 计算ADV与LOW的相关系数
    # 对corr的结果进行nan和inf的处理
    df['corr'] = df.groupby('symbol').apply(
        lambda g: g['ADV'].rolling(corr_window, min_periods=w).corr(g['low']).fillna(0).replace([np.inf, -np.inf], 0)
    ).reset_index(level=0, drop=True)

    # 计算(HIGH+LOW)/2相对于CLOSE的相对偏差
    df['deviation'] = ((df['high'] + df['low']) / 2 - df['close']) / (df['close'] + 1e-8)

    # 相加两个结果
    df['sum'] = df['corr'] + df['deviation']

    # 标准化处理（使用滚动窗口的均值和标准差）
    def scale(group):
        # 对滚动标准差加1e-8防止除以0
        std_rolling = group.rolling(window=scale_window, min_periods=w).std() + 1e-8
        mean_rolling = group.rolling(window=scale_window, min_periods=w).mean()
        # 对结果进行nan和inf的处理
        scaled_group = ((group - mean_rolling) / std_rolling).replace([np.inf, -np.inf], np.nan)
        return scaled_group

    df['factor'] = df.groupby('symbol')['sum'].transform(scale)

    # 严格恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去除无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

