# Alpha299因子 - factor_200
# 原始因子编号: 200
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_200(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha111因子
    参数:
        data_df: 输入的DataFrame，包含必要的字段
        w: 基准窗口参数（单位：天），默认为12
        uni_col: 单一基础列参数，本因子不需要使用该参数
    返回:
        包含因子结果的DataFrame
    """
    # 窗口配置
    window_configs = {
        'n1': 12.0,   # w，ts_corr(12, volume, open_price)
        'n2': 18.0,   # int(1.5*w) = int(1.5*12) = 18，ts_max(C1, 18)
        'n3': 12.0,   # w，ts_corr(12, volume, close)
        'n4': 10.0    # int(0.833*w) = int(0.833*12) = 10，delay(T2, 10)
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    
    df = data_df.copy()

    # 确保数值列为数值类型
    numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'amount']
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # 步骤1: 计算volume和open的n1周期滚动相关系数（使用实际窗口大小）
    def calc_corr(group):
        # 添加对常数序列的处理，如果std为0，corr为nan，我们将其设为0
        corr_result = group['volume'].rolling(n1, min_periods=w).corr(group['open'])
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    df['C1'] = df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)

    # 步骤2: 计算C1的n2周期滚动最大值（使用实际窗口大小）
    df['X1'] = df.groupby('symbol')['C1'].transform(
        lambda x: x.rolling(n2, min_periods=w).max()
    )
    # 确保滚动最大值结果没有inf或nan
    df['X1'] = df['X1'].replace([np.inf, -np.inf], np.nan)


    # 步骤3: 计算amount的绝对值平方根
    # 确保amount非负，虽然np.abs已经处理了，但为了稳健性
    df['T1'] = np.sqrt(np.abs(df['amount']))
    # 确保结果没有inf或nan
    df['T1'] = df['T1'].replace([np.inf, -np.inf], np.nan)


    # 步骤4: 计算volume和close的n3周期滚动相关系数（使用实际窗口大小）
    def calc_corr2(group):
        # 添加对常数序列的处理，如果std为0，corr为nan，我们将其设为0
        corr_result = group['volume'].rolling(n3, min_periods=w).corr(group['close'])
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    df['C2'] = df.groupby('symbol').apply(calc_corr2).reset_index(level=0, drop=True)

    # 步骤5: 取T1和C2的较大值
    df['T2'] = df[['T1', 'C2']].max(axis=1)
    # 确保结果没有inf或nan
    df['T2'] = df['T2'].replace([np.inf, -np.inf], np.nan)


    # 步骤6: 获取T2的n4周期延迟（使用实际窗口大小）
    df['X2'] = df.groupby('symbol')['T2'].shift(n4)
    # 确保结果没有inf或nan
    df['X2'] = df['X2'].replace([np.inf, -np.inf], np.nan)


    # 步骤7: 计算X1和X2的乘积
    df['factor'] = df['X1'] * df['X2']

    # 步骤8: 处理无穷大值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 格式化日期和时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    return output_df.dropna()

