# Alpha299因子 - factor_185
# 原始因子编号: 185
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_185(data_df, w: int | None = 9, uni_col: str | None = 'close'):
    """
    计算Alpha 89因子，基于MACD指标变体，应用tanh变换预防局部基准违规。

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列['symbol', 'trade_date', 'time', 'close']
        w (int | None): 核心可调窗口参数，默认12（对应EMA短期跨度）
        uni_col (str | None): 单一基础数据列，默认'close'

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 窗口配置
    window_configs = {
        'short_span': 12.0,   # w，短期EMA跨度
        'long_span': 26.0,    # w*2+2 = 12*2+2 = 26，长期EMA跨度
        'signal_span': 9.0    # 9*w/12 = 9*12/12 = 9，信号线EMA跨度
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    short_span = window_sizes['short_span']
    long_span = window_sizes['long_span']
    signal_span = window_sizes['signal_span']
    
    # 按symbol和时间排序确保EMA正确计算
    df = data_df.sort_values(by=['symbol', 'time']).copy()

    # 计算EMA（使用实际窗口大小）
    df['ema_short'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.ewm(span=short_span, adjust=False).mean())
    df['ema_long'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.ewm(span=long_span, adjust=False).mean())

    # 计算MACD线和信号线（使用实际窗口大小）
    df['macd_line'] = df['ema_short'] - df['ema_long']
    df['signal_line'] = df.groupby('symbol')['macd_line'].transform(
        lambda x: x.ewm(span=signal_span, adjust=False).mean())

    # 计算原始因子值并应用tanh变换
    df['alpha_89_raw'] = 2 * (df['macd_line'] - df['signal_line'])
    df['factor'] = np.tanh(df['alpha_89_raw'])

    # 处理±∞为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

