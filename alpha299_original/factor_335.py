# Alpha299因子 - factor_335
# 原始因子编号: 335
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_335(data_df, w: int | None = 3, uni_col: str | None = None):
    """
    计算Alpha125因子
    
    参数:
    - data_df: 输入数据DataFrame
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，本因子不使用单一列，设为None
    
    返回:
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 9,   # ts_cov窗口
        'n2': 10,  # ts_regbeta窗口
        'n3': 4,   # ts_mean窗口
        'n4': 12,  # ts_zscore窗口
        'n5': 3,   # delta窗口
        'n6': 6,   # ts_std窗口
        'n7': 7    # ts_min窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']
    n6 = window_sizes['n6']
    n7 = window_sizes['n7']

    df = data_df.copy()

    # 先计算vwap（因为输入数据中没有vwap列）
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
    
    # 1. 计算T1 = ts_cov(n1, amount, vwap)
    df['T1'] = df.groupby('symbol').apply(
        lambda g: g['amount'].rolling(window=n1, min_periods=w).cov(g['vwap'])
    ).droplevel('symbol')

    # 2. 计算T2 = ts_regbeta(amount, open_price, n2)
    # 贝塔 = cov(amount, open_price) / var(amount)
    cov = df.groupby('symbol').apply(
        lambda g: g['amount'].rolling(window=n2, min_periods=w).cov(g['open'])
    ).droplevel('symbol')
    var = df.groupby('symbol')['amount'].transform(
        lambda x: x.rolling(window=n2, min_periods=w).var()
    )
    df['T2'] = cov / (var + 1e-8)

    # 3. 计算T3 = ts_mean(T2, n3)
    df['T3'] = df.groupby('symbol')['T2'].transform(
        lambda x: x.rolling(window=n3, min_periods=w).mean()
    )

    # 4. T4 = gp_min(T1, T3)
    df['T4'] = df[['T1', 'T3']].min(axis=1)

    # 5. T4_zscored = ts_zscore(T4, n4)
    mean_T4 = df.groupby('symbol')['T4'].transform(
        lambda x: x.rolling(window=n4, min_periods=w).mean()
    )
    std_T4 = df.groupby('symbol')['T4'].transform(
        lambda x: x.rolling(window=n4, min_periods=w).std()
    )
    df['T4_zscored'] = (df['T4'] - mean_T4) / (std_T4 + 1e-8)  # 加极小值保护

    # 6. X1 = rank(T4_zscored)
    # 按trade_date分组，对每个分组内的T4_zscored进行排名
    # 使用pandas的rank方法，它会自动忽略NaN值
    df['X1'] = df.groupby(['trade_date'])['T4_zscored'].transform(
        lambda x: x.rank(method='average', pct=True)
    )

    # 7. T5 = delta(amount, n5)
    df['T5'] = df.groupby('symbol')['amount'].transform(
        lambda x: x.diff(n5)
    )

    # 8. T6 = ts_std(close, n6)
    df['T6'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=n6, min_periods=w).std()
    )

    # 9. T7 = T5 / T6
    df['T7'] = df['T5'] / (df['T6'] + 1e-8)  # 加极小值保护

    # 10. X2 = ts_min(T7, n7)
    df['X2'] = df.groupby('symbol')['T7'].transform(
        lambda x: x.rolling(window=n7, min_periods=w).min()
    )

    # 11. Alpha125 = X1 * X2
    df['factor'] = df['X1'] * df['X2']

    # 12. 处理无穷大值和NaN值
    df.replace([float('inf'), float('-inf')], float('nan'), inplace=True)

    # 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].astype('string')
    df['time'] = df['time'].astype('string')

    # 选择输出列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

