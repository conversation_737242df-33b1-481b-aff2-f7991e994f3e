# Alpha299因子 - factor_366
# 原始因子编号: 366
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_366(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算Alpha214因子

    Alpha214 = TS_MIN(TS_COV(12, TS_MIN(LOW, 7), TS_MEAN(AMOUNT, 26)), 16)

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列标识，本因子不使用单一列，设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'low_window': 7,          # TS_MIN(LOW)窗口
        'amount_window': 26,      # TS_MEAN(AMOUNT)窗口
        'cov_window': 12,         # TS_COV窗口
        'min_cov_window': 16      # TS_MIN(协方差)窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    low_window = window_sizes['low_window']
    amount_window = window_sizes['amount_window']
    cov_window = window_sizes['cov_window']
    min_cov_window = window_sizes['min_cov_window']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 确保时间列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组处理
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')

        # 步骤1: 计算LOW的7周期最小值
        low_min = group['low'].rolling(window=low_window, min_periods=w).min()

        # 步骤2: 计算AMOUNT的26周期均值
        amount_mean = group['amount'].rolling(window=amount_window, min_periods=w).mean()

        # 步骤3: 计算上述两个序列的12周期协方差
        # 使用滚动窗口计算协方差
        def rolling_cov(x, y, window):
            # 确保x和y长度相同
            assert len(x) == len(y)
            result = np.full(len(x), np.nan)

            for i in range(len(x)):
                if i < window - 1:
                    # 不足窗口期，使用所有可用数据
                    x_window = x[:i+1]
                    y_window = y[:i+1]
                else:
                    # 使用完整窗口
                    x_window = x[i-window+1:i+1]
                    y_window = y[i-window+1:i+1]

                # 至少需要2个有效值计算协方差
                if len(x_window) >= 2:
                    # 计算协方差
                    x_valid = ~np.isnan(x_window)
                    y_valid = ~np.isnan(y_window)
                    valid = x_valid & y_valid

                    if np.sum(valid) >= 2:
                        # 检查窗口内是否有常数序列，避免std=0导致协方差计算问题
                        if np.std(x_window[valid]) > 1e-8 and np.std(y_window[valid]) > 1e-8:
                             result[i] = np.cov(x_window[valid], y_window[valid])[0, 1]
                        else:
                            result[i] = 0.0 # 常数序列协方差为0
            return result

        cov_series = rolling_cov(low_min.values, amount_mean.values, cov_window)

        # 步骤4: 计算协方差的16周期最小值
        min_cov = pd.Series(cov_series).rolling(window=min_cov_window, min_periods=w).min()

        # 步骤5: 将无穷大和负无穷大替换为NaN
        min_cov = min_cov.replace([np.inf, -np.inf], np.nan)

        # 创建结果DataFrame
        result_df = pd.DataFrame({
            'trade_date': group['trade_date'],
            'time': group['time'],
            'symbol': symbol,
            'factor': min_cov.values
        })

        result_dfs.append(result_df)

    # 合并所有结果
    if result_dfs:
        final_df = pd.concat(result_dfs, ignore_index=True)
    else:
        # 如果没有数据，返回空DataFrame
        final_df = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    # 恢复日期和时间格式为字符串
    final_df['trade_date'] = final_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    final_df['time'] = final_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    return final_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

