# Alpha299因子 - factor_199
# 原始因子编号: 199
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_199(data_df, w: int | None = 11, uni_col: str | None = None):
    """
    计算优化后的Alpha10因子，解决量纲混加问题。

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列。
        w (int | None): 核心可调参数，单位为天，默认为11。
        uni_col (str | None): 用于指定单一基础数据列，此处设为None。
    """
    # 窗口配置
    window_configs = {
        'n1': 11.0,   # w，close的ts_zscore窗口
        'n2': 14.0,   # int(14*w/11) = int(14*11/11) = 14，volume的ts_zscore窗口
        'n3': 11.0,   # w，再次计算close的ts_zscore窗口
        'n4': 15.0    # int(15*w/11) = int(15*11/11) = 15，最终ts_std窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    
    df = data_df.copy()

    # 计算ts_zscore的辅助函数
    def ts_zscore(group, window):
        # 确保分母不为0
        std_dev = group.rolling(window=window, min_periods=w).std()
        mean_val = group.rolling(window=window, min_periods=w).mean()
        return (group - mean_val) / (std_dev + 1e-8)

    # 计算X1: close的ts_zscore(n1)（使用实际窗口大小）
    df['X1'] = df.groupby('symbol')['close'].transform(lambda x: ts_zscore(x, n1))

    # 计算X2: volume的ts_zscore(n2)（使用实际窗口大小）
    # 对volume进行处理，避免log(0)等情况，虽然这里没有log，但为了通用性，对输入进行微小调整
    df['volume_protected'] = df['volume'].apply(lambda x: x + 1e-8 if x <= 0 else x)
    df['X2'] = df.groupby('symbol')['volume_protected'].transform(lambda x: ts_zscore(x, n2))
    df = df.drop(columns=['volume_protected']) # 删除临时列

    # 计算X3: X1和X2的逐元素最大值
    df['X3'] = df[['X1', 'X2']].max(axis=1)

    # 计算X4: X1（再次计算close的ts_zscore(n3)）
    df['X4'] = df['X1']

    # 计算X5: X3 + X4
    df['X5'] = df['X3'] + df['X4']

    # 计算X6: X5的ts_std(n4)（使用实际窗口大小）
    df['X6'] = df.groupby('symbol')['X5'].transform(lambda x: x.rolling(window=n4, min_periods=w).std())

    # 替换无穷大值为NaN
    df['X6'] = df['X6'].replace([float('inf'), -float('inf')], float('nan'))

    # 重命名因子列
    df['factor'] = df['X6']

    # 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].astype('string')
    df['time'] = df['time'].astype('string')

    # 选择输出列并删除无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

