# Alpha299因子 - factor_93
# 原始因子编号: 93
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_93(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha 12因子：开盘价与VWAP偏离及收盘价与VWAP偏离因子
    参数:
        data_df: 输入DataFrame，包含必要列
        w: 以天为单位的移动平均窗口（默认10天）
        uni_col: 无用参数（本因子涉及多列计算）
    返回:
        包含因子值的DataFrame
    """
    # 三段式混合模型窗口配置
    window_configs = {
        'ma_window': 10,        # 移动平均窗口
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        ma_window = window_sizes.loc[w, 'ma_window']
    else:
        ma_window = window_configs['ma_window']
    
    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'open', 'close', 'volume', 'amount']
    if not all(col in data_df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in data_df.columns]
        raise ValueError(f"数据中缺少必要列: {missing}")

    df = data_df.copy()
    
    # 1. 计算日线级VWAP（确保每个交易日只计算一次）
    daily_vwap = df.groupby(['symbol', 'trade_date'])[['amount', 'volume']].sum()
    # 保护分母不为0
    daily_vwap['daily_vwap'] = daily_vwap['amount'] / (daily_vwap['volume'] + 1e-8)
    
    # 2. 合并回原始数据（保留time列）
    df = df.merge(daily_vwap.reset_index()[['symbol', 'trade_date', 'daily_vwap']], 
                 on=['symbol', 'trade_date'], how='left')
    
    # 3. 计算MA(VWAP,10)
    df.sort_values(['symbol', 'trade_date'], inplace=True)
    df['ma_vwap_10'] = df.groupby('symbol')['daily_vwap'].rolling(
        window=ma_window, min_periods=w).mean().reset_index(level=0, drop=True)
    
    # 4. 计算D_Open = open_t - MA(VWAP,10)
    df['d_open'] = df['open'] - df['ma_vwap_10']
    
    # 5. 计算D_Close_Abs = |close_t - daily_vwap|
    df['d_close_abs'] = (df['close'] - df['daily_vwap']).abs()
    
    # 6. 横截面百分比排序（按交易日分组）
    def cross_sectional_rank(group):
        # 保护空组或单元素组
        if len(group) <= 1:
            return pd.Series([np.nan] * len(group), index=group.index)
        # 保护输入包含inf/nan，rank函数默认会处理，但为了稳健性，可以先替换
        group = group.replace([np.inf, -np.inf], np.nan)
        return group.rank(pct=True)
    
    # 按交易日分组，进行横截面排序
    df['p1'] = df.groupby('trade_date')['d_open'].transform(cross_sectional_rank)
    df['p2'] = df.groupby('trade_date')['d_close_abs'].transform(cross_sectional_rank) * -1
    
    # 7. 计算最终因子值
    df['factor'] = df['p1'] * df['p2']
    
    # 8. 处理无效值
    df['factor'].replace([np.inf, -np.inf], np.nan, inplace=True)
    
    # 严格处理日期和时间格式
    df['trade_date'] = df['trade_date'].astype('string')
    df['time'] = df['time'].astype('string')
    
    # 保留必要列并去重
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].drop_duplicates()
    
    return output_df.dropna()

