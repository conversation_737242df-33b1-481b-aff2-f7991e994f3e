# Alpha299因子 - factor_25
# 原始因子编号: 25
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_25(
    data_df,
    w: int | None = 1,
    threshold: float = -0.1,
    uni_col: str | None = 'close'
):
    """
    计算Alpha49因子，基于给定的参数w和阈值threshold。

    参数:
    - data_df (pd.DataFrame): 输入数据，包含必要的列如'symbol', 'trade_date', 'time', 'close'等。
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - threshold (float): 判断条件的阈值，默认为-0.1。
    - uni_col (str | None): 单一基础数据列，默认为'close'。

    返回:
    - pd.DataFrame: 包含trade_date, time, symbol, factor四列的DataFrame。
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delay_1': 1,   # delay_1窗口
        'delay_10': 10, # delay_10窗口
        'delay_20': 20  # delay_20窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n1 = window_sizes['delay_1']
    n10 = window_sizes['delay_10']
    n20 = window_sizes['delay_20']

    # 确保数据按symbol和时间排序
    df = data_df.sort_values(by=['symbol', 'trade_date', 'time']).copy()

    # 计算延迟项
    delay_20 = df.groupby('symbol')[uni_col].transform(lambda x: x.shift(n20))
    delay_10 = df.groupby('symbol')[uni_col].transform(lambda x: x.shift(n10))
    delay_1 = df.groupby('symbol')[uni_col].transform(lambda x: x.shift(n1))
    close = df[uni_col]

    # 计算差值
    # 避免除以0，虽然这里是除以常数10，但为了代码健壮性，可以保留1e-8
    diff = (delay_20 - 2 * delay_10 + close) / (10 + 1e-8)

    # 条件判断
    # np.where会自动处理输入中的nan，但为了确保输出没有inf，可以对delay_1 - close进行处理
    # 这里delay_1和close都是原始数据，如果原始数据没有inf，差值也不会有inf
    # 保持原逻辑，np.where会根据diff是否为nan来决定输出
    df['factor'] = np.where(diff < threshold, 1, delay_1 - close)

    # 转换日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并删除无效行
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

