# Alpha299因子 - factor_399
# 原始因子编号: 399
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_399(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算Alpha91因子

    参数:
    - data_df: 输入数据DataFrame
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为7天（delay和delta窗口）。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，本因子不适用，设为None

    返回:
    - 包含factor列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delay_window': 7,
        'delta_window': 7
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delay_window = window_sizes['delay_window']
    delta_window = window_sizes['delta_window']

    # 创建数据副本
    df = data_df.copy()

    # 确保日期列为datetime格式，用于后续的计算
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 按照symbol分组计算因子
    def calculate_group(group):
        # 计算delay(amount, delay_window)，即delay_window个周期前的成交额
        group['amount_delay'] = group['amount'].shift(delay_window)

        # 计算delta(close, delta_window)，即当前收盘价与delta_window个周期前收盘价的差值
        group['close_delta'] = group['close'] - group['close'].shift(delta_window)

        # 计算mul(delay(amount, delay_window), delta(close, delta_window))
        # 添加对amount_delay和close_delta中可能存在的inf/nan的处理，避免乘法产生inf/nan
        group['amount_delay'] = np.where(np.isinf(group['amount_delay']), np.nan, group['amount_delay'])
        group['close_delta'] = np.where(np.isinf(group['close_delta']), np.nan, group['close_delta'])


        group['factor'] = group['amount_delay'] * group['close_delta']

        # 将无穷大值替换为NaN
        group['factor'] = np.where(np.isinf(group['factor']), np.nan, group['factor'])

        return group

    # 按symbol分组应用计算函数
    df = df.groupby('symbol', group_keys=False).apply(calculate_group)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并删除包含NaN的行
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

