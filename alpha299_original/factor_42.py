# Alpha299因子 - factor_42
# 原始因子编号: 42
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_42(data_df, w=7, uni_col=None,
              alpha: float = 1.0, w_max: float = 300.0, lambda_rate: float = 0.1):
    # 定义所有窗口的基准值
    window_configs = {
        'n1_window': 15,        # 线性衰减窗口
        'n2_window': 19,        # 时间序列排名窗口
        'n3_window': 8,         # 相关性计算窗口
        'n4_window': 7,         # 线性衰减和排名窗口
        'adv_window': 30        # ADV计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        nonlocal w_max, lambda_rate, alpha
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n1 = window_sizes['n1_window']         # 线性衰减窗口
    n2 = window_sizes['n2_window']         # 时间序列排名窗口
    n3 = window_sizes['n3_window']         # 相关性计算窗口
    n4 = window_sizes['n4_window']         # 线性衰减和排名窗口
    adv_window = window_sizes['adv_window'] # ADV计算窗口

    """
    计算Alpha92因子的核心逻辑（已修复未来数据泄露），使用动态窗口系统

    参数:
    data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', 'volume']
    w: 基础窗口大小（默认15）
    uni_col: 本因子使用多列数据，因此固定为None
    alpha: 动态窗口的非线性调整参数（默认1.0）
    w_max: 动态窗口的绝对上限（默认300.0）
    lambda_rate: 动态窗口的增长率（默认0.1）

    返回:
    pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果
    """
    
    # 计算平均成交量(使用动态窗口)
    data_df['ADV'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=adv_window, min_periods=w).mean()
    )

    # Part 1: ((HIGH+LOW)/2 + CLOSE) < (LOW+OPEN) 的线性衰减 + 时间序列排名
    # 确保分母不为0
    data_df['part1'] = (((data_df['high'] + data_df['low']) / (2 + 1e-8) + data_df['close']) < (data_df['low'] + data_df['open'])).astype(np.int64)

    def linear_decay(x):
        """线性衰减函数"""
        # 确保输入不是全NaN或全Inf
        if x.isnull().all() or np.isinf(x).all():
            return np.nan
        weights = np.arange(1, len(x)+1)
        # 确保分母不为0
        sum_weights = weights.sum()
        if sum_weights == 0:
            return np.nan
        return (x * weights).sum() / (sum_weights + 1e-8)

    # 使用动态窗口进行线性衰减
    data_df['part1_decay'] = data_df.groupby('symbol')['part1'].transform(
        lambda x: x.rolling(window=n1, min_periods=w).apply(linear_decay, raw=False)
    )

    # 使用动态窗口进行时间序列排名
    data_df['part1_rank'] = data_df.groupby('symbol')['part1_decay'].transform(
        lambda x: x.rolling(window=n2, min_periods=w).rank(pct=True)
    )

    # Part 2: 低点排名与ADV排名的相关性 + 线性衰减 + 时间序列排名
    # 计算排名（使用动态窗口）
    data_df['rank_low'] = data_df.groupby('symbol')['low'].transform(
        lambda x: x.rolling(window=n3, min_periods=w).rank(pct=True)
    )
    data_df['rank_ADV'] = data_df.groupby('symbol')['ADV'].transform(
        lambda x: x.rolling(window=n3, min_periods=w).rank(pct=True)
    )

    # 使用动态窗口计算相关性
    def rolling_corr(group):
        # 确保输入Series不为空且包含有效数据
        if group['rank_low'].isnull().all() or group['rank_ADV'].isnull().all() or len(group) < 2:
            return pd.Series(np.nan, index=group.index)

        # 计算滚动相关性
        corr_series = group['rank_low'].rolling(window=n3, min_periods=w).corr(group['rank_ADV'])

        # 处理相关性结果中的NaN和Inf，替换为0
        corr_series = corr_series.replace([np.inf, -np.inf], np.nan).fillna(0)
        return corr_series

    data_df['corr'] = data_df.groupby('symbol').apply(rolling_corr).droplevel('symbol')

    # 使用动态窗口进行线性衰减
    data_df['part2_decay'] = data_df.groupby('symbol')['corr'].transform(
        lambda x: x.rolling(window=n4, min_periods=w).apply(linear_decay, raw=False)
    )

    # 使用动态窗口进行时间序列排名
    data_df['part2_rank'] = data_df.groupby('symbol')['part2_decay'].transform(
        lambda x: x.rolling(window=n4, min_periods=w).rank(pct=True)
    )

    # 取两部分最小值作为最终因子
    data_df['factor'] = data_df[['part1_rank', 'part2_rank']].min(axis=1)

    # 格式化日期和时间字段
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

