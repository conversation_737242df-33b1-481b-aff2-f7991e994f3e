# Alpha299因子 - factor_168
# 原始因子编号: 168
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_168(data_df, w: int | None = 14, uni_col: str | None = None):
    """
    计算Alpha 68因子：平滑日内波动率调整的价格动量因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'high', 'low', 'volume']等列
        w (int | None): EMA的span参数（默认14）
        uni_col (str | None): 本因子不依赖单一基础列，故设为None

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 窗口配置
        window_configs = {
            'w': 14.0  # EMA的span参数
        }
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    actual_w = window_sizes['w']
    
    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'high', 'low', 'volume']
    if not all(col in data_df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in data_df.columns]
        raise ValueError(f"输入数据缺少必要列: {missing}")

    df = data_df.copy()

    # 按symbol和时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 1. 计算Mid_t
    # 避免分母为0
    df['mid_t'] = (df['high'] + df['low']) / (2.0 + 1e-8)

    # 2. 计算ΔMid_t (滞后1期)
    df['mid_t_lag1'] = df.groupby('symbol')['mid_t'].shift(1)
    df['delta_mid_t'] = df['mid_t'] - df['mid_t_lag1']

    # 3. 计算Range_t
    df['range_t'] = df['high'] - df['low']

    # 4. 计算X_t
    # 避免分母为0
    df['X_t'] = (df['delta_mid_t'] * df['range_t']) / (df['volume'] + 1e-8)

    # 处理Volume=0导致的无穷大或NaN
    df['X_t'] = df['X_t'].replace([float('inf'), -float('inf')], float('nan'))
    # 确保X_t中的NaN不会影响后续计算，虽然ewm可以处理NaN，但显式处理更安全
    df['X_t'] = df['X_t'].fillna(0) # 填充0是考虑到X_t作为中间变量，0代表没有波动或成交

    # 5. 计算EMA(X_t, span=actual_w)
    # ewm的min_periods=w可以处理窗口期开始的NaN，但我们已经在上一步fillna(0)了
    df['factor'] = df.groupby('symbol')['X_t'].transform(
        lambda x: x.ewm(span=actual_w, adjust=False, min_periods=w).mean()
    )

    # 6. 处理无效值（保留NaN）
    # ewm的结果可能仍然有NaN，例如整个序列都是NaN
    df['factor'] = df['factor'].astype(float)

    # 严格按要求格式化日期和时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    return result_df

