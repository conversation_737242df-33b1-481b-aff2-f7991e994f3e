# Alpha299因子 - factor_111
# 原始因子编号: 111
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_111(data_df, w: int | None = 3, uni_col: str | None = 'close'):
    """
    计算多周期均线平均值因子（Alpha 153）
    
    参数：
        data_df: 输入DataFrame，包含symbol, trade_date, time, close等列
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 使用的基础数据列，默认为'close'
        
    返回：
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 3,        # 第一个均线周期
        'n2': 6,        # 第二个均线周期 (2*w)
        'n3': 12,       # 第三个均线周期 (4*w)
        'n4': 24,       # 第四个均线周期 (8*w)
        'min_periods': 3  # 最小有效期数
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    min_periods = window_sizes['min_periods']

    df = data_df.copy()

    # 按symbol分组计算各周期均线
    # 确保输入数据没有inf或nan，虽然mean对nan有容忍度，但为了稳健性，可以考虑在计算前处理
    # 这里假设原始数据已经相对干净，主要关注计算过程中的潜在问题
    df[f'ma{n1}'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=n1, min_periods=w).mean()
    )
    df[f'ma{n2}'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=n2, min_periods=w).mean()
    )
    df[f'ma{n3}'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=n3, min_periods=w).mean()
    )
    df[f'ma{n4}'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=n4, min_periods=w).mean()
    )

    # 计算四个均线的平均值
    # 避免除以零的情况，虽然这里除数是常数4，但为了通用性，可以保留微小量
    df['factor'] = (df[f'ma{n1}'] + df[f'ma{n2}'] + df[f'ma{n3}'] + df[f'ma{n4}']) / (4 + 1e-8)

    # 处理无穷大值和NaN值
    # mean计算本身会产生NaN，这里进一步处理计算结果中的inf和nan
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    # 保留真实的NaN，不进行fillna(0)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    # 保留包含NaN的行，以便后续处理或观察
    output_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return output_df

