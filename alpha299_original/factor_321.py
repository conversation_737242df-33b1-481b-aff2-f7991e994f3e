# Alpha299因子 - factor_321
# 原始因子编号: 321
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_321(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha96因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'amount', 'low', 'close', 'volume']等列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 保留参数，此处不使用

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 7,   # ts_cov窗口
        'n2': 12,  # ts_mean窗口
        'n3': 6,   # ts_std窗口
        'n4': 11   # ts_corr窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']

    # 检查必要列
    required_columns = ['symbol', 'trade_date', 'time', 'amount', 'low', 'close', 'volume']
    for col in required_columns:
        if col not in data_df.columns:
            raise ValueError(f"缺少必要列: {col}")

    df = data_df.copy()

    # 动态计算vwap：使用累计成交量加权平均价
    # 保护分母不为0
    df['vwap'] = (df.groupby('symbol')['close'].transform(lambda x: (x * df['volume']).cumsum()) /
                 (df.groupby('symbol')['volume'].transform(lambda x: x.cumsum()) + 1e-8))

    # 1. 计算ts_cov(n1, vwap, amount)
    # 滚动协方差，可能出现窗口内常数导致std=0，或者inf/nan，这里先计算，后面统一处理inf/nan
    df['ts_cov'] = df.groupby('symbol').apply(
        lambda x: x['vwap'].rolling(window=n1, min_periods=w).cov(x['amount'])
    ).reset_index(level=0, drop=True)

    # 2. 计算ts_mean(n2, low)
    df['ts_mean_low'] = df.groupby('symbol')['low'].transform(
        lambda x: x.rolling(window=n2, min_periods=w).mean()
    )

    # 3. 计算X1 = ts_cov * ts_mean_low
    df['X1'] = df['ts_cov'] * df['ts_mean_low']

    # 4. 计算abs(amount)
    df['abs_amount'] = df['amount'].abs()

    # 5. 计算ts_std(n3, abs_amount)
    # 滚动标准差，可能出现窗口内常数导致std=0，这里先计算，后面统一处理inf/nan
    df['ts_std_abs_amount'] = df.groupby('symbol')['abs_amount'].transform(
        lambda x: x.rolling(window=n3, min_periods=w).std()
    )

    # 6. 计算ts_corr(n4, X1, ts_std_abs_amount)
    # 滚动相关系数，可能出现窗口内常数导致std=0，或者inf/nan
    df['Alpha96'] = df.groupby('symbol').apply(
        lambda x: x['X1'].rolling(window=n4, min_periods=w).corr(x['ts_std_abs_amount'])
    ).reset_index(level=0, drop=True)

    # 7. 处理无穷大值和NaN值，将inf/-inf/nan替换为0，因为corr结果为常数时std=0，corr无意义，替换为0是合理的处理
    df['Alpha96'] = df['Alpha96'].replace([float('inf'), -float('inf')], float('nan')).fillna(0)


    # 格式化日期和时间字段
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'Alpha96']].rename(columns={'Alpha96': 'factor'})
    # 保留原始的NaN值，不在这里dropna
    # output_df = output_df.dropna()

    return output_df

