# Alpha299因子 - factor_21
# 原始因子编号: 21
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_21(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha42因子：VWAP与CLOSE的差值排名除以VWAP与CLOSE的和的排名
    """
    df = data_df.copy()

    # 检查必要列是否存在
    required_cols = ['volume', 'amount', 'close']
    if not all(col in df.columns for col in required_cols):
        raise ValueError(f"输入数据缺少必要列: {required_cols}")

    # 按symbol和时间排序
    df.sort_values(by=['symbol', 'time'], inplace=True)

    # 计算VWAP（成交量加权平均价）
    # 保护分母不为0
    df['vwap'] = df.groupby('symbol')['amount'].cumsum() / (df.groupby('symbol')['volume'].cumsum() + 1e-8)

    # 计算VWAP与CLOSE的差值和和
    df['vwap_minus_close'] = df['vwap'] - df['close']
    df['vwap_plus_close'] = df['vwap'] + df['close']

    # 按时间分组计算排名
    # rank函数本身对nan有处理，但为了稳健，可以先填充一个极小值或0再rank，这里rank默认处理nan
    df['rank_diff'] = df.groupby('time')['vwap_minus_close'].rank()
    df['rank_sum'] = df.groupby('time')['vwap_plus_close'].rank()

    # 计算最终因子值
    # 保护分母不为0
    df['factor'] = df['rank_diff'] / (df['rank_sum'] + 1e-8)

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去除无效值
    # 这里的dropna()保留，因为它是在计算完成后去除最终的nan因子值，符合要求
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

