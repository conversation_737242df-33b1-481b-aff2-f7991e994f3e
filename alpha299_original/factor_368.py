# Alpha299因子 - factor_368
# 原始因子编号: 368
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_368(data_df, w: int | None = 2, uni_col: str | None = None):
    """
    计算Alpha219因子

    根据公式：Alpha219 = GP_MAX(GP_MIN(SIGMOID(VWAP), DELTA(VOLUME, 2)), TS_PCTCHG(LOW, 5))

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 不适用于此因子，设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delta_period': 2,      # DELTA窗口
        'pctchg_period': 5      # TS_PCTCHG窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delta_period = window_sizes['delta_period']
    pctchg_period = window_sizes['pctchg_period']

    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 计算VWAP（成交量加权平均价）
    # 保护：分母加一个极小值防止除以0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 1. 计算SIGMOID(VWAP)
    # SIGMOID函数定义域为所有实数，无需特殊处理
    df['sigmoid_vwap'] = 1 / (1 + np.exp(-df['vwap']))

    # 2. 计算DELTA(VOLUME, 2)：当前成交量与2期前成交量的差值
    df['delta_volume'] = df.groupby('symbol')['volume'].transform(lambda x: x.diff(delta_period))

    # 3. 计算GP_MIN(SIGMOID(VWAP), DELTA(VOLUME, 2))
    df['min_sigmoid_delta'] = df[['sigmoid_vwap', 'delta_volume']].min(axis=1)

    # 4. 计算TS_PCTCHG(LOW, 5)：最低价5期百分比变化
    # pct_change可能产生inf或-inf当分母为0时
    df['low_pctchg'] = df.groupby('symbol')['low'].transform(
        lambda x: x.pct_change(periods=pctchg_period)
    )
    # 保护：将pct_change产生的inf或-inf替换为NaN
    df['low_pctchg'] = df['low_pctchg'].replace([np.inf, -np.inf], np.nan)


    # 5. 逐元素比较步骤3结果和步骤4结果，返回较大值
    df['factor'] = df[['min_sigmoid_delta', 'low_pctchg']].max(axis=1)

    # 6. 将结果中的无穷大和负无穷大值替换为NaN
    # 这一步在计算low_pctchg时已经处理过一次，但为了确保最终结果干净，再检查一次
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回要求的列
    # 保持原始逻辑，dropna()
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

