# Alpha299因子 - factor_101
# 原始因子编号: 101
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_101(data_df, w=None, uni_col=None):
    """
    计算Alpha 137复杂条件价格变动因子

    参数:
        data_df: 输入数据，包含必需要的列
        w: 可调时间窗口参数（本因子无需）
        uni_col: 单一基础列参数（本因子无需）

    返回:
        包含因子值的DataFrame
    """
    # 检查必要列
    required_columns = ['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close']
    if not all(col in data_df.columns for col in required_columns):
        missing = [c for c in required_columns if c not in data_df.columns]
        raise ValueError(f"数据缺失必要列: {missing}")

    # 按symbol和时间排序
    df = data_df.sort_values(['symbol', 'trade_date', 'time']).copy()

    # 计算前一日数据
    df['C_t-1'] = df.groupby('symbol')['close'].shift(1)
    df['O_t-1'] = df.groupby('symbol')['open'].shift(1)
    df['L_t-1'] = df.groupby('symbol')['low'].shift(1)

    # 计算分子项
    df['PriceMove_t'] = (df['close'] - df['C_t-1'] +
                        (df['close'] - df['open']) / (2 + 1e-8) +
                        df['C_t-1'] - df['O_t-1'])

    df['VolAmp_t'] = np.maximum(
        np.abs(df['high'] - df['C_t-1']),
        np.abs(df['low'] - df['C_t-1'])
    )

    df['N_t'] = 16 * df['PriceMove_t'] * df['VolAmp_t']

    # 计算分母项
    df['TermA'] = np.abs(df['high'] - df['C_t-1'])
    df['TermB'] = np.abs(df['low'] - df['C_t-1'])
    df['TermC'] = np.abs(df['high'] - df['L_t-1'])
    df['TermD'] = np.abs(df['C_t-1'] - df['O_t-1'])

    # 创建条件判断
    cond1 = (df['TermA'] > df['TermB']) & (df['TermA'] > df['TermC'])
    cond2 = (df['TermB'] > df['TermC']) & (df['TermB'] > df['TermA'])

    df['D_t'] = np.select(
        [cond1, cond2],
        [
            df['TermA'] + df['TermB']/(2 + 1e-8) + df['TermD']/(4 + 1e-8),
            df['TermB'] + df['TermA']/(2 + 1e-8) + df['TermD']/(4 + 1e-8)
        ],
        default=df['TermC'] + df['TermD']/(4 + 1e-8)
    )

    # 计算最终因子值
    df['factor'] = df['N_t'] / (df['D_t'] + 1e-8)

    # 处理无效值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 过滤无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

