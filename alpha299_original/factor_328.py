# Alpha299因子 - factor_328
# 原始因子编号: 328
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_328(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha109因子

    根据因子思路，该因子需要计算：
    Alpha109 = gp_max(log(delay(amount, 5)), ts_zscore(ts_regres(delay(vwap, 10), add(add(vwap, high), close), 16), 20))

    参数:
        data_df: 输入的DataFrame
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一列参数，本因子不使用单一列，设为None

    返回:
        包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delay_amount_period': 5,   # delay amount窗口
        'delay_vwap_period': 10,    # delay vwap窗口
        'regres_period': 16,        # 回归窗口
        'zscore_period': 20         # 标准化窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delay_amount_period = window_sizes['delay_amount_period']
    delay_vwap_period = window_sizes['delay_vwap_period']
    regres_period = window_sizes['regres_period']
    zscore_period = window_sizes['zscore_period']

    # 检查必要的列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'amount', 'high', 'close', 'volume']
    for col in required_columns:
        if col not in data_df.columns:
            raise ValueError(f"输入数据缺少必要的列: {col}")

    # 创建数据副本进行计算
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 计算vwap (成交量加权平均价)
    if 'vwap' not in df.columns:
        # 如果没有vwap列，则计算vwap
        df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 按symbol分组进行计算
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        group = group.sort_values('time')

        # 1. 获取成交额(amount)在delay_amount_period个周期前的值: T1 = delay(amount, delay_amount_period)
        group['T1'] = group['amount'].shift(delay_amount_period)

        # 2. 计算T1的绝对值的自然对数: X1 = log(T1)
        # 添加保护：对T1取绝对值并加上一个很小的数，避免log(0)或log(负数)
        group['X1'] = np.log(np.abs(group['T1']) + 1e-8)

        # 3. 获取vwap在delay_vwap_period个周期前的值: T2 = delay(vwap, delay_vwap_period)
        group['T2'] = group['vwap'].shift(delay_vwap_period)

        # 4. 计算vwap与最高价(high)的和: T3 = add(vwap, high)
        group['T3'] = group['vwap'] + group['high']

        # 5. 计算T3与收盘价(close)的和: T4 = add(T3, close)
        group['T4'] = group['T3'] + group['close']

        # 6. 计算T4对T2在过去regres_period个周期内的滚动回归残差: T5 = ts_regres(T2, T4, regres_period)
        group['T5'] = None
        for i in range(regres_period, len(group)):
            window_y = group['T4'].iloc[i-regres_period+1:i+1].values
            window_x = group['T2'].iloc[i-regres_period+1:i+1].values

            # 检查数据有效性
            valid_indices = ~(np.isnan(window_x) | np.isnan(window_y) | np.isinf(window_x) | np.isinf(window_y))
            if np.sum(valid_indices) >= 2:  # 至少需要2个有效点进行回归
                window_x_valid = window_x[valid_indices]
                window_y_valid = window_y[valid_indices]

                # 添加常数项进行回归
                X = np.column_stack((np.ones(len(window_x_valid)), window_x_valid))
                try:
                    # 计算回归系数
                    beta = np.linalg.lstsq(X, window_y_valid, rcond=None)[0]
                    # 计算当前点的残差
                    group.loc[group.index[i], 'T5'] = group['T4'].iloc[i] - (beta[0] + beta[1] * group['T2'].iloc[i])
                except np.linalg.LinAlgError:
                    # 处理奇异矩阵，此时回归无意义，残差设为NaN
                    pass
        # 后处理：将T5中的inf/-inf替换为NaN
        group['T5'] = group['T5'].replace([np.inf, -np.inf], np.nan)

        # 7. 对T5在过去zscore_period个周期内进行滚动标准化: X2 = ts_zscore(T5, zscore_period)
        group['X2'] = group['T5'].rolling(window=zscore_period, min_periods=w).apply(
            lambda x: (x[-1] - np.nanmean(x)) / (np.nanstd(x) + 1e-8) if np.nanstd(x) != 0 and not np.isnan(x[-1]) else np.nan,
            raw=True
        )
        # 后处理：将X2中的inf/-inf替换为NaN
        group['X2'] = group['X2'].replace([np.inf, -np.inf], np.nan)

        # 8. 取X1和X2中逐元素的较大值得到Alpha109: Alpha109 = gp_max(X1, X2)
        group['factor'] = np.maximum(group['X1'], group['X2'])

        # 9. 将结果中的无穷大值(inf, -inf)替换为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        result_dfs.append(group[['trade_date', 'time', 'symbol', 'factor']])

    # 合并结果
    result_df = pd.concat(result_dfs)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df

