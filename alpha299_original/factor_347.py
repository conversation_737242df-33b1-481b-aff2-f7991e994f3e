# Alpha299因子 - factor_347
# 原始因子编号: 347
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_347(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha_179因子

    Args:
        data_df: 输入的DataFrame
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一列参数，本因子不使用

    Returns:
        包含['trade_date', 'time', 'symbol', 'factor']列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window_regbeta': 14,   # ts_regbeta的窗口
        'window_regres': 5      # ts_regres的窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window_regbeta = window_sizes['window_regbeta']
    window_regres = window_sizes['window_regres']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 确保数据按symbol分组并按时间排序
    df = df.sort_values(['symbol', 'time'])

    # 计算ts_regbeta(close, low, window_regbeta)
    def rolling_regbeta(group, x_col, y_col, window):
        x = group[x_col]
        y = group[y_col]

        # 计算滚动协方差
        rolling_cov = (x * y).rolling(window=window).sum() - \
                     x.rolling(window=window).sum() * y.rolling(window=window).sum() / (window + 1e-8)

        # 计算x的滚动方差
        rolling_var_x = (x * x).rolling(window=window).sum() - \
                       (x.rolling(window=window).sum() ** 2) / (window + 1e-8)

        # 计算beta = cov(x,y) / var(x)
        # 避免除以零，将结果中的inf和nan替换为0
        beta = rolling_cov / (rolling_var_x + 1e-8)
        beta = beta.replace([np.inf, -np.inf], np.nan)

        return beta

    # 按symbol分组计算ts_regbeta
    df['regbeta'] = df.groupby('symbol').apply(
        lambda g: rolling_regbeta(g, 'close', 'low', window_regbeta)
    ).reset_index(level=0, drop=True)

    # 计算ts_regres(regbeta, close, window_regres)
    def rolling_regres(group, x_col, y_col, window):
        x = group[x_col]
        y = group[y_col]

        # 计算滚动beta
        rolling_cov = (x * y).rolling(window=window).sum() - \
                     x.rolling(window=window).sum() * y.rolling(window=window).sum() / (window + 1e-8)

        rolling_var_x = (x * x).rolling(window=window).sum() - \
                       (x.rolling(window=window).sum() ** 2) / (window + 1e-8)

        # 避免除以零，将结果中的inf和nan替换为0
        beta = rolling_cov / (rolling_var_x + 1e-8)
        beta = beta.replace([np.inf, -np.inf], np.nan)

        # 计算残差 = y - beta * x
        residuals = y - beta * x

        return residuals

    # 按symbol分组计算ts_regres
    df['factor'] = df.groupby('symbol').apply(
        lambda g: rolling_regres(g, 'regbeta', 'close', window_regres)
    ).reset_index(level=0, drop=True)

    # 将正无穷和负无穷替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 只保留需要的列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

