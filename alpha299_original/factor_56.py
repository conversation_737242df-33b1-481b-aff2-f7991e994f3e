# Alpha299因子 - factor_56
# 原始因子编号: 56
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_56(data_df, w: int | None = 20, uni_col: str | None = 'close'):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 20        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    """
    计算Alpha 151因子：20日价格动量EMA因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'close']等列
        w (int): 价格动量窗口（默认20天）
        uni_col (str): 用于计算的基础价格列（默认'close'）
    
    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果
    """
    # 深拷贝数据以避免修改原始数据
    df = data_df.copy()
    
    # 计算20日价格动量（当前收盘价 - 20日前收盘价）
    # 确保uni_col列为数值类型
    df[uni_col] = pd.to_numeric(df[uni_col], errors='coerce')
    df['price_momentum'] = df.groupby('symbol')[uni_col].transform(lambda x: x - x.shift(w))
    
    # 计算EMA，span=2*w-1=39
    span = 2 * w - 1
    # 在计算EMA之前，对price_momentum中的inf和nan进行处理，虽然ewm通常能处理nan，
    # 但为了稳健性，可以先替换inf
    df['price_momentum'] = df['price_momentum'].replace([float('inf'), float('-inf')], float('nan'))
    df['ema'] = df.groupby('symbol')['price_momentum'].transform(
        lambda x: x.ewm(span=span, adjust=False).mean()
    )
    
    # 替换±∞为NaN
    df['ema'] = df['ema'].replace([float('inf'), float('-inf')], float('nan'))
    
    # 重命名列并处理日期时间格式
    df['factor'] = df['ema']
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 选择输出列并去除无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    return result_df

