# Alpha299因子 - factor_488
# 原始因子编号: 488
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_488(data_df, w: int | None = 14, uni_col: str | None = None):
    """
    计算资金流量指标 (Money Flow Index, MFI)

    参数:
    data_df: DataFrame - 输入数据
    w: int - 计算MFI的时间周期长度，默认为14天。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    uni_col: str | None - 单一列参数，MFI计算需要多列数据，因此设为None

    返回:
    DataFrame - 包含trade_date, time, symbol和factor列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}
    # 定义所有窗口的基准值
    window_configs = {
        'mfi_window': 14
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    mfi_window = window_sizes['mfi_window']

    # 创建数据副本
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 计算典型价格 (Typical Price, TP)
    # 避免分母为0
    df['typical_price'] = (df['high'] + df['low'] + df['close']) / (3.0 + 1e-8)

    # 计算原始资金流 (Raw Money Flow, RMF)
    df['money_flow'] = df['typical_price'] * df['volume']

    # 按symbol分组处理
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')

        # 计算典型价格变化
        group['tp_change'] = group['typical_price'].diff()

        # 确定正负资金流
        group['positive_money_flow'] = np.where(group['tp_change'] > 0, group['money_flow'], 0)
        group['negative_money_flow'] = np.where(group['tp_change'] < 0, group['money_flow'], 0)

        # 计算N期内的正负资金流总和
        # rolling().sum()会自动处理窗口内的NaN，但如果整个窗口都是NaN，结果会是NaN
        group['total_positive_money_flow'] = group['positive_money_flow'].rolling(window=mfi_window).sum()
        group['total_negative_money_flow'] = group['negative_money_flow'].rolling(window=mfi_window).sum()

        # 计算资金流量指标 (MFI)
        # 处理特殊情况：总资金流接近0或负资金流为0
        group['sum_money_flow'] = group['total_positive_money_flow'] + group['total_negative_money_flow']

        # 初始化MFI列
        group['factor'] = np.nan # 初始化为NaN，而不是0.0

        # 条件1：总资金流很小时，MFI = 0
        # 考虑到浮点数比较，使用一个小的阈值
        mask_small_sum = np.abs(group['sum_money_flow']) < 1e-8
        group.loc[mask_small_sum, 'factor'] = 0.0

        # 条件2：负资金流为0但正资金流大于0时，MFI = 100
        # 考虑到浮点数比较，使用一个小的阈值
        mask_zero_neg = (np.abs(group['total_negative_money_flow']) < 1e-8) & (group['total_positive_money_flow'] > 1e-8)
        group.loc[mask_zero_neg, 'factor'] = 100.0

        # 正常情况：计算MFI
        # 避免分母为0，同时排除已经处理过的特殊情况
        mask_normal = (~mask_small_sum) & (~mask_zero_neg) & (group['sum_money_flow'].notna())
        group.loc[mask_normal, 'factor'] = 100.0 * (
            group.loc[mask_normal, 'total_positive_money_flow'] /
            (group.loc[mask_normal, 'sum_money_flow'] + 1e-8 * np.sign(group.loc[mask_normal, 'sum_money_flow'].replace(0, 1))) # 避免分母为0，同时保持符号
        )

        # 处理计算过程中可能产生的inf或nan，将其设置为nan
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)


        # 添加到结果列表
        result_dfs.append(group)

    # 合并结果
    result_df = pd.concat(result_dfs, ignore_index=True)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 只保留所需列并删除缺失值
    result_df = result_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

