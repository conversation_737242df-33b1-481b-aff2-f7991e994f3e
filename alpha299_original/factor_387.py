# Alpha299因子 - factor_387
# 原始因子编号: 387
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_387(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha67因子

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为14天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，本因子不使用单一列，设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 14,  # 第一个ts_rank的窗口
        'n2': 7,   # 第二个ts_rank的窗口  
        'n3': 6    # ts_regres的窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2'] 
    n3 = window_sizes['n3']

    # 复制数据, 避免修改原始数据
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 计算vwap (成交量加权平均价)
    # 保护：分母加一个小的常数避免除以0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 结果列表
    result_dfs = []

    # 对每个品种分别计算
    for symbol, group in df.groupby('symbol'):
        # 按时间排序
        group = group.sort_values('time').reset_index(drop=True)

        # 1. 计算vwap与收盘价的差值
        group['diff'] = group['vwap'] - group['close']

        # 2. 计算diff在过去n1个周期内的滚动排名并归一化
        rank1 = []
        for i in range(len(group)):
            if i < n1:
                window = group['diff'].iloc[:i+1]
            else:
                window = group['diff'].iloc[i-n1+1:i+1]

            # 保护：过滤掉窗口中的NaN和Inf值
            window = window.replace([np.inf, -np.inf], np.nan).dropna()

            if len(window) > 0:
                # 计算排名并归一化
                # 保护：分母加一个小的常数避免除以0
                rank = window.rank(method='average')
                rank1.append(rank.iloc[-1] / (len(window) + 1e-8))
            else:
                rank1.append(np.nan)

        group['rank1'] = rank1

        # 3. 计算rank1在过去n2个周期内的滚动排名并归一化
        rank2 = []
        for i in range(len(group)):
            if i < n2:
                window = group['rank1'].iloc[:i+1]
            else:
                window = group['rank1'].iloc[i-n2+1:i+1]

            # 保护：过滤掉窗口中的NaN和Inf值
            window = window.replace([np.inf, -np.inf], np.nan).dropna()

            if len(window) > 0:
                # 计算排名并归一化
                # 保护：分母加一个小的常数避免除以0
                rank = window.rank(method='average')
                rank2.append(rank.iloc[-1] / (len(window) + 1e-8))
            else:
                rank2.append(np.nan)

        group['X1'] = rank2

        # 4. 计算vwap的绝对值的平方根
        # 保护：对vwap取绝对值，避免负数开方
        group['sqrt_vwap'] = np.sqrt(np.abs(group['vwap']))

        # 5. 计算sqrt_vwap的反正切值
        # 保护：arctan的定义域是全体实数，但为了数值稳定性，可以考虑对输入加一个小的常数
        group['X2'] = group['sqrt_vwap'].apply(lambda x: atan(x + 1e-8))

        # 6. 计算X2对X1在过去n3个周期内的滚动回归残差
        factor = []
        for i in range(len(group)):
            if i < n3 - 1:
                # 使用可用的历史数据
                if i > 0:  # 至少需要2个点才能进行回归
                    x_window = group['X1'].iloc[max(0, i-n3+1):i+1].values
                    y_window = group['X2'].iloc[max(0, i-n3+1):i+1].values

                    # 过滤掉nan值和inf值
                    valid_mask = ~(np.isnan(x_window) | np.isnan(y_window) | np.isinf(x_window) | np.isinf(y_window))
                    x_valid = x_window[valid_mask]
                    y_valid = y_window[valid_mask]

                    if len(x_valid) > 1:  # 确保有足够的点进行回归
                        try:
                            # 使用scipy的linregress进行线性回归
                            slope, intercept, r_value, p_value, std_err = stats.linregress(x_valid, y_valid)
                            # 计算残差：实际值 - 预测值
                            # 保护：如果回归结果出现NaN或Inf，则残差为NaN
                            if np.isnan(slope) or np.isnan(intercept) or np.isinf(slope) or np.isinf(intercept):
                                factor.append(np.nan)
                            else:
                                # 保护：如果当前点的X1或X2是NaN或Inf，则残差为NaN
                                if np.isnan(x_window[-1]) or np.isnan(y_window[-1]) or np.isinf(x_window[-1]) or np.isinf(y_window[-1]):
                                     factor.append(np.nan)
                                else:
                                    factor.append(y_window[-1] - (slope * x_window[-1] + intercept))
                        except:
                            factor.append(np.nan)
                    else:
                        factor.append(np.nan)
                else:
                    factor.append(np.nan)
            else:
                # 使用完整窗口
                x_window = group['X1'].iloc[i-n3+1:i+1].values
                y_window = group['X2'].iloc[i-n3+1:i+1].values

                # 过滤掉nan值和inf值
                valid_mask = ~(np.isnan(x_window) | np.isnan(y_window) | np.isinf(x_window) | np.isinf(y_window))
                x_valid = x_window[valid_mask]
                y_valid = y_window[valid_mask]

                if len(x_valid) > 1:  # 确保有足够的点进行回归
                    try:
                        # 使用scipy的linregress进行线性回归
                        slope, intercept, r_value, p_value, std_err = stats.linregress(x_valid, y_valid)
                        # 计算残差：实际值 - 预测值
                        # 保护：如果回归结果出现NaN或Inf，则残差为NaN
                        if np.isnan(slope) or np.isnan(intercept) or np.isinf(slope) or np.isinf(intercept):
                            factor.append(np.nan)
                        else:
                             # 保护：如果当前点的X1或X2是NaN或Inf，则残差为NaN
                            if np.isnan(x_window[-1]) or np.isnan(y_window[-1]) or np.isinf(x_window[-1]) or np.isinf(y_window[-1]):
                                 factor.append(np.nan)
                            else:
                                factor.append(y_window[-1] - (slope * x_window[-1] + intercept))
                    except:
                        factor.append(np.nan)
                else:
                    factor.append(np.nan)

        group['factor'] = factor

        # 将无穷大值替换为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        # 只保留需要的列
        result_df = group[['trade_date', 'time', 'symbol', 'factor']]
        result_dfs.append(result_df)

    # 合并结果
    final_result = pd.concat(result_dfs)

    # 恢复日期格式为字符串
    final_result['trade_date'] = final_result['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    final_result['time'] = final_result['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 删除含有NaN的行
    final_result = final_result.dropna()

    return final_result

