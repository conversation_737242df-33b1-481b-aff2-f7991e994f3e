# Alpha299因子 - factor_300
# 原始因子编号: 300
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_300(data_df, w: int | None = 2, uni_col: str | None = None):
    """
    计算Alpha58因子（修复未来数据泄露版本）

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列['amount', 'high', 'close']
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单列参数标识符，本因子为None（使用多列计算）

    返回:
        pd.DataFrame: 包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delay_window': 2  # delay(amount, 2)的延迟窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delay_window = window_sizes['delay_window']

    # 检查必要列是否存在
    required_columns = {'amount', 'high', 'close'}
    if not required_columns.issubset(data_df.columns):
        raise ValueError(f"输入数据缺少必要列: {required_columns}")

    df = data_df.copy()

    # 按symbol和时间排序
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 步骤1: 计算delay(amount, delay_window)
    df['delayed_amount'] = df.groupby('symbol')['amount'].shift(delay_window)

    # 步骤2: 计算log(|delayed_amount|)
    # 添加1e-8处理0的情况
    df['X1'] = np.log(np.abs(df['delayed_amount']) + 1e-8)

    # 步骤3: 截面排名（每个时间点内所有symbol的amount排名）
    df['rank_amount'] = df.groupby(['trade_date', 'time'])['amount'].transform(lambda x: x.rank())

    # 步骤4: 计算T3 = high + close
    df['T3'] = df['high'] + df['close']

    # 步骤5: 截面回归残差（每个时间点内的截面回归）
    def compute_cross_sectional_resid(group):
        # 检查是否有足够的非NaN数据进行回归
        valid_data = group[['rank_amount', 'T3']].dropna()
        if len(valid_data) < 2:  # 至少需要两个点进行回归
            return pd.Series([np.nan] * len(group), index=group.index)

        X = sm.add_constant(valid_data['rank_amount'])  # 添加截距项
        Y = valid_data['T3']

        try:
            model = sm.OLS(Y, X).fit()
            residuals = model.resid
            # 将残差映射回原始索引
            full_residuals = pd.Series(np.nan, index=group.index
                                       )
            full_residuals.loc[valid_data.index] = residuals
            return full_residuals
        except Exception as e:
            # 捕获回归可能出现的异常，返回NaN
            return pd.Series([np.nan] * len(group), index=group.index)

    # 使用apply并处理返回的Series，确保索引对齐
    residuals_result = df.groupby(['trade_date', 'time']).apply(
    lambda g: compute_cross_sectional_resid(g.drop(columns=['trade_date', 'time']))
)
    
    # 确保结果能正确展开为Series
    if isinstance(residuals_result, pd.DataFrame):
        # 如果结果是DataFrame，需要将其展开为Series
        df['X2'] = residuals_result.stack().reset_index(level=[0,1,2], drop=True)
    else:
        # 如果结果是Series，直接使用
        df['X2'] = residuals_result.reset_index(level=[0,1], drop=True)

    # 步骤6: 取X1和X2的较小值
    df['factor'] = df[['X1', 'X2']].min(axis=1)

    # 步骤7: 替换无穷大为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 输出所需的列并dropna
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    # Check if we have any results before processing
    if not output_df.empty:
        # 恢复trade_date和time的格式
        output_df['trade_date'] = pd.to_datetime(output_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
        output_df['time'] = pd.to_datetime(output_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return output_df

