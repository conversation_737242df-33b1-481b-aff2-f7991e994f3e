# Alpha299因子 - factor_44
# 原始因子编号: 44
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_44(data_df, w: int | None = 5, uni_col: str | None = 'close'):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 5        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    """
    计算Alpha9因子
    参数:
        data_df: 输入DataFrame，包含['symbol', 'trade_date', 'time', 'close']等列
        w: 窗口期（5天），默认值5
        uni_col: 基础数据列，默认值'close'
    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    df = data_df.copy()

    # 按symbol和时间排序，确保rolling计算正确
    df = df.sort_values(by=['symbol', 'time'])

    # 计算CLOSE的1期差分
    df['delta'] = df.groupby('symbol')[uni_col].transform(lambda x: x.diff(1))

    # 计算过去w期的最小值和最大值
    # 添加对delta中inf和nan的处理，避免rolling计算异常
    df['delta_cleaned'] = df['delta'].replace([np.inf, -np.inf], np.nan)
    df['ts_min'] = df.groupby('symbol')['delta_cleaned'].transform(lambda x: x.rolling(window=w, min_periods=w).min())
    df['ts_max'] = df.groupby('symbol')['delta_cleaned'].transform(lambda x: x.rolling(window=w, min_periods=w).max())

    # 根据条件生成因子值
    condition1 = (df['ts_min'] > 0)
    condition2 = (df['ts_max'] < 0)
    # 使用原始的delta进行计算，但np.where会自动处理nan值
    df['factor'] = np.where(condition1 | condition2, df['delta'], -df['delta'])

    # 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并去除非空行
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

