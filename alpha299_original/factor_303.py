# Alpha299因子 - factor_303
# 原始因子编号: 303
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_303(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算因子值

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数，本因子涉及多列，设为None

    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 9,       # ts_corr的窗口
        'max_window': 13,       # ts_max的窗口
        'delay_window': 7       # delay的周期
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['corr_window']   # ts_corr的窗口
    n2 = window_sizes['max_window']    # ts_max的窗口
    n3 = window_sizes['delay_window']  # delay的周期

    df = data_df.copy()

    # 1. 计算ts_corr(n1, volume, close)
    df['volume'] = df['volume'].astype('float64')  # 修正数据类型转换方式
    df['close'] = df['close'].astype('float64')    # 修正数据类型转换方式
    # 按symbol分组计算rolling corr
    df['C1'] = df.groupby('symbol').apply(
        lambda x: x['volume'].rolling(window=n1, min_periods=w).corr(x['close'])
    ).reset_index(level=0, drop=True)
    # 处理滚动相关系数可能出现的nan或inf
    df['C1'] = df['C1'].replace([float('inf'), -float('inf')], np.nan).fillna(0)

    # 2. 计算ts_max(C1, n2)
    df['X1'] = df.groupby('symbol')['C1'].rolling(window=n2, min_periods=w).max().reset_index(level=0, drop=True)

    # 3. 计算sqrt(amount)的绝对值平方根
    # amount可能为0，但sqrt(0)是0，没有定义域问题
    df['T1'] = df['amount'].abs().pow(0.5)

    # 4. gp_max(T1, C1)
    df['T2'] = df[['T1', 'C1']].max(axis=1)

    # 5. delay(T2, n3)
    df['X2'] = df.groupby('symbol')['T2'].shift(n3)

    # 6. mul(X1, X2)
    df['factor'] = df['X1'] * df['X2']

    # 7. 处理无穷大值和nan
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], np.nan)

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

