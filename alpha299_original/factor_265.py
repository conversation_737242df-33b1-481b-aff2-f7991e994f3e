# Alpha299因子 - factor_265
# 原始因子编号: 265
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_265(data_df, w: int | None = 10, uni_col: str | None = 'close'):
    """
    计算Alpha208因子，基于VWAP、CLOSE和HIGH的组合逻辑
    参数:
        data_df: 输入数据DataFrame，包含必要字段
        w: 时间窗口参数(天)，用于TS_PCTCHG计算
        uni_col: 单一基础数据列，此处使用'close'
    返回:
        包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'pctchg_window': 10     # 百分比变化计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    pctchg_window = window_sizes['pctchg_window']      # 百分比变化计算窗口

    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'close', 'high', 'volume']
    missing_cols = [col for col in required_cols if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    df = data_df.copy()

    # 计算VWAP（使用close价和volume的累积值）
    # 保护 volume.cumsum() 可能为0的情况
    df['vwap'] = df.groupby('symbol').apply(
        lambda x: (x['close'] * x['volume']).cumsum() / (x['volume'].cumsum() + 1e-8)
    ).reset_index(level=0, drop=True)

    # 计算GP_MIN(VWAP, CLOSE)
    df['gp_min'] = df.groupby('symbol').apply(
        lambda x: x['vwap'].clip(lower=x['close'])
    ).reset_index(level=0, drop=True)

    # 应用SIGMOID函数
    # Sigmoid函数定义域为实数，无需特殊处理
    df['sigmoid_gp_min'] = 1 / (1 + np.exp(-df['gp_min']))

    # 计算VWAP/HIGH
    # 保护 high 可能为0的情况
    df['vwap_high_ratio'] = df['vwap'] / (df['high'] + 1e-8)

    # 计算两者的差值
    df['term1'] = df['sigmoid_gp_min'] - df['vwap_high_ratio']

    # 计算TS_PCTCHG(CLOSE, pctchg_window)
    # pct_change内部会处理NaN，但连续为常数时会产生NaN，这里不做额外处理，保留真实情况
    df['ts_pctchg'] = df.groupby('symbol')['close'].transform(
        lambda x: x.pct_change(periods=pctchg_window)
    )

    # 计算GP_MAX(term1, ts_pctchg)
    # max操作会忽略NaN，保留非NaN值，如果两者都为NaN则结果为NaN
    df['factor'] = df.groupby('symbol').apply(
        lambda x: pd.DataFrame({
            'term1': x['term1'],
            'ts_pctchg': x['ts_pctchg']
        }).max(axis=1)
    ).reset_index(level=0, drop=True)

    # 处理无效值（无穷大）
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列
    # 按照要求，保留NaN值，不进行fillna(0)
    output_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return output_df

