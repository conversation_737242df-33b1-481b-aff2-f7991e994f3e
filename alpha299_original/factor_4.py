# Alpha299因子 - factor_4
# 原始因子编号: 4
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_4(data_df, w: int | None = 5, uni_col: str | None = None):    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 5        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    main_window = window_sizes['main_window']       # 主窗口


    """
    修复后的Alpha16因子计算代码
    修复内容：将所有全局排序改为滚动窗口排序，确保时间序列一致性
    """
    df = data_df.copy()

    # 确保数据按symbol和时间排序
    df = df.sort_values(['symbol', 'time'])

    # 使用滚动窗口排名代替全局排名
    df['rank_high'] = df.groupby('symbol')['high'].rolling(window=w).rank(
        method='average'
    ).reset_index(level=0, drop=True)

    # 对volume进行保护性处理，避免log等操作出现问题，虽然这里没有log，但为了通用性考虑
    df['volume_protected'] = df['volume'].apply(lambda x: x if x > 0 else 1e-8)

    df['rank_volume'] = df.groupby('symbol')['volume_protected'].rolling(window=w).rank(
        method='average'
    ).reset_index(level=0, drop=True)

    # 滚动协方差计算
    def compute_rolling_cov(group):
        # 在计算协方差前，对可能导致问题的列进行检查和处理
        group_cleaned = group[['rank_high', 'rank_volume']].copy()
        # 填充可能的inf或nan，这里选择中位数或者0，考虑到rank的性质，0可能更合适
        group_cleaned = group_cleaned.replace([np.inf, -np.inf], np.nan).fillna(0)
        return group_cleaned['rank_high'].rolling(window=w, min_periods=w).cov(group_cleaned['rank_volume'])

    df['cov_rank'] = df.groupby('symbol').apply(compute_rolling_cov).reset_index(level=0, drop=True)

    # 对协方差结果进行保护性处理，填充可能的nan或inf
    df['cov_rank'] = df['cov_rank'].replace([np.inf, -np.inf], np.nan).fillna(0)


    # 使用滚动窗口排名代替全局排名
    df['factor'] = -df.groupby('symbol')['cov_rank'].rolling(window=w).rank(
        method='average'
    ).reset_index(level=0, drop=True)

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

