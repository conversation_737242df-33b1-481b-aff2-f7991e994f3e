# Alpha299因子 - factor_402
# 原始因子编号: 402
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_402(data_df, w: int | None = 3, uni_col: str | None = None):
    """
    计算Alpha95因子

    公式：Alpha95 = sub(ts_zscore(ts_max(ts_regbeta(close, amount, 15), 5), 20), ts_zscore(log(amount), 20))

    参数:
        data_df: 输入数据
        w: 回归窗口大小，默认为15。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
        uni_col: 不适用于此因子，设为None
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'regbeta_window': 15,
        'max_window': 3,
        'zscore_window': 10
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    regbeta_window = window_sizes['regbeta_window']
    max_window = window_sizes['max_window']
    zscore_window = window_sizes['zscore_window']

    df = data_df.copy()

    # 确保日期列为datetime类型用于排序和分组计算
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 按symbol分组，并确保按时间排序
    df = df.sort_values(['symbol', 'time'])

    # 1. 计算ts_regbeta(close, amount, regbeta_window)
    def rolling_regbeta(group):
        x = group['close'].values
        y = group['amount'].values
        result = np.full_like(x, np.nan)

        for i in range(regbeta_window - 1, len(x)):
            x_window = x[i-regbeta_window+1:i+1]
            y_window = y[i-regbeta_window+1:i+1]

            # 检查窗口中是否有无效值
            if np.isnan(x_window).any() or np.isnan(y_window).any():
                continue

            # 计算回归系数beta = cov(y,x)/var(x)
            try:
                # 使用更稳健的方法计算beta
                # 添加方差检查，并处理常数序列
                if np.var(x_window) > 1e-10 and not np.all(x_window == x_window[0]):
                    slope, _, _, _, _ = linregress(x_window, y_window)
                    result[i] = slope
                else:
                    # 如果x是常数序列或方差接近0，beta无意义，设为NaN
                    result[i] = np.nan
            except:
                # 处理可能的错误情况
                result[i] = np.nan

        return pd.Series(result, index=group.index)

    # 计算回归beta系数
    df['regbeta'] = df.groupby('symbol').apply(rolling_regbeta).reset_index(level=0, drop=True)

    # 2. 计算ts_max(regbeta, max_window)
    df['max_regbeta'] = df.groupby('symbol')['regbeta'].transform(
        lambda x: x.rolling(window=max_window, min_periods=w).max()
    )

    # 3. 计算ts_zscore(max_regbeta, zscore_window)
    df['zscore_max_regbeta'] = df.groupby('symbol')['max_regbeta'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std() + 1e-10)  # 添加小常数避免除零
    )
    # 将zscore计算结果中的inf/-inf替换为NaN
    df['zscore_max_regbeta'] = df['zscore_max_regbeta'].replace([np.inf, -np.inf], np.nan)

    # 4. 计算log(amount)，添加小常数避免对0或负数取对数
    df['log_amount'] = np.log(np.abs(df['amount']) + 1e-10)

    # 5. 计算ts_zscore(log_amount, zscore_window)
    df['zscore_log_amount'] = df.groupby('symbol')['log_amount'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std() + 1e-10)  # 添加小常数避免除零
    )
    # 将zscore计算结果中的inf/-inf替换为NaN
    df['zscore_log_amount'] = df['zscore_log_amount'].replace([np.inf, -np.inf], np.nan)

    # 6. 计算Alpha95 = sub(zscore_max_regbeta, zscore_log_amount)
    df['factor'] = df['zscore_max_regbeta'] - df['zscore_log_amount']

    # 7. 将无穷大值替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    return result_df

