# Alpha299因子 - factor_270
# 原始因子编号: 270
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_270(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha218因子
    参数:
        data_df: 输入DataFrame，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount']
        w: 核心可调参数，用于推导其他窗口长度
        uni_col: 单一基础列参数（此处设为None，因涉及多列运算）
    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 5,       # DELTA计算窗口
        'corr_window': 10        # TS_CORR计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']      # DELTA计算窗口
    corr_window = window_sizes['corr_window']        # TS_CORR计算窗口

    df = data_df.copy()

    # 确保数据按symbol和时间排序
    df.sort_values(by=['symbol', 'time'], inplace=True)

    # 动态计算VWAP（成交量加权平均价）
    def calculate_vwap(group):
        # 使用累计金额除以累计成交量计算VWAP
        group['vwap'] = (group['amount'].cumsum() / (group['volume'].cumsum() + 1e-8)).fillna(0)
        return group

    df = df.groupby('symbol', group_keys=False).apply(calculate_vwap)

    # 步骤1: 计算ARCTAN(VOLUME)
    # 确保输入大于0
    df['arctan_volume'] = np.arctan(df['volume'] + 1e-8)

    # 步骤2: 计算TS_CORR(corr_window, VOLUME, VWAP)
    def calculate_window_corr(group):
        # 分组内按时间排序
        group = group.sort_values('time')
        result = []

        for i in range(len(group)):
            # 提取窗口数据
            start_idx = max(0, i - corr_window + 1)
            window_volumes = group['volume'].iloc[start_idx:i+1]
            window_vwaps = group['vwap'].iloc[start_idx:i+1]

            # 计算相关系数
            if len(window_volumes) < corr_window:
                result.append(np.nan)
            else:
                # 计算标准差，并添加保护
                std_volumes = window_volumes.std()
                std_vwaps = window_vwaps.std()

                if std_volumes == 0 or std_vwaps == 0:
                    result.append(0.0) # 常数序列相关系数为0
                else:
                    # 使用pandas内置corr，更健壮
                    r = window_volumes.corr(window_vwaps)
                    # 确保结果不是inf或nan
                    if np.isinf(r) or np.isnan(r):
                        result.append(0.0)
                    else:
                        result.append(r)

        group['ts_corr'] = result
        return group

    df = df.groupby('symbol', group_keys=False).apply(calculate_window_corr)

    # 步骤3: 相乘
    df['product'] = df['arctan_volume'] * df['ts_corr']

    # 步骤4: LOG(绝对值)
    # 确保输入大于0
    df['log_result'] = np.log(np.abs(df['product']) + 1e-8)

    # 步骤5: DELTA(VWAP, delta_window)
    df['delta_vwap'] = df.groupby('symbol')['vwap'].transform(lambda x: x - x.shift(delta_window))

    # 步骤6: GP_MAX
    df['gp_max'] = df[['log_result', 'delta_vwap']].max(axis=1)

    # 步骤7: SQRT
    # 确保输入非负
    df['sqrt_result'] = np.sqrt(np.abs(df['gp_max']))

    # 步骤8: 替换无穷值为NaN
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 格式处理
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构造输出
    result_df = df[['trade_date', 'time', 'symbol', 'sqrt_result']].rename(columns={'sqrt_result': 'factor'})

    return result_df.dropna()

