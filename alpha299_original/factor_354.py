# Alpha299因子 - factor_354
# 原始因子编号: 354
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_354(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算Alpha18因子

    Alpha18 = ts_regres(delay(open_price, 8), close, 7)

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 这个因子不使用单一列，所以设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'regres_window': 7,       # 回归窗口
        'delay_period': 8         # 延迟周期
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    regres_window = window_sizes['regres_window']
    delay_period = window_sizes['delay_period']

    df = data_df.copy()

    # 确保日期列为datetime类型以便排序和计算
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 确保按照时间排序
    df = df.sort_values(['symbol', 'time'])

    # 使用groupby和apply方法进行计算
    def calculate_group_factor(group):
        # 计算延迟8个周期的开盘价
        group['delayed_open'] = group['open'].shift(delay_period)

        # 初始化因子列
        group['factor'] = np.nan

        # 对于每一行，如果有足够的历史数据，计算回归残差
        for i in range(len(group)):
            if i < delay_period + regres_window - 1:  # 前面的数据不足以计算
                continue

            # 获取过去w个周期的数据
            y_data = group['close'].iloc[i-regres_window+1:i+1].values  # 收盘价作为因变量
            x_data = group['delayed_open'].iloc[i-regres_window+1:i+1].values  # 延迟的开盘价作为自变量

            # 检查是否有无效值或者常数序列
            if np.isnan(x_data).any() or np.isnan(y_data).any() or np.std(x_data) == 0 or np.std(y_data) == 0:
                continue

            # 计算线性回归
            try:
                # 使用stats.linregress，其内部会处理除法，但为了安全，可以考虑手动计算斜率和截距
                # slope = sum((x - mean_x) * (y - mean_y)) / sum((x - mean_x)**2)
                # intercept = mean_y - slope * mean_x
                # 为了保持原函数结构，我们继续使用linregress，其内部的除法保护由库处理
                slope, intercept, r_value, p_value, std_err = stats.linregress(x_data, y_data)

                # 计算残差：实际值 - 预测值
                predicted = slope * x_data[-1] + intercept
                residual = y_data[-1] - predicted
                group.iloc[i, group.columns.get_loc('factor')] = residual
            except:
                continue

        return group[['trade_date', 'time', 'symbol', 'factor']]

    # 应用到每个品种
    result_df = df.groupby('symbol', group_keys=False).apply(calculate_group_factor)

    # 将无穷大值替换为NaN
    result_df['factor'] = result_df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 只保留非NaN的行
    result_df = result_df.dropna(subset=['factor'])

    return result_df[['trade_date', 'time', 'symbol', 'factor']]

