# Alpha299因子 - factor_37
# 原始因子编号: 37
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_37(data_df, w: int | None = None, uni_col: str | None = None):
    # 因子 Alpha72 的核心参数定义（固定值）
    n1 = int(8.93345)  # correlation(High+Low/2, ADV40) 窗口
    n2 = int(10.1519)  # decay_linear 窗口
    n3 = int(3.72469)  # ts_rank(VWAP) 窗口
    n4 = int(18.5188)  # ts_rank(VOLUME) 窗口
    n5 = int(6.86671)  # correlation(ts_rank(VWAP), ts_rank(VOLUME)) 窗口
    n6 = int(2.95011)  # decay_linear 窗口

    # 确保数据按symbol和time排序
    data_df = data_df.sort_values(by=['symbol', 'time'])

    # 计算ADV40（40日平均成交量）
    data_df['adv40'] = data_df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=40, min_periods=w).mean())

    # 计算(High+Low)/2
    data_df['hl2'] = (data_df['high'] + data_df['low']) / 2

    # 计算VWAP（成交量加权平均价）
    data_df['vwap'] = data_df['amount'] / (data_df['volume'] + 1e-8)

    # 计算(High+Low)/2 与 ADV40 的相关系数（窗口n1）
    # 增加对常数序列和inf/nan的处理
    data_df['corr_hl2_adv40'] = data_df.groupby('symbol').apply(
        lambda g: g['hl2'].rolling(n1, min_periods=w).corr(g['adv40']).fillna(0).replace([np.inf, -np.inf], 0)
    ).droplevel('symbol')

    # 对相关系数进行线性衰减处理（窗口n2）
    def decay_linear(x, window):
        # 确保输入不是全NaN或全Inf
        if x.isnull().all() or np.isinf(x).all():
            return pd.Series(np.nan, index=x.index)

        return x.rolling(window, min_periods=w).apply(
            lambda s: np.dot(s.fillna(0), np.arange(1, len(s)+1) / (np.sum(np.arange(1, len(s)+1)) + 1e-8)),
            raw=False
        )

    data_df['decay_corr'] = data_df.groupby('symbol')['corr_hl2_adv40'].transform(lambda x: decay_linear(x, n2))

    # 对衰减后的结果进行滚动窗口排名（修复未来数据泄露）
    data_df['rank_decay_corr'] = data_df.groupby('symbol')['decay_corr'].transform(
        lambda x: x.rolling(window=n2, min_periods=w).rank()
    )

    # 计算VWAP的ts_rank（窗口n3）
    data_df['vwap_ts_rank'] = data_df.groupby('symbol')['vwap'].transform(
        lambda x: x.rolling(n3, min_periods=w).rank()
    )

    # 计算VOLUME的ts_rank（窗口n4）
    data_df['volume_ts_rank'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(n4, min_periods=w).rank()
    )

    # 计算ts_rank(VWAP) 与 ts_rank(VOLUME) 的相关系数（窗口n5）
    # 增加对常数序列和inf/nan的处理
    data_df['corr_ts_rank'] = data_df.groupby('symbol').apply(
        lambda g: (
            g['vwap_ts_rank'].rolling(n5, min_periods=w)
            .corr(g['volume_ts_rank'])
            .fillna(0)  # 将NaN替换为0
            .replace([np.inf, -np.inf], 0) # 将Inf替换为0
            .clip(-1, 1)
        )
    ).droplevel('symbol')

    # 对相关系数进行线性衰减处理（窗口n6）
    data_df['decay_corr_ts'] = data_df.groupby('symbol')['corr_ts_rank'].transform(lambda x: decay_linear(x, n6))

    # 对衰减后的结果进行滚动窗口排名（修复未来数据泄露）
    data_df['rank_decay_corr_ts'] = data_df.groupby('symbol')['decay_corr_ts'].transform(
        lambda x: x.rolling(window=n6, min_periods=w).rank()
    )

    # 计算最终因子值（分子/分母）
    # 确保分母不为0
    data_df['factor'] = data_df['rank_decay_corr'] / (data_df['rank_decay_corr_ts'] + 1e-8)
    # 处理可能产生的inf/nan
    data_df['factor'] = data_df['factor'].replace([np.inf, -np.inf], np.nan)


    # 严格按要求处理日期和时间格式
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留并清理输出列
    result_df = data_df[['trade_date', 'time', 'symbol', 'factor']].drop_duplicates().dropna()

    return result_df

