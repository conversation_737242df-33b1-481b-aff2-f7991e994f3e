# Alpha299因子 - factor_113
# 原始因子编号: 113
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_113(data_df, w: int | None = 9, uni_col: str | None = 'volume'):
    """
    计算Alpha 155成交量MACD因子

    参数:
        data_df (pd.DataFrame): 输入数据包含['symbol', 'trade_date', 'time', 'volume']等列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为12天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 使用的单一基础数据列，默认为'volume'

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'short_span': 12,   # 短期EMA span
        'long_span': 26,    # 长期EMA span
        'signal_span': 9    # 信号线EMA span
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    short_span = window_sizes['short_span']
    long_span = window_sizes['long_span']
    signal_span = window_sizes['signal_span']

    df = data_df.copy()

    # 对基础数据进行保护，避免对0或负数进行log等操作，虽然这里是volume，但EMA计算本身对0是友好的，但为了通用性，可以考虑对输入数据进行微小调整或NaN处理
    # 对于volume，直接使用即可，EMA对0是允许的，但如果换成其他可能为0或负数的列，需要额外处理。
    # 这里不对volume做额外处理，因为EMA计算是稳健的。

    # 按symbol分组计算EMA
    df['ema_short'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.ewm(span=short_span, adjust=False, min_periods=w).mean()
    )
    df['ema_long'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.ewm(span=long_span, adjust=False, min_periods=w).mean()
    )

    # 计算MACD线
    df['macd_vol'] = df['ema_short'] - df['ema_long']

    # 计算信号线
    df['signal_vol'] = df.groupby('symbol')['macd_vol'].transform(
        lambda x: x.ewm(span=signal_span, adjust=False, min_periods=w).mean()
    )

    # 计算最终因子值（MACD柱状图）
    df['factor'] = df['macd_vol'] - df['signal_vol']

    # 处理无穷值和NaN值
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))
    # 这里不额外对NaN进行填充，保留真实的计算结果中的NaN

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    # 保留dropna()以移除计算过程中产生的NaN，这是MACD计算的自然结果
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

