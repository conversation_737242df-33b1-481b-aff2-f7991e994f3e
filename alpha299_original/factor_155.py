# Alpha299因子 - factor_155
# 原始因子编号: 155
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_155(data_df, w=None, uni_col=None):
    # 设置默认参数
    if w is None:
        w = 2  # 默认窗口参数
    if uni_col is None:
        uni_col = 'close'  # 默认使用close列

    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 窗口配置
        window_configs = {
            'n1': 2.0,   # w
            'n2': 8.0,   # 4*w
            'n3': 20.0   # 10*w
        }
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']  
    n3 = window_sizes['n3']

    # 检查必要列
    required_columns = ['symbol', 'trade_date', 'time', 'close', 'volume']
    missing_columns = [col for col in required_columns if col not in data_df.columns]
    if missing_columns:
        raise ValueError(f"缺少必要列: {missing_columns}")

    # 按symbol和time排序以确保滚动计算正确
    df = data_df.sort_values(by=['symbol', 'time']).copy()

    # 计算滚动统计量
    df['MA_Close_8'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=n2, min_periods=w).mean()
    )
    df['MA_Close_2'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=n1, min_periods=w).mean()
    )
    # 增加对std的保护，避免常数序列导致std为0
    df['std_Close_8'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=n2, min_periods=w).std(ddof=1)
    ).fillna(0) # std为0时填0
    df['MA_Volume_20'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=n3, min_periods=w).mean()
    )

    # 计算条件
    # 增加对std_Close_8的保护，避免除以0的情况，虽然这里是加减，但为了代码健壮性，对std为0的情况进行处理
    C1 = (df['MA_Close_8'] + df['std_Close_8']) < df['MA_Close_2']
    C2 = (df['MA_Close_2'] < (df['MA_Close_8'] - df['std_Close_8']))
    # 增加对MA_Volume_20的保护，避免除以0
    C3 = (df['volume'] / (df['MA_Volume_20'] + 1e-8)) >= 1

    # 计算V值（修正.astype(int)为字符串格式）
    V = (
        -1 * C1.astype('int') +
        1 * ((~C1) & C2).astype('int') +
        1 * ((~C1) & (~C2) & C3).astype('int') +
        -1 * ((~C1) & (~C2) & (~C3)).astype('int')
    )

    # 计算最终因子值
    df['factor'] = df['MA_Close_8'] * (1 + V)

    # 去除close为NaN的行
    df = df.dropna(subset=['close'])

    # 替换inf/-inf为NaN
    df['factor'].replace([np.inf, -np.inf], np.nan, inplace=True)

    # 转换日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择并返回所需列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

