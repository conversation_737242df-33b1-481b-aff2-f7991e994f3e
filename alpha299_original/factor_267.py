# Alpha299因子 - factor_267
# 原始因子编号: 267
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_267(data_df, w: int | None = 8, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'max_std_window': 8,    # TS_MAX(TS_STD(...), 8) 的窗口
        'mean_window': 20,      # TS_MEAN(AMOUNT, 20) 的窗口
        'std_window': 14,       # TS_STD(VOLUME*CLOSSE, 14) 的窗口
        'max_cov_window': 16    # TS_MAX(COV(...), 16) 的窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    max_std_window = window_sizes['max_std_window']    # TS_MAX(TS_STD(...), 8) 的窗口
    mean_window = window_sizes['mean_window']          # TS_MEAN(AMOUNT, 20) 的窗口
    std_window = window_sizes['std_window']            # TS_STD(VOLUME*CLOSSE, 14) 的窗口
    max_cov_window = window_sizes['max_cov_window']    # TS_MAX(COV(...), 16) 的窗口

    df = data_df.copy()

    # 步骤1: 计算VOLUME * CLOSE
    df['vol_close'] = df['volume'] * df['close']

    # 步骤2: 计算TS_STD(VOLUME*CLOSSE, std_window)
    df['ts_std'] = df.groupby('symbol')['vol_close'].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).std()
    )

    # 步骤3: 计算TS_MAX(TS_STD(...), max_std_window)
    df['ts_max_std'] = df.groupby('symbol')['ts_std'].transform(
        lambda x: x.rolling(window=max_std_window, min_periods=w).max()
    )
    # 处理ts_max_std中的inf/nan
    df['ts_max_std'] = df['ts_max_std'].replace([np.inf, -np.inf], np.nan)

    # 步骤4: 计算TS_MEAN(AMOUNT, mean_window)
    df['ts_mean_amount'] = df.groupby('symbol')['amount'].transform(
        lambda x: x.rolling(window=mean_window, min_periods=w).mean()
    )
    # 处理ts_mean_amount中的inf/nan
    df['ts_mean_amount'] = df['ts_mean_amount'].replace([np.inf, -np.inf], np.nan)

    # 步骤5: 计算TS_COV(std_window, ts_max_std, ts_mean_amount)
    df['ts_cov'] = df.groupby('symbol').apply(
        lambda group: group['ts_max_std'].rolling(window=std_window, min_periods=w).cov(group['ts_mean_amount'])
    ).reset_index(level=0, drop=True)
    # 处理ts_cov中的inf/nan
    df['ts_cov'] = df['ts_cov'].replace([np.inf, -np.inf], np.nan)

    # 步骤6: 计算TS_MAX(ts_cov, max_cov_window)
    df['factor'] = df.groupby('symbol')['ts_cov'].transform(
        lambda x: x.rolling(window=max_cov_window, min_periods=w).max()
    )

    # 处理无穷大和负无穷大
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 严格按照要求恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并去重
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

