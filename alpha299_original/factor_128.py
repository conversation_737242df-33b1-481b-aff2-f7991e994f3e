# Alpha299因子 - factor_128
# 原始因子编号: 128
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_128(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha 176因子：随机震荡值排名与成交量排名相关性因子
    
    参数:
        data_df (pd.DataFrame): 输入数据
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列参数（本因子不适用，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'stochastic_window': 12,  # 随机指标窗口 (2*w)
        'correlation_window': 6   # 相关性窗口 (w)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    stochastic_window = window_sizes['stochastic_window']
    correlation_window = window_sizes['correlation_window']

    df = data_df.copy()

    # 1. 按symbol和time排序确保时间序列连续
    df.sort_values(by=['symbol', 'time'], inplace=True)

    # 3. 计算滚动最低价和最高价
    df['L_low'] = df.groupby('symbol')['low'].transform(
        lambda x: x.rolling(window=stochastic_window, min_periods=w).min()
    )
    df['H_high'] = df.groupby('symbol')['high'].transform(
        lambda x: x.rolling(window=stochastic_window, min_periods=w).max()
    )

    # 4. 计算随机震荡值StochK（处理分母为0的情况）
    denominator = df['H_high'] - df['L_low']
    df['StochK'] = (df['close'] - df['L_low']) / (denominator + 1e-8)

    # 5. 横截面百分比排序R_K
    df['R_K'] = df.groupby('time')['StochK'].transform(
        lambda x: x.rank(pct=True)
    )

    # 6. 计算成交量的滚动标准化Z_V（使用scipy的zscore）
    # 确保rolling窗口内不是常数，且处理nan/inf
    def safe_zscore(s):
        if s.std() == 0 or np.isnan(s).all() or np.isinf(s).any():
            return np.nan
        return zscore(s, nan_policy='omit')[0]

    df['Z_V'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=correlation_window, min_periods=w).apply(
            safe_zscore, raw=True
        )
    )

    # 7. 横截面百分比排序R_V
    df['R_V'] = df.groupby('time')['Z_V'].transform(
        lambda x: x.rank(pct=True)
    )

    # 8. 计算R_K和R_V的滚动相关系数（修复维度问题）
    # 对corr结果中的nan/inf进行处理，替换为0
    df['factor'] = df.groupby('symbol').apply(
        lambda group: group['R_K'].rolling(window=correlation_window, min_periods=w).corr(group['R_V']).fillna(0)
    ).reset_index(level=0, drop=True)

    # 9. 替换±∞为NaN (这一步在corr后fillna(0)后可能不再必要，但保留以防万一)
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 10. 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 11. 选择输出列并去除无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

