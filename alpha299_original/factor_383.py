# Alpha299因子 - factor_383
# 原始因子编号: 383
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_383(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha62因子

    Alpha62 = ts_regbeta(sub(log(volume), low), log(add(low, close)), 6)

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，本因子不使用单一列，设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'regbeta_window': 6    # ts_regbeta窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    regbeta_window = window_sizes['regbeta_window']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 转换日期格式，便于后续操作
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 计算步骤1: 计算成交量的绝对值的自然对数
    # 添加对volume的保护，避免log(0)
    df['T1'] = np.log(np.abs(df['volume']) + 1e-8)

    # 计算步骤2: 计算T1与最低价的差值
    df['X1'] = df['T1'] - df['low']

    # 计算步骤3: 计算最低价与收盘价的和
    df['T2'] = df['low'] + df['close']

    # 计算步骤4: 计算T2的绝对值的自然对数
    # 添加对T2的保护，避免log(0)
    df['X2'] = np.log(np.abs(df['T2']) + 1e-8)

    # 初始化结果DataFrame
    result_list = []

    # 按symbol分组计算回归beta系数
    for symbol, group in df.groupby('symbol'):
        # 确保按时间排序
        group = group.sort_values('time').reset_index(drop=True)

        # 创建一个新列用于存储因子值
        group['factor'] = np.nan

        # 对每个时间点计算回归beta
        for i in range(regbeta_window, len(group)):
            # 获取窗口内的数据
            window_data = group.iloc[i-regbeta_window:i]

            # 提取X1和X2
            x = window_data['X1'].values
            y = window_data['X2'].values

            # 检查是否有足够的有效数据
            valid_mask = ~np.isnan(x) & ~np.isnan(y)
            if np.sum(valid_mask) >= 2:  # 至少需要2个点才能计算回归
                try:
                    # 计算回归的beta系数
                    # 计算均值
                    mean_x = np.mean(x[valid_mask])
                    mean_y = np.mean(y[valid_mask])

                    # 计算协方差和方差
                    cov_xy = np.sum((x[valid_mask] - mean_x) * (y[valid_mask] - mean_y))
                    var_x = np.sum((x[valid_mask] - mean_x)**2)

                    # 计算beta (斜率)
                    # 添加除法保护，避免除以0
                    if var_x + 1e-8 != 0:
                        slope = cov_xy / (var_x + 1e-8)
                        group.loc[i, 'factor'] = slope
                    else:
                        # 如果方差为0，说明x是常数，回归斜率为0
                        group.loc[i, 'factor'] = 0.0
                except:
                    # 捕获其他可能的异常，将因子值设为NaN
                    group.loc[i, 'factor'] = np.nan


        # 将计算结果添加到结果列表
        result_list.append(group[['trade_date', 'time', 'symbol', 'factor']])

    # 合并所有结果
    if result_list:
        result_df = pd.concat(result_list)

        # 处理无穷大值
        result_df['factor'] = result_df['factor'].replace([np.inf, -np.inf], np.nan)

        # 恢复日期和时间格式为字符串
        result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
        result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

        # 移除NaN值
        # result_df = result_df.dropna() # 保留真实的NaN值

        return result_df
    else:
        # 如果没有结果，返回空DataFrame
        return pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

