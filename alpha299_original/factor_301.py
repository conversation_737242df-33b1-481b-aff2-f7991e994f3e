# Alpha299因子 - factor_301
# 原始因子编号: 301
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_301(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算 Alpha 5 因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'volume', 'amount', 'high']等列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数，此处设为None（因子涉及多列基础数据）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']列的因子结果
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 9,        # ts_cov窗口
        'regbeta_window': 13,   # ts_regbeta窗口
        'mean_window': 8        # ts_mean窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['cov_window']      # ts_cov窗口
    n2 = window_sizes['regbeta_window']  # ts_regbeta窗口
    n3 = window_sizes['mean_window']     # ts_mean窗口

    df = data_df.copy()

    # 计算X1: ts_cov(n1, volume, amount)
    # 添加对inf/nan的保护，虽然cov函数本身对nan有处理，但为了健壮性，可以考虑在计算前或后处理
    df['X1'] = df.groupby('symbol').apply(
        lambda x: x['volume'].rolling(n1, min_periods=w).cov(x['amount'])
    ).reset_index(level=0, drop=True)
    df['X1'] = df['X1'].replace([float('inf'), float('-inf')], np.nan)

    # 计算X2: ts_regbeta(amount, high, n2) = Cov(high, amount) / Var(amount)
    # 注意分母Var(amount)可能为0，需要添加保护
    df['X2'] = df.groupby('symbol').apply(
        lambda x: x['high'].rolling(n2, min_periods=w).cov(x['amount']) /
                 (x['amount'].rolling(n2, min_periods=w).var() + 1e-8)
    ).reset_index(level=0, drop=True)
    df['X2'] = df['X2'].replace([float('inf'), float('-inf')], np.nan)

    # 计算X3: ts_mean(X2, n3)
    # mean函数对nan有处理，但为了健壮性，可以考虑在计算前或后处理
    df['X3'] = df.groupby('symbol')['X2'].transform(
        lambda x: x.rolling(n3, min_periods=w).mean()
    )
    df['X3'] = df['X3'].replace([float('inf'), float('-inf')], np.nan)

    # 计算Alpha5: X1 / X3
    # 注意分母X3可能为0，需要添加保护
    df['factor'] = df['X1'] / (df['X3'] + 1e-8)

    # 处理无穷大值和NaN值
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], np.nan)

    # 严格处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出结果
    # 保持原始逻辑，dropna()保留真实的缺值情况
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

