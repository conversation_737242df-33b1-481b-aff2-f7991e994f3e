# Alpha299因子 - factor_359
# 原始因子编号: 359
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_359(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算Alpha19因子

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为12天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 不使用单一列计算，设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window1': 12,    # ts_regbeta窗口
        'window2': 7      # ts_regres窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window1 = window_sizes['window1']
    window2 = window_sizes['window2']

    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 定义ts_regbeta函数
    def ts_regbeta(group, x_col, y_col, window):
        """计算滚动回归的贝塔系数"""
        x = group[x_col].values
        y = group[y_col].values

        betas = np.full(len(x), np.nan)

        for i in range(window - 1, len(x)):
            x_window = x[i - window + 1:i + 1]
            y_window = y[i - window + 1:i + 1]

            # 检查是否有足够的有效值
            valid_indices = ~(np.isnan(x_window) | np.isnan(y_window))
            if np.sum(valid_indices) >= 2:  # 至少需要2个点进行回归
                x_valid = x_window[valid_indices]
                y_valid = y_window[valid_indices]

                # 计算协方差和方差
                cov_xy = np.cov(x_valid, y_valid, ddof=0)[0, 1]
                var_x = np.var(x_valid, ddof=0)

                # 避免除以零
                betas[i] = cov_xy / (var_x + 1e-8)

        return betas

    # 定义ts_regres函数
    def ts_regres(group, x_col, y_col, window):
        """计算滚动回归的残差"""
        x = group[x_col].values
        y = group[y_col].values

        residuals = np.full(len(x), np.nan)

        for i in range(window - 1, len(x)):
            x_window = x[i - window + 1:i + 1]
            y_window = y[i - window + 1:i + 1]

            # 检查是否有足够的有效值
            valid_indices = ~(np.isnan(x_window) | np.isnan(y_window))
            if np.sum(valid_indices) >= 2:  # 至少需要2个点进行回归
                x_valid = x_window[valid_indices]
                y_valid = y_window[valid_indices]

                # 计算贝塔系数
                cov_xy = np.cov(x_valid, y_valid, ddof=0)[0, 1]
                var_x = np.var(x_valid, ddof=0)

                # 避免除以零
                beta = cov_xy / (var_x + 1e-8)
                # 计算当前点的残差
                residuals[i] = y[i] - beta * x[i]

        return residuals

    # 按品种分组计算
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        group = group.sort_values('trade_date')  # 确保按时间排序

        # 计算第一步: ts_regbeta(open_price, low, 12)
        group['X1'] = ts_regbeta(group, 'open', 'low', window1)

        # 计算第二步: ts_regres(X1, close, 7)
        group['factor'] = ts_regres(group, 'X1', 'close', window2)

        # 将无穷大值替换为NaN
        group['factor'] = np.where(np.isinf(group['factor']), np.nan, group['factor'])

        result_dfs.append(group)

    # 合并结果
    result_df = pd.concat(result_dfs)

    # 恢复日期格式
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并去除NaN值
    result_df = result_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

