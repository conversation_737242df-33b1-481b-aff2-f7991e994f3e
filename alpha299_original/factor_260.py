# Alpha299因子 - factor_260
# 原始因子编号: 260
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_260(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha202因子：TS_CORR(8, DELTA(HIGH, 6), -VOLUME)

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'high', 'volume']等列
        w (int): 时间窗口参数，TS_CORR的窗口大小，默认8
        uni_col (str | None): 由于涉及多个基础列，该参数设为None

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 6,    # delta计算窗口
        'corr_window': 8      # 相关系数计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']    # delta计算窗口
    corr_window = window_sizes['corr_window']      # 相关系数计算窗口

    # 验证必要列存在性
    required_cols = ['symbol', 'trade_date', 'time', 'high', 'volume']
    if not all(col in data_df.columns for col in required_cols):
        raise KeyError(f"输入数据缺少必要列: {required_cols}")

    df = data_df.copy()

    # 计算DELTA(HIGH, delta_window) = high - high.shift(delta_window)
    df['delta_high'] = df.groupby('symbol')['high'].transform(lambda x: x - x.shift(delta_window))

    # 计算-VOLUME
    # volume可能为0，但-volume不会导致数学异常，无需特殊处理
    df['neg_volume'] = -df['volume']

    # 计算TS_CORR(corr_window, delta_high, neg_volume)
    def compute_rolling_corr(group):
        """计算滚动相关系数"""
        # Pandas rolling.corr() 内部会处理除以标准差的情况，
        # 如果标准差为0，结果为NaN。
        # 考虑到滚动窗口内可能出现inf或nan导致corr计算失败，
        # 并且corr结果本身也可能出现nan或inf，
        # 我们在计算后进行统一处理。
        rolling_corr_result = group['delta_high'].rolling(window=corr_window, min_periods=w).corr(group['neg_volume'])
        return rolling_corr_result

    df['factor'] = df.groupby('symbol').apply(compute_rolling_corr).reset_index(level=0, drop=True)

    # 处理无效值：替换inf和-inf为NaN，并将NaN替换为0（因为corr结果为NaN通常意味着无法计算相关性，此时设为0是合理的处理）
    df.replace([float('inf'), float('-inf')], float('nan'), inplace=True)
    df['factor'].fillna(0, inplace=True)


    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    # 保留所有行，包括因子值为0的行，因为fillna(0)已经处理了NaN
    output_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return output_df

