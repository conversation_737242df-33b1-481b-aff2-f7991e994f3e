# Alpha299因子 - factor_261
# 原始因子编号: 261
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_261(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha203因子
    
    参数:
        data_df: 输入DataFrame，包含必要字段
        w: 标准化窗口期（默认20）
        uni_col: 单列参数（此处不适用，设为None）
    """
    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 20,    # z-score计算窗口
        'delta_window': 8       # delta计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    zscore_window = window_sizes['zscore_window']    # z-score计算窗口
    delta_window = window_sizes['delta_window']      # delta计算窗口

    df = data_df.copy()
    
    # 确保必要列存在（注意：VWAP需要通过amount/volume计算）
    required_cols = ['close', 'volume', 'amount', 'high', 'trade_date', 'time', 'symbol']
    if not all(col in df.columns for col in required_cols):
        raise ValueError(f"缺失必要列: {required_cols}")
    
    # 计算VWAP（成交量加权平均价）
    # 添加对volume为0的保护
    df['vwap'] = df['amount'] / (df['volume'].replace(0, 1e-8) + 1e-10)
    
    # 计算滚动z-score
    def calc_ts_zscore(group, col, window):
        # 确保输入序列没有inf或nan，虽然rolling会跳过，但为了稳健性
        valid_group = group[col].replace([np.inf, -np.inf], np.nan)
        
        return valid_group.rolling(window=window, min_periods=w).apply(
            lambda x: (x.iloc[-1] - x.mean()) / (x.std() + 1e-10) if len(x) >= window and (x.std() + 1e-10) != 0 else np.nan, 
            raw=False
        )
    
    # 分组计算
    df['z_close'] = df.groupby('symbol').apply(calc_ts_zscore, 'close', zscore_window).reset_index(level='symbol', drop=True)
    df['z_volume'] = df.groupby('symbol').apply(calc_ts_zscore, 'volume', zscore_window).reset_index(level='symbol', drop=True)
    df['z_vwap'] = df.groupby('symbol').apply(calc_ts_zscore, 'vwap', zscore_window).reset_index(level='symbol', drop=True)
    
    # 计算中间步骤
    df['term1'] = df['z_close'] - df['z_volume']
    df['term2'] = df[['z_volume', 'z_vwap']].max(axis=1)
    df['min_term'] = df[['term1', 'term2']].min(axis=1)
    
    # 计算DELTA(HIGH, delta_window)
    df['delta_high'] = df.groupby('symbol')['high'].transform(
        lambda x: x - x.shift(delta_window)
    )
    
    # 最终计算
    df['factor'] = df['min_term'] * df['delta_high']
    
    # 处理无效值（不使用fillna）
    # 确保factor列中的inf和-inf也被视为无效值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    df = df.dropna(subset=['factor'])
    
    # 格式化日期和时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    return df[['trade_date', 'time', 'symbol', 'factor']]

