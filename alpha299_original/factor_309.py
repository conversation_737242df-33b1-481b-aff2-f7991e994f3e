# Alpha299因子 - factor_309
# 原始因子编号: 309
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_309(data_df, w: int | None = 9, uni_col: str | None = 'close'):
    """
    计算Alpha76因子，基于成交量绝对值与收盘价百分比变化率的滚动相关系数

    参数:
    data_df: 输入数据DataFrame，包含['symbol', 'trade_date', 'time', 'close', 'volume']等列
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 用于计算百分比变化率的基准列，默认为'close'

    返回:
    包含['trade_date', 'time', 'symbol', 'factor']列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 9,       # 相关系数窗口
        'pctchg_window': 14     # 百分比变化率窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n_corr = window_sizes['corr_window']        # 相关系数窗口
    n_pctchg = window_sizes['pctchg_window']    # 百分比变化率窗口

    df = data_df.copy()

    # 确保时间顺序正确
    df = df.sort_values(by=['symbol', 'time'])

    # 计算X1 = abs(volume)
    # 确保volume非负，虽然abs已经处理，但为了健壮性，可以考虑对原始volume进行处理，这里abs足够
    df['X1'] = df['volume'].abs()

    # 计算X2 = ts_pctchg(close, n_pctchg)
    # pct_change可能产生inf或nan，但pandas内置函数通常会处理，这里无需额外处理
    df['X2'] = df.groupby('symbol')[uni_col].transform(lambda x: x.pct_change(periods=n_pctchg))

    # 计算X1和X2的滚动相关系数，window=n_corr，min_periods=w
    # 滚动相关系数可能因为窗口内数据为常数（std=0）或包含inf/nan而产生nan或inf
    # pandas的.corr在std=0时会返回NaN，这里需要将NaN和inf/neg_inf替换为0
    df['factor'] = df.groupby('symbol').apply(
        lambda g: g['X1'].rolling(window=n_corr, min_periods=w).corr(g['X2'])
    ).reset_index(level=0, drop=True)

    # 处理相关系数计算结果中的NaN和无穷大值，替换为0
    df['factor'] = df['factor'].fillna(0)
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], 0)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择所需列，这里不进行dropna，保留原始的NaN情况
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

