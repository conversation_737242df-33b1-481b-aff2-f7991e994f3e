# Alpha299因子 - factor_287
# 原始因子编号: 287
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_287(
    data_df,
    w: int | None = 4,
    uni_col: str | None = None
):
    """
    计算Alpha43因子，解决量纲混加问题。

    参数:
        data_df: 输入数据DataFrame，包含必要的列如open, volume, vwap, close等。
        w: 滚动窗口大小用于ts_zscore计算。
        delta_window: delta函数的窗口大小。
        uni_col: 单一基础数据列参数（此处不适用，设为None）。

    返回:
        包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']。
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 4,       # DELTA窗口
        'zscore_window': 20      # ts_zscore窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']       # DELTA窗口
    zscore_window = window_sizes['zscore_window']     # ts_zscore窗口

    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 4,       # DELTA窗口
        'zscore_window': 20      # ts_zscore窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']       # DELTA窗口
    zscore_window = window_sizes['zscore_window']     # ts_zscore窗口

    # 检查必要列是否存在
    required_columns = ['open', 'volume', 'close', 'amount', 'trade_date', 'time', 'symbol']
    for col in required_columns:
        if col not in data_df.columns:
            raise ValueError(f"数据中缺少必要列: {col}")

    df = data_df.copy()

    # 计算vwap（成交量加权平均价）
    df['vwap'] = df['amount'] / (df['volume'] + 1e-10)  # 避免除以0

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 计算滚动Z-score
    def ts_zscore(group, window):
        # 确保输入数据没有inf或nan，并在计算std时避免除以0
        group = group.replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(method='bfill')
        return group.rolling(window=window, min_periods=w).apply(
            lambda x: (x.iloc[-1] - x.mean()) / (x.std() + 1e-10)
        ).replace([np.inf, -np.inf], np.nan) # 滚动计算后再次检查inf/nan

    # 按symbol分组计算Z-score
    df['open_zscore'] = df.groupby('symbol')['open'].transform(lambda x: ts_zscore(x, zscore_window))
    df['volume_zscore'] = df.groupby('symbol')['volume'].transform(lambda x: ts_zscore(x, zscore_window))
    df['vwap_zscore'] = df.groupby('symbol')['vwap'].transform(lambda x: ts_zscore(x, zscore_window))

    # 步骤4: sub(open_zscore, volume_zscore)
    df['T1'] = df['open_zscore'] - df['volume_zscore']

    # 步骤5: gp_max(volume_zscore, vwap_zscore)
    df['T2'] = df[['volume_zscore', 'vwap_zscore']].max(axis=1)

    # 步骤6: gp_max(T1, T2)
    df['X1'] = df[['T1', 'T2']].max(axis=1)

    # 步骤7: delta(close, delta_window)
    df['X2'] = df.groupby('symbol')['close'].transform(lambda x: x - x.shift(delta_window))

    # 步骤8: mul(X1, X2)
    df['factor'] = df['X1'] * df['X2']

    # 步骤9: 替换无穷大值为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去重
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

