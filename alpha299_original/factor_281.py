# Alpha299因子 - factor_281
# 原始因子编号: 281
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_281(data_df, w: int | None = 4, uni_col: str | None = None):
    """
    计算Alpha33因子：
    1. 计算volume的ts_rank(14)
    2. 计算vwap和ts_rank结果的ts_cov(4)
    3. 处理无穷大值
    """
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 4,         # ts_cov窗口
        'rank_window': 14        # ts_rank窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    cov_window = window_sizes['cov_window']         # ts_cov窗口
    rank_window = window_sizes['rank_window']       # ts_rank窗口

    df = data_df.copy()

    # 检查并计算vwap（如果不存在）
    if 'vwap' not in df.columns:
        # 根据close和volume计算累计成交量加权平均价
        df['vwap'] = df.groupby('symbol').apply(
            lambda x: (x['close'] * x['volume']).cumsum() / (x['volume'].cumsum() + 1e-8)
        ).reset_index(level=0, drop=True)

    # 计算ts_rank(volume, rank_window)
    def rolling_rank(group):
        # Ensure volume is not negative or zero before ranking
        group_processed = group.where(group > 0, np.nan)
        return group_processed.rolling(window=rank_window, min_periods=w).apply(
            lambda s: (rankdata(-s.dropna(), method='ordinal') / (len(s.dropna()) + 1e-8))[-1] if len(s.dropna()) > 0 else np.nan,
            raw=False
        )
    df['ts_rank_volume'] = df.groupby('symbol')['volume'].transform(rolling_rank)

    # 计算ts_cov(vwap, ts_rank_volume, cov_window)
    def calc_rolling_cov(group):
        # 确保数据是数值类型
        vwap = group['vwap'].values
        ts_rank = group['ts_rank_volume'].values
        
        # 计算滚动协方差
        result = np.zeros(len(group))
        for i in range(len(group)):
            if i < cov_window - 1:
                result[i] = np.nan
            else:
                window_vwap = vwap[i-cov_window+1:i+1]
                window_rank = ts_rank[i-cov_window+1:i+1]
                valid = ~np.isnan(window_vwap) & ~np.isnan(window_rank)
                if valid.sum() >= 1:
                    cov = np.cov(window_vwap[valid], window_rank[valid], ddof=1)[0,1]
                    result[i] = cov if not np.isnan(cov) else np.nan
                else:
                    result[i] = np.nan
        return pd.Series(result, index=group.index)

    df['factor'] = df.groupby('symbol').apply(calc_rolling_cov).reset_index(level=0, drop=True)

    # 处理无穷大值和NaNs from rolling operations
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

