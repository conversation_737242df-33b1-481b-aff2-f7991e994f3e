# Alpha299因子 - factor_216
# 原始因子编号: 216
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_216(
    data_df,
    w=8,  # 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
    uni_col=None  # 不使用单一基础列，设为None
):
    """
    计算Alpha133因子：
    1. 计算high对amount的16周期滚动回归贝塔（w1=2*w）
    2. 计算volume对close的8周期滚动回归贝塔（w2=w）
    3. 取两个贝塔值的逐元素最大值
    4. 处理无穷大值
    
    参数:
        data_df: 输入数据DataFrame
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 不使用单一基础列，设为None
    """
    # 定义所有窗口的基准值
    window_configs = {
        'beta1_window': 16,    # high vs amount 回归窗口
        'beta2_window': 8      # volume vs close 回归窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    w1 = window_sizes['beta1_window']  # high vs amount 回归窗口
    w2 = window_sizes['beta2_window']  # volume vs close 回归窗口
    
    df = data_df.copy()

    # 确保数据按symbol分组，并按时间排序
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 定义滚动回归贝塔计算函数
    def _ts_regbeta(X_col, Y_col, window):
        """计算Y对X的滚动回归贝塔系数"""
        def beta_func(group):
            X = group[X_col]
            Y = group[Y_col]
            # 计算滚动协方差和方差
            # 添加对X和Y中inf/nan的处理，避免滚动计算中断
            X_clean = X.replace([np.inf, -np.inf], np.nan)
            Y_clean = Y.replace([np.inf, -np.inf], np.nan)

            # 填充可能存在的nan，使用前一个有效值填充，或者使用0填充
            X_filled = X_clean.fillna(method='ffill').fillna(0)
            Y_filled = Y_clean.fillna(method='ffill').fillna(0)

            cov = X_filled.rolling(window=window, min_periods=w).cov(Y_filled)
            var = X_filled.rolling(window=window, min_periods=w).var()

            # 避免除以0，对var加一个小的常数
            beta = cov / (var + 1e-8)

            # 后处理beta，将inf/-inf/nan替换为nan
            beta = beta.replace([np.inf, -np.inf], np.nan)
            return beta
        return df.groupby('symbol').apply(beta_func).reset_index(level=0, drop=True)

    # 计算两个贝塔系数
    df['beta1'] = _ts_regbeta('high', 'amount', w1)  # high vs amount
    df['beta2'] = _ts_regbeta('volume', 'close', w2)  # volume vs close

    # 取最大值
    df['factor'] = df[['beta1', 'beta2']].max(axis=1)

    # 处理无穷大值 (虽然_ts_regbeta内部已经处理，但为了健壮性再检查一次)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

