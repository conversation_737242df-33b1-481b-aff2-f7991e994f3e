# Alpha299因子 - factor_10
# 原始因子编号: 10
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_10(data_df, w: int | None = 20, uni_col: str | None = None):
    """
    计算Alpha25因子，核心逻辑为：
    1. 计算-RETURNS × ADV20 × VWAP × (HIGH - CLOSE)
    2. 对结果进行窗口期滚动Z-score标准化
    3. 对标准化结果进行截面排名
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数，本因子涉及多列，设为None
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'adv_window': 20,       # 平均成交量窗口
        'zscore_window': 20     # Z-score标准化窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    adv_window = window_sizes['adv_window']       # 平均成交量窗口
    zscore_window = window_sizes['zscore_window'] # Z-score标准化窗口

    # 深拷贝数据以避免修改原始数据
    df = data_df.copy()

    # 1. 数据预处理 - 按品种和时间排序
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 2. 计算基础变量
    # 2.1 收益率RETURNS（按品种计算）
    df['RETURNS'] = df.groupby('symbol')['close'].pct_change()

    # 2.2 平均成交量ADV
    df['ADV'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=adv_window, min_periods=w).mean()
    )

    # 2.3 VWAP（成交量加权平均价）
    # 避免除以0
    df['VWAP'] = df['amount'] / (df['volume'] + 1e-8)

    # 2.4 HIGH - CLOSE
    df['HIGH_CLOSE'] = df['high'] - df['close']

    # 3. 计算核心乘积
    df['product'] = -df['RETURNS'] * df['ADV'] * df['VWAP'] * df['HIGH_CLOSE']

    # 4. 滚动Z-score标准化（时间序列维度）
    def rolling_zscore(x, window):
        # 避免除以0，并处理std为NaN/inf的情况
        rolling_mean = x.rolling(window=window, min_periods=w).mean()
        rolling_std = x.rolling(window=window, min_periods=w).std()
        # 将rolling_std中的NaN和inf替换为0，避免除以NaN或inf
        rolling_std = rolling_std.replace([np.inf, -np.inf, np.nan], 0)
        # 避免除以0
        return (x - rolling_mean) / (rolling_std + 1e-8)

    df['zscore'] = df.groupby('symbol')['product'].transform(
        lambda x: rolling_zscore(x, window=zscore_window)
    )

    # 5. 截面排名（同一时间点维度）
    df['factor'] = df.groupby(['trade_date', 'time'])['zscore'].transform(
        lambda x: x.rank(method='first', ascending=False)
    )

    # 6. 格式处理
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 7. 保留必要列并去除无效值
    # 在dropna之前，将factor列中的inf和-inf替换为NaN，以便dropna能够正确处理
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

