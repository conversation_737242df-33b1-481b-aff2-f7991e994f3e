# Alpha299因子 - factor_302
# 原始因子编号: 302
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_302(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha60因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
                                 'volume', 'amount', 'open_interest', 'industry_name']列
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一列参数，本因子使用多列数据，设为None

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 20,    # TS_ZSCORE窗口
        'max_window': 10,       # TS_MAX窗口
        'corr_window': 11,      # TS_CORR窗口
        'delta_window': 5       # DELTA窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    zscore_window = window_sizes['zscore_window']
    max_window = window_sizes['max_window']
    corr_window = window_sizes['corr_window']
    delta_window = window_sizes['delta_window']

    df = data_df.copy()

    # 按symbol分组处理
    grouped = df.groupby('symbol')

    # 计算TS_ZSCORE(sqrt(volume), zscore_window)
    # 对volume加1e-8防止sqrt(0)
    df['sqrt_volume'] = np.sqrt(np.abs(df['volume'] + 1e-8))
    df['zscore_sqrt_volume'] = grouped['sqrt_volume'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std() + 1e-8)
    )

    # 计算TS_ZSCORE(open, zscore_window)
    df['zscore_open'] = grouped['open'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std() + 1e-8)
    )

    # GP_MIN(TS_ZSCORE(sqrt(volume), zscore_window), TS_ZSCORE(open, zscore_window))
    df['gp_min_1'] = df[['zscore_sqrt_volume', 'zscore_open']].min(axis=1)

    # 计算TS_MAX(volume, max_window)
    df['max_volume'] = grouped['volume'].transform(
        lambda x: x.rolling(window=max_window, min_periods=w).max()
    )

    # TS_CORR(corr_window, gp_min_1, max_volume)
    def rolling_corr(x, y, window):
        # 确保输入没有inf或nan，并对结果中的inf/nan进行处理
        temp_df = pd.DataFrame({'x': x, 'y': y}).replace([np.inf, -np.inf], np.nan)
        corr_result = temp_df['x'].rolling(window=window, min_periods=w).corr(temp_df['y'])
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0) # 将corr结果的nan和inf填0

    df['corr_result'] = grouped.apply(
        lambda g: rolling_corr(g['gp_min_1'], g['max_volume'], corr_window)
    ).reset_index(level=0, drop=True)

    # NEG(TS_CORR)
    df['neg_corr'] = -df['corr_result']

    # 计算TS_ZSCORE(low, zscore_window)
    df['zscore_low'] = grouped['low'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std() + 1e-8)
    )

    # 计算TS_ZSCORE(volume, zscore_window)
    # 对volume加1e-8防止std为0
    df['zscore_volume'] = grouped['volume'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std() + 1e-8)
    )

    # GP_MIN(TS_ZSCORE(low, zscore_window), TS_ZSCORE(volume, zscore_window))
    df['gp_min_2'] = df[['zscore_low', 'zscore_volume']].min(axis=1)

    # DELTA(gp_min_2, delta_window)
    df['delta_result'] = grouped['gp_min_2'].transform(
        lambda x: x - x.shift(delta_window)
    )

    # 最终因子计算：SUB(neg_corr, delta_result)
    df['factor'] = df['neg_corr'] - df['delta_result']

    # 替换无穷值为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

