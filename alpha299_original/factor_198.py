# Alpha299因子 - factor_198
# 原始因子编号: 198
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_198(
    data_df,
    w: int | None = None,
    uni_col: str | None = None
):
    """
    计算Alpha104因子

    参数:
        data_df: 输入数据DataFrame
        w: 核心时间窗口参数（本因子不使用）
        uni_col: 单一基础列参数（本因子不使用）

    返回:
        包含因子值的DataFrame
    """
    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'volume', 'low']
    if not all(col in data_df.columns for col in required_cols):
        raise ValueError(f"输入数据缺少必要列: {required_cols}")

    df = data_df.copy()

    # 按symbol和时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 计算ts_corr(16, volume, low)
    def rolling_corr(x, y, window=16):
        # 增加对常数序列的处理，corr结果为NaN时填0
        corr_result = x.rolling(window=window, min_periods=w).corr(y)
        return corr_result.fillna(0)

    df['X1'] = df.groupby('symbol').apply(
        lambda g: rolling_corr(g['volume'], g['low'], window=16)
    ).reset_index(level=0, drop=True)

    # 计算ts_max(volume, 20)
    df['T1'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=20, min_periods=w).max()
    )

    # 计算ts_pctchg(T1, 3)
    # pct_change可能产生inf或nan，这里不特殊处理，留到最后统一处理
    df['X2'] = df.groupby('symbol')['T1'].transform(
        lambda x: x.pct_change(periods=3)
    )

    # 计算X1 + X2
    df['X3'] = df['X1'] + df['X2']

    # 计算arctan(X3)
    # arctan定义域为全体实数，无需特殊处理
    df['factor'] = np.arctan(df['X3'])

    # 替换无穷大值为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    return result_df

