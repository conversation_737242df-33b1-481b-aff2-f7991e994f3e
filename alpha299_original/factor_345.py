# Alpha299因子 - factor_345
# 原始因子编号: 345
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_345(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算Alpha159因子

    参数:
    - data_df: 输入的DataFrame
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一数据列，本因子不使用单一列

    返回:
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 10,  # ts_max(amount, 10)
        'n2': 12,  # ts_regbeta(close, amount, 12)
        'n3': 10,  # ts_rank(..., 10)
        'n4': 8,   # ts_rank(..., 8)
        'n5': 7    # ts_regres(..., 7)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 将日期和时间转换为datetime格式，方便后续处理
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 步骤1: 计算成交额在过去10个周期内的滚动最大值
    df['T1'] = df.groupby('symbol')['amount'].transform(
        lambda x: x.rolling(window=n1, min_periods=w).max()
    )

    # 步骤2: 计算T1的反正切值，对可能为0或负数的情况进行保护
    df['M1'] = df['T1'].apply(lambda x: np.arctan(x + 1e-8))

    # 步骤3: 计算收盘价对成交额在过去12个周期内的滚动回归贝塔系数
    def rolling_beta(group, y_col, x_col, window):
        """计算滚动回归的贝塔系数"""
        y = group[y_col].values
        x = group[x_col].values
        result = np.full_like(x, np.nan)

        for i in range(len(x)):
            if i < window - 1:
                if i > 0:  # 至少有2个点才能计算回归
                    start_idx = 0
                    end_idx = i + 1
                    y_window = y[start_idx:end_idx]
                    x_window = x[start_idx:end_idx]
                    # 过滤掉窗口内的NaN和Inf
                    valid_indices = np.isfinite(x_window) & np.isfinite(y_window)
                    x_window_valid = x_window[valid_indices]
                    y_window_valid = y_window[valid_indices]

                    if len(x_window_valid) >= 2:  # 确保有足够的数据点进行回归
                        cov = np.cov(x_window_valid, y_window_valid)[0, 1] if len(x_window_valid) > 1 else 0
                        var_x = np.var(x_window_valid)
                        # 避免除以零
                        result[i] = cov / (var_x + 1e-8)
            else:
                y_window = y[i-window+1:i+1]
                x_window = x[i-window+1:i+1]
                # 过滤掉窗口内的NaN and Inf
                valid_indices = np.isfinite(x_window) & np.isfinite(y_window)
                x_window_valid = x_window[valid_indices]
                y_window_valid = y_window[valid_indices]

                if len(x_window_valid) >= 2: # 确保有足够的数据点进行回归
                    cov = np.cov(x_window_valid, y_window_valid)[0, 1] if len(x_window_valid) > 1 else 0
                    var_x = np.var(x_window_valid)
                    # 避免除以零
                    result[i] = cov / (var_x + 1e-8)

        return result

    # 对每个品种分别计算滚动回归贝塔系数
    df['T2'] = np.nan
    for symbol in df['symbol'].unique():
        symbol_data = df[df['symbol'] == symbol]
        indices = symbol_data.index
        beta_values = rolling_beta(symbol_data, 'close', 'amount', n2)
        df.loc[indices, 'T2'] = beta_values

    # 步骤4和5: 计算T2在过去10个周期内的滚动排名，再计算在过去8个周期内的滚动排名
    def rolling_rank(series, window):
        """计算滚动排名并归一化到(0,1]区间"""
        result = np.full_like(series, np.nan)

        for i in range(len(series)):
            if i < window - 1:
                if i > 0:  # 至少有1个点才能计算排名
                    window_data = series[0:i+1]
                    # 过滤掉窗口内的NaN and Inf
                    window_data_valid = window_data[np.isfinite(window_data)]
                    if len(window_data_valid) > 0:
                        # 计算降序排名（最大值排名为1）
                        ranks = np.zeros(len(window_data_valid))
                        for j in range(len(window_data_valid)):
                            ranks[j] = np.sum(window_data_valid >= window_data_valid[j])
                        # 找到当前点在有效数据中的索引
                        current_value = series[i]
                        if np.isfinite(current_value):
                            current_rank_index = np.where(window_data_valid == current_value)[-1][-1] if current_value in window_data_valid else None
                            if current_rank_index is not None:
                                # 归一化排名
                                result[i] = ranks[current_rank_index] / (len(window_data_valid) + 1e-8)
            else:
                window_data = series[i-window+1:i+1]
                # 过滤掉窗口内的NaN and Inf
                window_data_valid = window_data[np.isfinite(window_data)]
                if len(window_data_valid) > 0:
                    # 计算降序排名（最大值排名为1）
                    ranks = np.zeros(len(window_data_valid))
                    for j in range(len(window_data_valid)):
                        ranks[j] = np.sum(window_data_valid >= window_data_valid[j])
                    # 找到当前点在有效数据中的索引
                    current_value = series[i]
                    if np.isfinite(current_value):
                        current_rank_index = np.where(window_data_valid == current_value)[-1][-1] if current_value in window_data_valid else None
                        if current_rank_index is not None:
                            # 归一化排名
                            result[i] = ranks[current_rank_index] / (len(window_data_valid) + 1e-8)

        return result

    # 对每个品种分别计算两次滚动排名
    df['T3'] = np.nan
    df['M2'] = np.nan
    for symbol in df['symbol'].unique():
        symbol_data = df[df['symbol'] == symbol]
        indices = symbol_data.index
        t2_values = symbol_data['T2'].values

        #第一次滚动排名
        t3_values = rolling_rank(t2_values, n3)
        df.loc[indices, 'T3'] = t3_values

        #第二次滚动排名
        m2_values = rolling_rank(t3_values, n4)
        df.loc[indices, 'M2'] = m2_values

    # 步骤6: 计算M1与M2的乘积
    df['X1'] = df['M1'] * df['M2']

    # 步骤7: 计算最低价对X1在过去7个周期内的滚动回归残差
    def rolling_residual(group, y_col, x_col, window):
        """计算滚动回归残差"""
        y = group[y_col].values
        x = group[x_col].values
        result = np.full_like(x, np.nan)

        for i in range(len(x)):
            if i < window - 1:
                if i > 0:  # 至少有2个点才能计算回归
                    start_idx = 0
                    end_idx = i + 1
                    y_window = y[start_idx:end_idx]
                    x_window = x[start_idx:end_idx]
                    # 过滤掉窗口内的NaN and Inf
                    valid_indices = np.isfinite(x_window) & np.isfinite(y_window)
                    x_window_valid = x_window[valid_indices]
                    y_window_valid = y_window[valid_indices]

                    if len(x_window_valid) >= 2:  # 确保有足够的数据点进行回归
                        cov = np.cov(x_window_valid, y_window_valid)[0, 1] if len(x_window_valid) > 1 else 0
                        var_x = np.var(x_window_valid)
                        # 避免除以零
                        beta = cov / (var_x + 1e-8)
                        result[i] = y_window[-1] - beta * x_window[-1]
            else:
                y_window = y[i-window+1:i+1]
                x_window = x[i-window+1:i+1]
                # 过滤掉窗口内的NaN and Inf
                valid_indices = np.isfinite(x_window) & np.isfinite(y_window)
                x_window_valid = x_window[valid_indices]
                y_window_valid = y_window[valid_indices]

                if len(x_window_valid) >= 2: # 确保有足够的数据点进行回归
                    cov = np.cov(x_window_valid, y_window_valid)[0, 1] if len(x_window_valid) > 1 else 0
                    var_x = np.var(x_window_valid)
                    # 避免除以零
                    beta = cov / (var_x + 1e-8)
                    result[i] = y_window[-1] - beta * x_window[-1]

        return result

    # 对每个品种分别计算滚动回归残差
    df['factor'] = np.nan
    for symbol in df['symbol'].unique():
        symbol_data = df[df['symbol'] == symbol]
        indices = symbol_data.index
        residual_values = rolling_residual(symbol_data, 'low', 'X1', n5)
        df.loc[indices, 'factor'] = residual_values

    # 步骤8: 将无穷大值替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()
    return result_df

