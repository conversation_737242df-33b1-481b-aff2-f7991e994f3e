# Alpha299因子 - factor_337
# 原始因子编号: 337
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_337(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha130因子

    参数:
    data_df: 输入的DataFrame
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 单一列参数，本因子不使用单一列，设为None

    返回:
    包含trade_date, time, symbol和factor列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window1': 8,   # vwap滚动均值窗口
        'window2': 12   # high和amount滚动相关系数窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window1 = window_sizes['window1']
    window2 = window_sizes['window2']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 检查必要的列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'high', 'amount', 'volume', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"数据中缺少必要的列: {missing_columns}")

    # 计算vwap (成交量加权平均价)
    # vwap = amount / volume
    # 添加对volume为0的保护
    if 'vwap' not in df.columns:
        df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 转换日期列为datetime类型，用于后续分组和排序
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 按symbol分组，并确保数据按时间排序
    df = df.sort_values(['symbol', 'time'])

    # 1. 计算vwap在过去window1个周期内的滚动均值
    df['vwap_mean'] = df.groupby('symbol')['vwap'].transform(
        lambda x: x.rolling(window=window1, min_periods=w).mean()
    )

    # 2. 计算high和amount在过去window2个周期内的滚动相关系数
    def rolling_corr(group):
        high_series = group['high']
        amount_series = group['amount']
        corr_results = []
        for i in range(len(high_series)):
            window_high = high_series.iloc[max(0, i-window2+1):i+1]
            window_amount = amount_series.iloc[max(0, i-window2+1):i+1]

            # 检查窗口内是否有足够的非NaN数据进行相关性计算
            if len(window_high.dropna()) < 2 or len(window_amount.dropna()) < 2:
                corr_results.append(np.nan)
            else:
                # 计算相关性，并处理常数序列的情况（std=0）
                corr_val = window_high.corr(window_amount)
                # 如果corr_val是NaN（例如，因为其中一个序列是常数），则设为0
                corr_results.append(corr_val if not pd.isna(corr_val) else 0)

        return pd.Series(corr_results, index=group.index)

    df['high_amount_corr'] = df.groupby('symbol').apply(rolling_corr).reset_index(level=0, drop=True)

    # 3. 计算vwap_mean与high_amount_corr的乘积得到Alpha130
    df['factor'] = df['vwap_mean'] * df['high_amount_corr']

    # 4. 将结果中的无穷大值(inf, -inf)替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果DataFrame，只保留需要的列
    # 保留所有行，包括factor为NaN的行，以便观察真实的缺值情况
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

