# Alpha299因子 - factor_85
# 原始因子编号: 85
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_85(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha 111因子：资金流指标EMA差值因子
    参数:
        data_df: 输入的DataFrame，包含必需的列
        w: 可调参数（天数），此处设为None，因为因子参数是固定的
        uni_col: 单一基础列参数，设为None，因为涉及多个列
    返回:
        包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']
    """
    df = data_df.copy()

    # 计算资金流乘数 (MFM)
    numerator = (df['close'] - df['low']) - (df['high'] - df['close'])
    denominator = df['high'] - df['low']
    # 避免分母为0
    df['MFM'] = numerator / (denominator + 1e-8)

    # 计算资金流量 (MFV)
    df['MFV'] = df['MFM'] * df['volume']

    # 计算短期EMA (span=3)
    # EMA计算本身对NaN有一定处理能力，但为保险起见，可以考虑在计算前对MFV进行有限填充，
    # 但考虑到EMA的特性，这里保持原样，让EMA自行处理NaN。
    df['EMA_short'] = df.groupby('symbol')['MFV'].transform(lambda x: x.ewm(span=3, adjust=False).mean())

    # 计算长期EMA (span=10)
    df['EMA_long'] = df.groupby('symbol')['MFV'].transform(lambda x: x.ewm(span=10, adjust=False).mean())

    # 计算因子值
    df['factor'] = df['EMA_long'] - df['EMA_short']

    # 替换±inf为NaN
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], float('nan'))

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

