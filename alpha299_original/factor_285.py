# Alpha299因子 - factor_285
# 原始因子编号: 285
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_285(data_df, w: int | None = 9, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'pctchg_window': 11,     # ts_pctchg窗口
        'delta_window': 9,       # DELTA窗口
        'corr_window': 9         # 滚动相关系数窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    pctchg_window = window_sizes['pctchg_window']     # ts_pctchg窗口
    delta_window = window_sizes['delta_window']       # DELTA窗口
    corr_window = window_sizes['corr_window']         # 滚动相关系数窗口

    df = data_df.copy()

    # 1. 计算收盘价的反正切值
    # atan的定义域是全体实数，不需要特殊处理
    df['X1'] = np.arctan(df['close'])

    # 2. 计算开盘价的百分比变化率 (修正：直接使用 pct_change)
    df['T1'] = df.groupby('symbol')['open'].transform(
        lambda x: x.pct_change(periods=pctchg_window)
    )

    # 3. 计算成交量的差值
    df['T2'] = df.groupby('symbol')['volume'].transform(
        lambda x: x - x.shift(delta_window)
    )

    # 4. 逐元素取较大值
    df['X2'] = df[['T1', 'T2']].max(axis=1)

    # 5. 计算滚动相关系数
    def calc_rolling_corr(group):
        # 在计算corr之前，先处理可能的inf/nan，虽然corr函数本身会处理，但为了稳健性，可以提前处理
        # 这里选择不对X1和X2进行填充，让corr函数自行处理窗口内的nan
        # corr函数在窗口内存在nan时，会忽略nan进行计算，如果窗口内所有值都是nan，则结果为nan
        # 如果窗口内其中一个序列是常数（std=0），corr结果为nan
        # 后续会统一处理corr结果中的nan和inf
        return group['X1'].rolling(corr_window, min_periods=w).corr(group['X2'])

    df['factor'] = df.groupby('symbol').apply(calc_rolling_corr).reset_index(level=0, drop=True)

    # 6. 替换无穷大值为NaN，并将NaN填充为0
    # 根据要求，只有corr结果需要填充0，其他情况保留NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan).fillna(0)

    # 转换日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去重
    # 注意：这里保留了dropna()，如果需要保留所有行（包括原始数据中的NaN），可以移除dropna()
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

