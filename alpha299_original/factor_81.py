# Alpha299因子 - factor_81
# 原始因子编号: 81
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_81(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha99因子：收盘价排名与时序标准化成交量排名协方差的负排名因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'close', 'volume']等列
        w (int | None): 协方差窗口期（5天），默认5
        uni_col (str | None): 基础数据列，默认None（因涉及close和volume两列）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果
    """
    # 检查必要列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'close', 'volume']
    missing_cols = [col for col in required_columns if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    # 三段式混合模型窗口配置
    window_configs = {
        'n_cova': 5,       # 协方差窗口期
        'n_zscore': 20,    # 时序Z-score窗口期
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        n_cova = window_sizes.loc[w, 'n_cova']
        n_zscore = window_sizes.loc[w, 'n_zscore']
    else:
        n_cova = window_configs['n_cova']
        n_zscore = window_configs['n_zscore']

    # 按时间排序确保时间序列操作正确
    data_df = data_df.sort_values(['symbol', 'trade_date', 'time'])

    # 1. 计算收盘价横截面排名 R_C
    data_df['R_C'] = data_df.groupby(['trade_date', 'time'])['close'].transform(
        lambda x: x.rank(method='average') / (x.count() + 1e-8)
    )

    # 2. 计算成交量时序Z-score标准化 V'
    def ts_zscore(x, window):
        # 避免除以零的情况
        std_dev = x.rolling(window=window, min_periods=w).std()
        mean_val = x.rolling(window=window, min_periods=w).mean()
        return (x - mean_val) / (std_dev + 1e-8)

    data_df['V_prime'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: ts_zscore(x, window=n_zscore)
    )

    # 3. 计算标准化成交量横截面排名 R_V'
    data_df['R_V_prime'] = data_df.groupby(['trade_date', 'time'])['V_prime'].transform(
        lambda x: x.rank(method='average') / (x.count() + 1e-8)
    )

    # 4. 计算协方差 Cov(R_C, R_V')
    # pandas rolling.cov会自动处理NaN，但如果窗口内数据为常数，结果可能为NaN
    # 这里不额外处理，依赖后续的NaN/Inf替换
    data_df['Cov'] = data_df.groupby('symbol').apply(
        lambda d: d['R_C'].rolling(window=n_cova, min_periods=w).cov(d['R_V_prime'])
    ).droplevel(0)

    # 5. 计算最终因子：对协方差进行横截面排名并取负
    data_df['factor'] = -data_df.groupby(['trade_date', 'time'])['Cov'].transform(
        lambda x: x.rank(method='average') / (x.count() + 1e-8)
    )

    # 6. 替换±∞为NaN
    data_df.replace([float('inf'), float('-inf')], float('nan'), inplace=True)

    # 恢复日期和时间格式为字符串
    data_df['trade_date'] = data_df['trade_date'].astype(str)
    data_df['time'] = data_df['time'].astype(str)

    # 构建输出DataFrame
    output_df = data_df[['trade_date', 'time', 'symbol', 'factor']].copy()
    return output_df.dropna()

