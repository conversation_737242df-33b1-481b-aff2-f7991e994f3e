# Alpha299因子 - factor_492
# 原始因子编号: 492
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_492(data_df, w: int | None = 14, uni_col: str | None = None):
    """
    计算平均趋向指数 (Average Directional Movement Index, ADX)

    参数:
    data_df: 输入数据DataFrame
    w: 计算ADX所选定的时间周期长度（窗口期），默认为14。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    uni_col: 因为ADX计算需要高开低收数据，所以设为None

    返回:
    包含 ['trade_date', 'time', 'symbol', 'factor'] 的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}
    # 定义所有窗口的基准值
    window_configs = {
        'adx_window': 14
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    adx_window = window_sizes['adx_window']

    # 确保输入数据是DataFrame的副本
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组计算ADX
    result_dfs = []
    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')

        # 1. 计算单周期真实波幅 (TR) 和单周期趋向变动 (+DM, -DM)
        group['tr'] = np.maximum(
            group['high'] - group['low'],
            np.maximum(
                np.abs(group['high'] - group['close'].shift(1).fillna(group['close'])), # 填充shift(1)的nan
                np.abs(group['low'] - group['close'].shift(1).fillna(group['close'])) # 填充shift(1)的nan
            )
        )

        # 计算UpMove和DownMove
        group['up_move'] = group['high'] - group['high'].shift(1).fillna(group['high']) # 填充shift(1)的nan
        group['down_move'] = group['low'].shift(1).fillna(group['low']) - group['low'] # 填充shift(1)的nan

        # 计算+DM和-DM
        group['plus_dm'] = np.where(
            (group['up_move'] > group['down_move']) & (group['up_move'] > 0),
            group['up_move'],
            0
        )
        group['minus_dm'] = np.where(
            (group['down_move'] > group['up_move']) & (group['down_move'] > 0),
            group['down_move'],
            0
        )

        # 2. 计算N周期平滑真实波幅 (TR_N)、N周期平滑正趋向变动 (+DM_N) 和N周期平滑负趋向变动 (-DM_N)
        # 初始化平滑值
        group['tr_n'] = np.nan
        group['plus_dm_n'] = np.nan
        group['minus_dm_n'] = np.nan

        # 计算前N个TR, +DM, -DM的总和作为初始平滑值
        # 确保有足够的历史数据进行计算
        if len(group) >= adx_window:
            # 使用iloc进行位置索引，避免索引错误
            group.iloc[adx_window-1, group.columns.get_loc('tr_n')] = group.iloc[0:adx_window, group.columns.get_loc('tr')].sum()
            group.iloc[adx_window-1, group.columns.get_loc('plus_dm_n')] = group.iloc[0:adx_window, group.columns.get_loc('plus_dm')].sum()
            group.iloc[adx_window-1, group.columns.get_loc('minus_dm_n')] = group.iloc[0:adx_window, group.columns.get_loc('minus_dm')].sum()

            # 使用Wilder平滑法计算后续值
            for i in range(adx_window, len(group)):
                # 使用iloc进行位置索引
                prev_tr_n = group.iloc[i-1, group.columns.get_loc('tr_n')]
                prev_plus_dm_n = group.iloc[i-1, group.columns.get_loc('plus_dm_n')]
                prev_minus_dm_n = group.iloc[i-1, group.columns.get_loc('minus_dm_n')]
                
                group.iloc[i, group.columns.get_loc('tr_n')] = prev_tr_n - (prev_tr_n / (adx_window + 1e-8)) + group.iloc[i, group.columns.get_loc('tr')]
                group.iloc[i, group.columns.get_loc('plus_dm_n')] = prev_plus_dm_n - (prev_plus_dm_n / (adx_window + 1e-8)) + group.iloc[i, group.columns.get_loc('plus_dm')]
                group.iloc[i, group.columns.get_loc('minus_dm_n')] = prev_minus_dm_n - (prev_minus_dm_n / (adx_window + 1e-8)) + group.iloc[i, group.columns.get_loc('minus_dm')]


        # 3. 计算N周期趋向指标 (+DI_N, -DI_N)
        # 避免除以零
        group['plus_di'] = 100 * group['plus_dm_n'] / (group['tr_n'] + 1e-8)
        group['minus_di'] = 100 * group['minus_dm_n'] / (group['tr_n'] + 1e-8)

        # 4. 计算N周期趋向指数 (DX)
        group['di_sum'] = group['plus_di'] + group['minus_di']
        group['di_diff'] = np.abs(group['plus_di'] - group['minus_di'])
        group['dx'] = 100 * group['di_diff'] / (group['di_sum'] + 1e-8)

        # 5. 计算N周期平均趋向指数 (ADX)
        group['adx'] = np.nan

        # 初始ADX值是前N个DX的简单平均
        # 确保有足够的历史数据进行计算
        if len(group) >= 2*adx_window - 1:
             # 确保取平均的窗口内没有nan或inf，使用iloc
            dx_window = group.iloc[adx_window-1:2*adx_window-1, group.columns.get_loc('dx')].replace([np.inf, -np.inf], np.nan).dropna()
            if not dx_window.empty:
                group.iloc[2*adx_window-2, group.columns.get_loc('adx')] = dx_window.mean()
            else:
                 group.iloc[2*adx_window-2, group.columns.get_loc('adx')] = 0 # 如果窗口内都是nan/inf，则设为0

            # 使用Wilder平滑法计算后续ADX值
            for i in range(2*adx_window-1, len(group)):
                prev_adx = group.iloc[i-1, group.columns.get_loc('adx')]
                current_dx = group.iloc[i, group.columns.get_loc('dx')]

                # 处理前一个ADX或当前DX为nan/inf的情况
                if pd.isna(prev_adx) or np.isinf(prev_adx):
                    prev_adx = 0 # 或者其他合理的默认值
                if pd.isna(current_dx) or np.isinf(current_dx):
                    current_dx = 0 # 或者其他合理的默认值

                group.iloc[i, group.columns.get_loc('adx')] = (prev_adx * (adx_window-1) + current_dx) / (adx_window + 1e-8)


        # 最终的ADX值就是我们的因子
        group['factor'] = group['adx']

        # 只保留需要的列
        result_df = group[['trade_date', 'time', 'symbol', 'factor']]
        result_dfs.append(result_df)

    # 合并所有结果
    if result_dfs:
        final_df = pd.concat(result_dfs)
    else:
        final_df = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    # 恢复日期和时间格式为字符串
    final_df['trade_date'] = final_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    final_df['time'] = final_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 去除缺失值
    final_df = final_df.dropna()

    return final_df

