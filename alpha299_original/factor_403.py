# Alpha299因子 - factor_403
# 原始因子编号: 403
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_403(data_df, w: int | None = 3, uni_col: str | None = None):
    """
    计算Alpha9因子

    参数:
    - data_df: 输入数据
    - w: 窗口参数，默认为3。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    - uni_col: 单一列参数，本因子使用多列数据，设为None

    返回:
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 3,
        'pctchg_window': 4
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    delta_window = window_sizes['delta_window']
    pctchg_window = window_sizes['pctchg_window']

    # 复制输入数据，避免修改原始数据
    df = data_df.copy()

    # 检查必要的列是否存在
    required_columns = ['high', 'volume', 'amount']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"缺少必要的列: {col}")

    # 计算vwap (成交量加权平均价)
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 1. 计算vwap的Sigmoid值
    df['sigmoid_vwap'] = 1 / (1 + np.exp(-df['vwap']))

    # 2. 计算volume在过去delta_window个周期内的差值
    df['delta_volume'] = df.groupby('symbol')['volume'].transform(
        lambda x: x - x.shift(delta_window)
    )

    # 3. 取sigmoid_vwap和delta_volume中逐元素的较大值
    df['max_sigmoid_delta'] = np.maximum(df['sigmoid_vwap'], df['delta_volume'])

    # 4. 计算high在过去pctchg_window个周期内的百分比变化率
    df['pctchg_high'] = df.groupby('symbol')['high'].transform(
        lambda x: (x - x.shift(pctchg_window)) / (x.shift(pctchg_window) + 1e-8)
    )

    # 5. 取max_sigmoid_delta和pctchg_high中逐元素的较小值得到Alpha9
    df['factor'] = np.minimum(df['max_sigmoid_delta'], df['pctchg_high'])

    # 6. 将结果中的无穷大值替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 转换日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d').dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S').dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

