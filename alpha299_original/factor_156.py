# Alpha299因子 - factor_156
# 原始因子编号: 156
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_156(
    data_df,
    w: int | None = 26,
    uni_col: str | None = None
):
    """
    计算Alpha 52因子：价格相对位置的累积比率因子

    参数:
        data_df: 输入DataFrame，包含['symbol', 'trade_date', 'time', 'high', 'low', 'close']
        w: 滚动窗口大小（默认26）
        uni_col: 单一基础列参数（本因子不适用，设为None）

    返回:
        包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 窗口配置
        window_configs = {
            'w': 26.0  # 基础窗口参数
        }
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    actual_w = window_sizes['w']
    
    df = data_df.copy()

    # 1. 计算每日平均价格 AvgP
    df['AvgP'] = (df['high'] + df['low'] + df['close']) / (3 + 1e-8)

    # 2. 计算前一日平均价格 dAvgP
    df['dAvgP'] = df.groupby('symbol')['AvgP'].shift(1)

    # 3. 计算向上突破项 TermUp
    df['TermUp'] = np.maximum(0, df['high'] - df['dAvgP'])

    # 4. 计算向下突破项 TermDown
    df['TermDown'] = np.maximum(0, df['dAvgP'] - df['low'])

    # 5. 计算滚动求和（使用实际窗口大小）
    # 使用groupby+rolling组合实现分组滚动
    df['SumUp'] = df.groupby('symbol')['TermUp'].rolling(window=actual_w, min_periods=w).sum().reset_index(level=0, drop=True)
    df['SumDown'] = df.groupby('symbol')['TermDown'].rolling(window=actual_w, min_periods=w).sum().reset_index(level=0, drop=True)

    # 6. 计算最终因子值
    # 避免除以零产生inf
    df['factor'] = (df['SumUp'] / (df['SumDown'] + 1e-8)) * 100

    # 7. 处理SumDown=0情况（替换无穷大为NaN）
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 8. 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 9. 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

