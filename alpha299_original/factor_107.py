# Alpha299因子 - factor_107
# 原始因子编号: 107
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_107(data_df, w: int | None = 12, uni_col: str | None = 'close', 
               alpha: float = 1.0, w_max: float = 300.0, lambda_rate: float = 0.1):
    # 定义所有窗口的基准值
    window_configs = {
        'ma_window': 12        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        nonlocal w_max, lambda_rate, alpha
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    ma_window = window_sizes['ma_window']       # 主窗口

    """
    计算factor_107因子，使用动态窗口系统
    
    参数：
    data_df: 输入数据DataFrame
    w: 基础窗口大小（默认12）
    uni_col: 用于计算的价格列（默认'close'）
    alpha: 动态窗口的非线性调整参数（默认1.0）
    w_max: 动态窗口的绝对上限（默认300.0）
    lambda_rate: 动态窗口的增长率（默认0.1）
    """
    df = data_df.copy()

    # 确保按symbol和时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 计算移动平均
    df[f'MA{ma_window}'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=ma_window, min_periods=w).mean()
    )

    # 定义处理每个symbol的函数
    def process_group(group):
        # 生成x序列（1到窗口长度）
        group['x'] = np.arange(1, len(group) + 1)
        # 计算滚动协方差和方差
        group['cov'] = group[f'MA{ma_window}'].rolling(window=ma_window, min_periods=w).cov(group['x'])
        group['var'] = group['x'].rolling(window=ma_window, min_periods=w).var()
        # 计算回归斜率
        # 避免除以0，并处理可能的inf/nan结果
        group['factor'] = group['cov'] / (group['var'] + 1e-8)
        # 清理临时列
        group.drop(columns=['x', 'cov', 'var'], inplace=True)
        return group

    # 应用处理函数并重置索引
    df = df.groupby('symbol').apply(process_group).reset_index(drop=True)

    # 处理无效值，将inf/-inf/nan替换为nan
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

