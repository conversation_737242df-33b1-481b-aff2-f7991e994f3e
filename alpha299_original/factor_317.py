# Alpha299因子 - factor_317
# 原始因子编号: 317
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_317(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算因子值

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数，本因子涉及多列，设为None

    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 特殊情况：如果max_base == min_base，避免除零错误
        if max_base == min_base:
            base_val = min_base
            if w <= base_val:
                # 对于小于等于基准的w，返回w和base_val的最大值
                return {k: max(1, int(max(w, base_val))) for k in window_configs.keys()}
            else:
                # 对于大于基准的w，使用简单的比例缩放
                scale_factor = min(w / base_val, w_max / base_val)
                return {k: max(1, int(min(base_val * scale_factor, w_max))) for k in window_configs.keys()}
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'pctchg_window': 7      # 百分比变化率窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    pctchg_period = window_sizes['pctchg_window']  # 百分比变化率周期

    # 检查必要列
    required_cols = ['open', 'high', 'low', 'close', 'volume', 'amount']
    if not all(col in data_df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in data_df.columns]
        raise ValueError(f"输入数据缺少必要列: {missing}")

    df = data_df.copy()

    # 计算VWAP（成交量加权平均价）
    # 保护分母不为0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 按symbol分组并按时间排序
    df = df.sort_values(by=['symbol', 'time'])

    # 计算pctchg_period周期百分比变化率
    # 保护分母不为0
    shifted_vwap = df.groupby('symbol')['vwap'].shift(pctchg_period)
    df['factor'] = (df['vwap'] - shifted_vwap) / (shifted_vwap + 1e-8)

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并删除无效值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

