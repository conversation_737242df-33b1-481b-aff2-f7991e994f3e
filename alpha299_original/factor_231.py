# Alpha299因子 - factor_231
# 原始因子编号: 231
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_231(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha153因子
    
    参数:
        data_df: 输入数据DataFrame
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一基础数据列（本因子不适用，保留为None）
    
    返回:
        包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'mean_window': 20,  # volume ts_mean窗口
        'zscore_window': 20,  # open ts_zscore窗口
        'delta_window': 8     # high delta窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    mean_window = window_sizes['mean_window']
    zscore_window = window_sizes['zscore_window']
    delta_window = window_sizes['delta_window']
    
    df = data_df.copy()

    # 计算vwap（成交量加权平均价）= amount / volume
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 计算volume的mean_window日ts_mean
    df['volume_mean'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=mean_window, min_periods=w).mean())

    # 计算V_norm = volume / volume_mean
    df['V_norm'] = df['volume'] / (df['volume_mean'] + 1e-8)

    # 计算open的ts_zscore(zscore_window)
    open_mean = df.groupby('symbol')['open'].transform(
        lambda x: x.rolling(window=zscore_window, min_periods=w).mean())
    open_std = df.groupby('symbol')['open'].transform(
        lambda x: x.rolling(window=zscore_window, min_periods=w).std(ddof=0))  # 总体标准差
    df['O_zscore'] = (df['open'] - open_mean) / (open_std + 1e-8)

    # 计算T1 = O_zscore - V_norm
    df['T1'] = df['O_zscore'] - df['V_norm']

    # 计算T2 = max(V_norm, vwap)
    df['T2'] = df[['V_norm', 'vwap']].max(axis=1)

    # 计算X1 = max(T1, T2)
    df['X1'] = df[['T1', 'T2']].max(axis=1)

    # 计算delta(high, delta_window)
    df['high_shifted'] = df.groupby('symbol')['high'].transform(
        lambda x: x.shift(delta_window))
    df['X2'] = df['high'] - df['high_shifted']

    # 计算Alpha153 = X1 * X2
    df['factor'] = df['X1'] * df['X2']

    # 处理无穷大值和NaN值
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], float('nan'))


    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].astype('string')
    df['time'] = df['time'].astype('string')

    # 选择需要的列并处理空值
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

