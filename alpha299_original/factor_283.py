# Alpha299因子 - factor_283
# 原始因子编号: 283
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_283(data_df, w: int | None = 8, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'mean_window': 8,        # 滚动均值窗口
        'corr_window': 12        # 滚动相关系数窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    mean_window = window_sizes['mean_window']       # 滚动均值窗口
    corr_window = window_sizes['corr_window']       # 滚动相关系数窗口

    # 检查必需列是否存在
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    for col in required_columns:
        if col not in data_df.columns:
            raise ValueError(f"输入数据中缺少必需的列：{col}")

    # 确保数据按时间排序
    data_df = data_df.sort_values(by=['trade_date', 'time']).reset_index(drop=True)

    # 计算典型价格 (high+low+close)/3
    data_df['typical_price'] = (data_df['high'] + data_df['low'] + data_df['close']) / (3 + 1e-8)

    # 计算typical_price * volume
    data_df['typical_price_times_volume'] = data_df['typical_price'] * data_df['volume']

    # 计算累计典型价格*volume和累计volume，按symbol分组
    data_df['cum_typical_volume'] = data_df.groupby('symbol')['typical_price_times_volume'].cumsum()
    data_df['cum_volume'] = data_df.groupby('symbol')['volume'].cumsum()

    # 计算vwap
    data_df['vwap'] = data_df['cum_typical_volume'] / (data_df['cum_volume'] + 1e-8)

    # 计算volume的mean_window周期滚动均值
    data_df['volume_mean'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=mean_window, min_periods=w).mean()
    )

    # 计算vwap和volume_mean的corr_window周期滚动相关系数
    def compute_corr(group):
        # 检查窗口内是否有常数序列或inf/nan
        def safe_corr(vwap_window, volume_mean_window):
            if len(vwap_window) == 0 or len(volume_mean_window) == 0:
                return float('nan')
            if vwap_window.std() == 0 or volume_mean_window.std() == 0:
                return 0.0
            # 检查是否有inf或nan
            if vwap_window.isnull().any() or volume_mean_window.isnull().any() or \
               (vwap_window == float('inf')).any() or (vwap_window == -float('inf')).any() or \
               (volume_mean_window == float('inf')).any() or (volume_mean_window == -float('inf')).any():
                return float('nan') # 或者其他合适的处理，这里保留nan
            return vwap_window.corr(volume_mean_window)

        # 创建结果序列
        result = []
        for i in range(len(group)):
            # 确定窗口范围
            start_idx = max(0, i - corr_window + 1)
            end_idx = i + 1
            
            # 提取窗口数据
            vwap_window = group['vwap'].iloc[start_idx:end_idx]
            volume_mean_window = group['volume_mean'].iloc[start_idx:end_idx]
            
            # 计算相关系数
            corr_val = safe_corr(vwap_window, volume_mean_window)
            result.append(corr_val)
        
        return pd.Series(result, index=group.index)

    alpha36 = data_df.groupby('symbol').apply(compute_corr).droplevel('symbol')

    # 处理无穷大值和nan值，将inf/-inf替换为nan，并将nan替换为0
    data_df['factor'] = alpha36.replace([float('inf'), -float('inf')], float('nan'))
    # 注意：这里根据要求，不强制将nan填充为0，保留真实的缺失情况。
    # 如果需要填充，可以在这里添加：.fillna(0)

    # 恢复日期和时间格式为字符串
    data_df['trade_date'] = data_df['trade_date'].astype('string')
    data_df['time'] = data_df['time'].astype('string')

    # 构造输出DataFrame
    output_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

