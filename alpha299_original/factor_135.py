# Alpha299因子 - factor_135
# 原始因子编号: 135
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_135(data_df, w: int | None = 2, uni_col: str | None = 'close'):
    """
    计算Alpha25因子，基于价格动量、成交量相对强度和长期收益表现的组合。

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'close', 'volume']
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 用于计算的核心基础数据列，默认为'close'

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 2,         # 价格动量差分窗口
        'zscore_window': 2,        # Z-score标准化窗口
        'volume_ma_window': 3,     # 成交量移动平均窗口
        'decay_window': 2,         # 线性衰减窗口
        'ret_sum_window': 20       # 收益率累计窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']
    zscore_window = window_sizes['zscore_window']
    volume_ma_window = window_sizes['volume_ma_window']
    decay_window = window_sizes['decay_window']
    ret_sum_window = window_sizes['ret_sum_window']

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'close', 'volume']
    missing_cols = [col for col in required_cols if col not in data_df.columns]
    if missing_cols:
        raise KeyError(f"输入数据缺少必要列: {missing_cols}")

    df = data_df.copy()

    # 1. 计算R'_1: rank_cs(ts_zscore(Delta(Close_t, delta_window), zscore_window))
    # 计算价格动量
    df['delta_close'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x - x.shift(delta_window)
    )

    # 计算滚动标准化（使用更大的epsilon防止除零）
    df['zscore_delta'] = df.groupby('symbol')['delta_close'].transform(
        lambda x: (x - x.rolling(zscore_window, min_periods=w).mean()) /
                  (x.rolling(zscore_window, min_periods=w).std() + 1e-8)  # 增大epsilon
    )
    # 处理zscore_delta中的inf和nan
    df['zscore_delta'] = df['zscore_delta'].replace([np.inf, -np.inf], np.nan)

    # 计算横截面排名（使用Pandas内置rank方法处理NaN）
    df['R1'] = df.groupby('trade_date')['zscore_delta'].transform(
        lambda x: x.rank(pct=True, na_option='top')  # 使用Pandas rank处理NaN
    )

    # 2. 计算R2: 1 - rank_cs(DecayLinear(V'_t, decay_window))
    # 计算V'_t = Volume_t / MA(Volume_t)
    df['volume_ma'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(volume_ma_window, min_periods=w).mean()
    )
    # 确保分母不为0或nan
    df['V_t'] = df['volume'] / (df['volume_ma'].replace(0, 1e-8).fillna(1e-8) + 1e-8)  # 增大epsilon并处理nan

    # 计算DecayLinear(V'_t, decay_window)
    def decay_linear(x):
        x = np.array(x)
        # 检查是否有inf或nan
        if np.isinf(x).any() or np.isnan(x).any():
            return np.nan
        if len(x) == 0:
            return np.nan
        try:
            weights = np.arange(1, len(x)+1) / (len(x)*(len(x)+1)/2 + 1e-8) # 增大epsilon
        except ZeroDivisionError:
            return np.nan
        # 确保点积结果不为inf或nan
        result = np.dot(x, weights)
        if np.isinf(result) or np.isnan(result):
            return np.nan
        return result

    df['decay_V'] = df.groupby('symbol')['V_t'].transform(
        lambda x: x.rolling(decay_window, min_periods=w).apply(decay_linear, raw=False)
    )
    # 处理decay_V中的inf和nan
    df['decay_V'] = df['decay_V'].replace([np.inf, -np.inf], np.nan)

    # 计算横截面排名的逆（使用Pandas rank方法）
    df['R2'] = df.groupby('trade_date')['decay_V'].transform(
        lambda x: x.rank(pct=True, na_option='top')  # 使用Pandas rank处理NaN
    )
    df['R2'] = 1 - df['R2']

    # 3. 计算R3: 1 + rank_cs(sum(Ret_t, ret_sum_window))
    # 计算Ret_t = pct_chg
    df['Ret_t'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.pct_change()
    )
    # 处理Ret_t中的inf和nan
    df['Ret_t'] = df['Ret_t'].replace([np.inf, -np.inf], np.nan)

    # 计算累计收益率
    df['ret_sum'] = df.groupby('symbol')['Ret_t'].transform(
        lambda x: x.rolling(ret_sum_window, min_periods=w).sum()
    )
    # 处理ret_sum中的inf和nan
    df['ret_sum'] = df['ret_sum'].replace([np.inf, -np.inf], np.nan)

    # 计算横截面排名加1（使用Pandas rank方法）
    df['R3'] = df.groupby('trade_date')['ret_sum'].transform(
        lambda x: x.rank(pct=True, na_option='top')  # 使用Pandas rank处理NaN
    )
    df['R3'] = 1 + df['R3']

    # 4. 计算最终因子值
    # 确保R1, R2, R3相乘前没有inf或nan
    df['factor'] = - (df['R1'] * df['R2'] * df['R3'])

    # 处理最终因子值中的无效值（±inf和nan）
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 转换日期时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame（保留所有数据，不进行dropna）
    output_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return output_df

