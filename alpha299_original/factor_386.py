# Alpha299因子 - factor_386
# 原始因子编号: 386
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_386(data_df, w: int | None = 5, uni_col: str | None = 'close'):
    """
    计算Alpha65因子

    Alpha65 = ts_corr(7, add(ts_zscore(volume, 20), ts_zscore(close, 20)), sqrt(ts_pctchg(close, 5)))

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 价格列，默认为'close'
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window_zscore': 20,  # ts_zscore窗口
        'window_corr': 7,     # ts_corr窗口
        'window_pctchg': 5    # ts_pctchg窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window_zscore = window_sizes['window_zscore']
    window_corr = window_sizes['window_corr']
    window_pctchg = window_sizes['window_pctchg']

    df = data_df.copy()

    # 确保日期时间格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组计算
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        group = group.sort_values('time')

        # 1. 计算成交量在过去20个周期内的滚动标准化
        volume_mean = group['volume'].rolling(window=window_zscore, min_periods=w).mean()
        volume_std = group['volume'].rolling(window=window_zscore, min_periods=w).std()
        # 避免除以0
        volume_zscore = (group['volume'] - volume_mean) / (volume_std + 1e-8)

        # 2. 计算收盘价在过去20个周期内的滚动标准化
        close_mean = group[uni_col].rolling(window=window_zscore, min_periods=w).mean()
        close_std = group[uni_col].rolling(window=window_zscore, min_periods=w).std()
        # 避免除以0
        close_zscore = (group[uni_col] - close_mean) / (close_std + 1e-8)

        # 3. 计算标准化后的成交量与标准化后的收盘价的和
        x1 = volume_zscore + close_zscore

        # 4. 计算收盘价在过去5个周期内的百分比变化率
        pct_change = group[uni_col].pct_change(window_pctchg)

        # 5. 计算pct_change的绝对值的平方根
        # 确保输入np.sqrt的值非负
        x2 = np.sqrt(np.abs(pct_change))

        # 6. 计算x1和x2在过去7个周期内的滚动相关系数
        # 创建一个存储相关系数的数组
        corr_values = np.full(len(group), np.nan)

        for i in range(window_corr - 1, len(group)):
            x1_window = x1.iloc[i-window_corr+1:i+1].values
            x2_window = x2.iloc[i-window_corr+1:i+1].values

            # 排除包含NaN的情况
            valid_indices = ~(np.isnan(x1_window) | np.isnan(x2_window))
            if np.sum(valid_indices) > 1:  # 确保至少有两个有效数据点才能计算相关系数
                try:
                    # 计算相关系数，这里面的除法由pearsonr内部处理，我们不修改
                    # 如果窗口内数据为常数，pearsonr会返回NaN，这是合理的
                    corr, _ = pearsonr(x1_window[valid_indices], x2_window[valid_indices])
                    corr_values[i] = corr
                except:
                    # 如果数据不足以计算相关系数，保持为NaN
                    pass

        # 创建结果DataFrame
        result = pd.DataFrame({
            'trade_date': group['trade_date'],
            'time': group['time'],
            'symbol': symbol,
            'factor': corr_values
        })

        # 7. 将结果中的无穷大值替换为NaN
        result['factor'] = result['factor'].replace([np.inf, -np.inf], np.nan)

        result_dfs.append(result)

    # 合并所有结果
    result_df = pd.concat(result_dfs, ignore_index=True)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df[['trade_date', 'time', 'symbol', 'factor']]

