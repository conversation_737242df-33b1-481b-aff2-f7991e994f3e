# Alpha299因子 - factor_346
# 原始因子编号: 346
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_346(data_df, w: int | None = 9, uni_col: str | None = None):
    """
    计算Alpha15因子

    参数:
    data_df: 输入数据
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为15天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 单一列参数，本因子不使用单一列，设为None

    返回:
    包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'n1': 15,  # delta(vwap, 15)
        'n2': 15,  # ts_zscore(Y1, 15)
        'n3': 9,   # ts_max(X3, 9)
        'n4': 9,   # ts_cov(9, vwap, amount) 和 ts_zscore(Y5, 9)
        'n5': 14   # ts_std(X6, 14)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']

    # 复制输入数据，避免修改原始数据
    df = data_df.copy()

    # 确保数据中包含必要的列
    required_columns = ['vwap', 'amount', 'volume']
    missing_columns = [col for col in required_columns if col not in df.columns]

    # 如果缺少vwap列，尝试计算它
    if 'vwap' in missing_columns and 'volume' in df.columns and 'amount' in df.columns:
        # 计算vwap = amount / volume
        df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
        if 'vwap' in missing_columns:
             missing_columns.remove('vwap')

    # 如果仍有缺失列，则报错
    if missing_columns:
        raise ValueError(f"缺少必要的列: {missing_columns}")

    # 将日期时间列转换为datetime格式，以便进行时间序列操作
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 按照因子计算步骤实现

    # 1. 计算vwap在过去15个周期内的差值: Y1 = delta(vwap, 15)
    df['Y1'] = df.groupby('symbol')['vwap'].transform(
        lambda x: x - x.shift(n1)
    )

    # 2. 对Y1进行过去15个周期的滚动标准化: X1 = ts_zscore(Y1, 15)
    def ts_zscore(series, window):
        # 计算滚动均值和标准差
        rolling_mean = series.rolling(window=window, min_periods=w).mean()
        rolling_std = series.rolling(window=window, min_periods=w).std()
        # 避免除以零
        return (series - rolling_mean) / (rolling_std + 1e-8)

    df['X1'] = df.groupby('symbol')['Y1'].transform(
        lambda x: ts_zscore(x, n2)
    )

    # 3. 计算成交额(amount)的截面排名: X2 = rank(amount)
    df['X2'] = df.groupby('trade_date')['amount'].transform(
        lambda x: x.rank(method='average', pct=True)
    )

    # 4. 取X1和X2中逐元素的较大值: X3 = gp_max(X1, X2)
    df['X3'] = df[['X1', 'X2']].max(axis=1)

    # 5. 计算X3在过去9个周期内的滚动最大值: X4 = ts_max(X3, 9)
    df['X4'] = df.groupby('symbol')['X3'].transform(
        lambda x: x.rolling(window=n3, min_periods=w).max()
    )

    # 6. 计算vwap和amount在过去9个周期内的滚动协方差: Y5 = ts_cov(9, vwap, amount)
    def rolling_cov(x, y, window):
        # 将两个序列合并为DataFrame进行计算
        combined = pd.DataFrame({'x': x, 'y': y})
        # 计算滚动协方差
        cov_result = combined['x'].rolling(window=window, min_periods=w).cov(combined['y'])
        # 替换inf和nan为0，因为协方差为0表示没有线性关系
        return cov_result.replace([np.inf, -np.inf], np.nan).fillna(0)

    df['Y5'] = df.groupby('symbol').apply(
        lambda group: rolling_cov(group['vwap'], group['amount'], n4)
    ).reset_index(level=0, drop=True)


    # 7. 对Y5进行过去9个周期的滚动标准化: X5 = ts_zscore(Y5, 9)
    df['X5'] = df.groupby('symbol')['Y5'].transform(
        lambda x: ts_zscore(x, n4)
    )

    # 8. 取X4和X5中逐元素的较大值: X6 = gp_max(X4, X5)
    df['X6'] = df[['X4', 'X5']].max(axis=1)

    # 9. 计算X6在过去14个周期内的滚动标准差得到Alpha15: Alpha15 = ts_std(X6, 14)
    df['factor'] = df.groupby('symbol')['X6'].transform(
        lambda x: x.rolling(window=n5, min_periods=w).std()
    )

    # 10. 将结果中的无穷大值(inf, -inf)替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果DataFrame，只包含所需的列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    return result_df

