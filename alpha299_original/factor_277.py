# Alpha299因子 - factor_277
# 原始因子编号: 277
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_277(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha27因子，基于滚动统计和回归分析。

    参数:
    data_df (pd.DataFrame): 包含原始数据的DataFrame。
    w (int | None): 核心时间窗口参数，用于推导其他窗口大小，默认值为14。
    uni_col (str | None): 单一基础数据列参数，此处设为None，因为因子涉及多列。

    返回:
    pd.DataFrame: 包含因子值的DataFrame，列包括trade_date, time, symbol, factor。
    """
    # 定义所有窗口的基准值
    window_configs = {
        'beta_window': 6,        # ts_regbeta的窗口
        'zscore_window': 14      # ts_zscore和ts_std的窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    beta_window = window_sizes['beta_window']        # ts_regbeta的窗口
    zscore_window = window_sizes['zscore_window']    # ts_zscore和ts_std的窗口

    # 按symbol分组处理
    df = data_df.copy()

    # 步骤1-2: 对volume和high进行zscore_window天滚动Z-score
    df['T1a'] = df.groupby('symbol')['volume'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std(ddof=1).replace([np.inf, -np.inf, np.nan], 0) + 1e-8)
    )
    df['T1b'] = df.groupby('symbol')['high'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std(ddof=1).replace([np.inf, -np.inf, np.nan], 0) + 1e-8)
    )

    # 步骤3: 计算T1a + T1b
    df['T1'] = df['T1a'] + df['T1b']

    # 步骤4: 计算amount的反正切
    df['T2'] = np.arctan(df['amount'].replace([np.inf, -np.inf, np.nan], 0))

    # 步骤5: T1 * T2
    df['T3'] = df['T1'] * df['T2']

    # 步骤6: 取绝对值的自然对数
    df['X1'] = np.log(np.abs(df['T3']) + 1e-8)

    # 步骤7: 计算close/open的ratio
    df['T4'] = df['close'] / (df['open'].replace([np.inf, -np.inf, np.nan], 0) + 1e-8)

    # 步骤8: 计算T4的滚动标准差
    df['T5'] = df.groupby('symbol')['T4'].transform(
        lambda x: x.rolling(window=zscore_window, min_periods=w).std(ddof=1).replace([np.inf, -np.inf, np.nan], 0)
    )

    # 步骤9: 对low进行zscore_window天滚动Z-score
    df['T5a'] = df.groupby('symbol')['low'].transform(
        lambda x: (x - x.rolling(window=zscore_window, min_periods=w).mean()) /
                  (x.rolling(window=zscore_window, min_periods=w).std(ddof=1).replace([np.inf, -np.inf, np.nan], 0) + 1e-8)
    )

    # 步骤10: T5 - T5a
    df['X2'] = df['T5'] - df['T5a']

    # 步骤11: 计算X2对X1的滚动回归Beta
    def calc_beta(group):
        X = group['X1']
        Y = group['X2']
        # 计算滚动协方差和方差
        cov = X.rolling(window=beta_window, min_periods=w).cov(Y)
        var = X.rolling(window=beta_window, min_periods=w).var(ddof=1)
        # 避免除以零，并将inf/nan结果替换为0
        beta = cov / (var + 1e-8)
        return beta.replace([np.inf, -np.inf, np.nan], 0)


    df['factor'] = df.groupby('symbol').apply(calc_beta).reset_index(level=0, drop=True)

    # 步骤12: 替换inf为NaN (此步骤在calc_beta中已处理，但保留作为额外的清理)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 输出所需的列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

