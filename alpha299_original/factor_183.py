# Alpha299因子 - factor_183
# 原始因子编号: 183
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_183(data_df, w=4, uni_col=None):
    # 窗口配置
    window_configs = {
        'delta_vwap_window': 4.0,      # Delta4 VWAP的滞后窗口
        'decay_linear_7': 7.0,         # DecayLinear窗口7
        'decay_linear_11': 11.0,       # DecayLinear窗口11
        'ts_rank_7': 7.0               # ts_rank窗口7
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限，如果w为None则使用默认基准值
        w1 = max(1.0, float(w1_input if w1_input is not None else 11.0))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    delta_vwap_window = window_sizes['delta_vwap_window']
    decay_linear_7 = window_sizes['decay_linear_7']
    decay_linear_11 = window_sizes['decay_linear_11']
    ts_rank_7 = window_sizes['ts_rank_7']
    
    df = data_df.copy()

    # 检查必要列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'amount', 'volume']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"缺少必要列: {col}")

    # 计算VWAP
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 计算Delta VWAP（使用实际窗口大小）
    df['delta_vwap'] = df.groupby('symbol')['vwap'].transform(lambda x: x - x.shift(delta_vwap_window))

    # 线性衰减函数（根据实际窗口长度动态生成权重）
    def linear_decay(x):
        n = len(x)
        # 确保分母不为零
        denominator = n * (n + 1)
        if denominator == 0:
            return np.nan  # 或者其他合理的处理方式
        weights = np.linspace(1, n, n) * 2 / (denominator + 1e-8)
        return np.dot(x, weights)

    # 应用DecayLinear到Delta VWAP（使用实际窗口大小）
    # 在应用滚动函数前，先对输入进行inf/nan处理
    df['delta_vwap_cleaned'] = df['delta_vwap'].replace([np.inf, -np.inf], np.nan)
    df['dl1'] = df.groupby('symbol')['delta_vwap_cleaned'].transform(
        lambda x: x.rolling(window=decay_linear_7, min_periods=w).apply(linear_decay, raw=True)
    )

    # 计算跨截面排名R1
    df['r1'] = df.groupby('time')['dl1'].transform(lambda x: x.rank(pct=True))

    # 计算Term_t
    denominator = df['open'] - (df['high'] + df['low']) / 2
    # 确保分母不为零
    df['term_t'] = (df['low'] - df['vwap']) / (denominator + 1e-8)

    # 应用DecayLinear到Term_t（使用实际窗口大小）
    # 在应用滚动函数前，先对输入进行inf/nan处理
    df['term_t_cleaned'] = df['term_t'].replace([np.inf, -np.inf], np.nan)
    df['dl2'] = df.groupby('symbol')['term_t_cleaned'].transform(
        lambda x: x.rolling(window=decay_linear_11, min_periods=w).apply(linear_decay, raw=True)
    )

    # 计算ts_rank R2（使用实际窗口大小）
    def ts_rank(x, window):
        # 在计算rank前，先对输入进行inf/nan处理
        x_cleaned = pd.Series(x).replace([np.inf, -np.inf], np.nan)
        return x_cleaned.rolling(window=window, min_periods=w).apply(
            lambda y: pd.Series(y).rank(pct=True).iloc[-1] if not pd.Series(y).isnull().all() else np.nan
        )

    df['r2'] = df.groupby('symbol')['dl2'].transform(lambda x: ts_rank(x, ts_rank_7))

    # 计算最终因子
    df['factor'] = -(df['r1'] + df['r2'])

    # 处理无效值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    # 保留原始的dropna行为，只在最后一步移除包含nan的行
    # df.dropna(subset=['factor'], inplace=True) # 暂时注释掉，根据需求决定是否在最后移除

    # 转换日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']]

    # 在返回前移除包含nan的行，如果需要
    output_df.dropna(subset=['factor'], inplace=True)

    return output_df

