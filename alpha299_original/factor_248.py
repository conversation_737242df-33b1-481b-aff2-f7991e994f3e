# Alpha299因子 - factor_248
# 原始因子编号: 248
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_248(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha_178因子
    参数:
        data_df (DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'high', 'close']等字段
        w (int | None): 核心可调参数（本因子中无天数参数，故设为None）
        uni_col (str | None): 单一基础列参数（本因子使用high和close，故设为None）
    """
    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'high', 'close']
    if not all(col in data_df.columns for col in required_cols):
        raise ValueError(f"输入数据缺少必要列: {required_cols}")

    # 按symbol和时间排序
    df = data_df.copy()
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 计算延迟16期的high
    df['I1'] = df.groupby('symbol')['high'].shift(16)

    # 计算残差：close - beta*I1
    # beta = Cov(I1, close) / Var(I1)
    def compute_residuals(group):
        # 计算滚动协方差和方差
        # 确保输入到cov和var的值不是inf或nan
        group_cleaned = group[['I1', 'close']].replace([np.inf, -np.inf], np.nan)

        cov = group_cleaned['I1'].rolling(window=14, min_periods=w).cov(group_cleaned['close'])
        var = group_cleaned['I1'].rolling(window=14, min_periods=w).var()

        # 处理var为0的情况，避免除以零
        beta = cov / (var + 1e-8)

        # 处理beta中的inf和nan
        beta = beta.replace([np.inf, -np.inf], np.nan)

        # 计算残差，确保I1和close不是inf或nan
        residuals = group_cleaned['close'] - beta * group_cleaned['I1']

        return residuals

    # 应用计算
    df['factor'] = df.groupby('symbol').apply(compute_residuals).reset_index(level=0, drop=True)

    # 处理无效值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 保留必要列并处理日期格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

