# Alpha299因子 - factor_384
# 原始因子编号: 384
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_384(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha63因子

    Alpha63 = ts_regres(delay(high, 10), mul(open_price, close), 10)

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，此因子不使用单一列，设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delay_period': 10,    # delay窗口
        'regres_window': 10    # ts_regres窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delay_period = window_sizes['delay_period']
    regres_window = window_sizes['regres_window']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期时间列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 1. 计算delay(high, 10)
    df['high_delay'] = df.groupby('symbol')['high'].transform(lambda x: x.shift(delay_period))

    # 2. 计算mul(open_price, close)
    df['open_close_mul'] = df['open'] * df['close']

    # 3. 计算ts_regres(X1, X2, 10)，即计算X2对X1的滚动回归残差
    result_df = pd.DataFrame()

    # 按symbol分组进行处理
    for symbol, group in df.groupby('symbol'):
        group = group.sort_values('time')

        # 初始化残差列
        group['residuals'] = np.nan

        # 滚动窗口回归
        for i in range(regres_window, len(group)):
            window = group.iloc[i-regres_window:i]
            X = window['high_delay'].values.reshape(-1, 1)
            y = window['open_close_mul'].values

            # 检查是否有足够的有效数据进行回归
            # 过滤掉窗口内包含NaN或Inf的数据点
            valid_indices = np.isfinite(X.flatten()) & np.isfinite(y)
            X_valid = X[valid_indices]
            y_valid = y[valid_indices]

            if len(X_valid) < 2:
                continue

            try:
                # 进行线性回归
                # 检查X的标准差是否接近于零，避免除零
                if np.std(X_valid.flatten()) < 1e-8:
                    continue # 如果标准差接近零，无法进行回归
                slope, intercept, _, _, _ = stats.linregress(X_valid.flatten(), y_valid)
                # 计算当前点的残差
                current_high_delay = group.iloc[i]['high_delay']
                current_open_close_mul = group.iloc[i]['open_close_mul']

                # 检查当前点是否有效
                if np.isfinite(current_high_delay) and np.isfinite(current_open_close_mul):
                    predicted = slope * current_high_delay + intercept
                    residual = current_open_close_mul - predicted
                    group.iloc[i, group.columns.get_loc('residuals')] = residual
                else:
                    # 如果当前点无效，残差为NaN
                    group.iloc[i, group.columns.get_loc('residuals')] = np.nan

            except:
                # 回归失败时，保持为NaN
                continue

        # 将结果添加到结果DataFrame
        result_df = pd.concat([result_df, group[['trade_date', 'time', 'symbol', 'residuals']]])

    # 重命名残差列为factor
    result_df = result_df.rename(columns={'residuals': 'factor'})

    # 替换无穷大值为NaN
    result_df['factor'] = result_df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回最终结果，仅包含所需列
    return result_df[['trade_date', 'time', 'symbol', 'factor']]

