# Alpha299因子 - factor_226
# 原始因子编号: 226
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_226(data_df, w: int | None = 9, uni_col: str | None = None):
    """
    计算因子值：基于Z-score和价格变化的组合
    
    参数:
        data_df: 输入数据DataFrame
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一基础数据列（本因子不适用，保留为None）
    
    返回:
        包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 20,  # ts_zscore窗口
        'pctchg_window': 9    # ts_pctchg窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n_zscore = window_sizes['zscore_window']  # ts_zscore窗口
    n_pctchg = window_sizes['pctchg_window']  # ts_pctchg窗口

    # 数据分组处理
    df = data_df.copy()

    # 1. 计算low的滚动Z-score
    df['low_zscore'] = df.groupby('symbol')['low'].transform(
        lambda x: (x - x.rolling(window=n_zscore, min_periods=w).mean()) /
                   (x.rolling(window=n_zscore, min_periods=w).std() + 1e-8)
    )

    # 2. 计算volume的滚动Z-score
    df['volume_zscore'] = df.groupby('symbol')['volume'].transform(
        lambda x: (x - x.rolling(window=n_zscore, min_periods=w).mean()) /
                   (x.rolling(window=n_zscore, min_periods=w).std() + 1e-8)
    )

    # 3. 计算T1 = low_zscore - volume_zscore
    df['T1'] = df['low_zscore'] - df['volume_zscore']

    # 4. 计算sigmoid(T1)
    df['X1'] = 1 / (1 + np.exp(-df['T1']))

    # 5. 计算log(close)
    # 确保close大于0，避免log(0)或log(负数)
    df['log_close'] = np.log(np.abs(df['close']) + 1e-8)

    # 6. 计算log_close的n_pctchg周期百分比变化
    df['X2'] = df.groupby('symbol')['log_close'].transform(
        lambda x: x.pct_change(periods=n_pctchg)
    )

    # 7. 计算Alpha147 = X1 - X2
    df['factor'] = df['X1'] - df['X2']

    # 8. 处理无穷大值和NaN值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    # 在计算过程中可能产生NaN，这里不填充，保留真实的缺失情况

    # 日期时间格式转换
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建结果DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

