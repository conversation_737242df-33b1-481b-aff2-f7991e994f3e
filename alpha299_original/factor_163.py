# Alpha299因子 - factor_163
# 原始因子编号: 163
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_163(data_df, w: int | None = 3, uni_col: str | None = None):
    """
    计算Alpha 5因子：高点与成交量时序排名的相关性因子
    """
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 窗口配置
        window_configs = {
            'ts_rank_window': 5.0,  # w
            'corr_window': 5.0,     # w
            'max_window': 3.0       # 固定窗口，不基于w推导
        }
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    ts_rank_window = window_sizes['ts_rank_window']
    corr_window = window_sizes['corr_window']
    max_window = window_sizes['max_window']

    # 数据预处理
    df = data_df.copy()
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 计算时序排名
    def ts_rank(x):
        # 确保输入不是全NaN或全Inf
        if np.all(np.isnan(x)) or np.all(np.isinf(x)):
            return np.nan
        # 过滤掉inf和nan再计算rank
        valid_x = x[np.isfinite(x)]
        if len(valid_x) == 0:
            return np.nan
        return rankdata(valid_x, method='min')[-1]  # 使用method='min'处理并列排名

    df['ts_rank_high'] = df.groupby('symbol')['high'].transform(
        lambda x: x.rolling(window=ts_rank_window, min_periods=w).apply(ts_rank, raw=True)
    )

    df['ts_rank_volume'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=ts_rank_window, min_periods=w).apply(ts_rank, raw=True)
    )

    # 计算滚动相关系数
    def rolling_corr(group):
        rh = group['ts_rank_high']
        rv = group['ts_rank_volume']
        # 在计算corr之前，先处理可能导致std=0的情况，例如窗口内所有值都相同
        # pandas的corr函数在std=0时会返回NaN，这里我们保留NaN，后续再处理
        # 同时处理输入中的inf和nan，corr函数会自动忽略nan，但inf可能导致问题
        # 确保输入到corr的是有限数值
        combined = pd.DataFrame({'rh': rh, 'rv': rv}).replace([np.inf, -np.inf], np.nan)
        # 如果窗口内的有效数据少于2个，corr会返回NaN，这是合理的
        # 如果窗口内的有效数据std为0，corr会返回NaN，这也是合理的
        corr_result = combined['rh'].rolling(window=corr_window, min_periods=w).corr(combined['rv'])
        # 将NaN填充为0，并将结果限制在[-1, 1]
        return corr_result.fillna(0).clip(-1, 1)

    df['corr'] = df.groupby('symbol').apply(rolling_corr).reset_index(level=0, drop=True)

    # 计算滚动最大值
    df['max_corr'] = df.groupby('symbol')['corr'].transform(
        lambda x: x.rolling(window=max_window, min_periods=w).max()
    )

    # 计算最终因子值并处理无效值
    df['factor'] = -df['max_corr']
    # 最终因子值中的inf和nan保留，不进行填充
    # df.replace([np.inf, -np.inf], np.nan, inplace=True)
    # df.dropna(subset=['factor'], inplace=True) # 保留NaN，不在此处删除

    # 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    return result_df

