# Alpha299因子 - factor_222
# 原始因子编号: 222
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_222(data_df, w: int | None = 12, uni_col: str | None = None):
    """
    计算Alpha141因子

    参数:
        data_df: 输入数据DataFrame
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为12天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一基础数据列 (None，因为使用volume和close两个基础列)

    返回:
        包含 ['trade_date', 'time', 'symbol', 'factor'] 的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delay_window': 12,    # delay窗口
        'delta_window': 12     # delta窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delay_window = window_sizes['delay_window']
    delta_window = window_sizes['delta_window']
    
    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'volume', 'close']
    if not all(col in data_df.columns for col in required_cols):
        raise ValueError(f"缺失必要列: {required_cols}")

    df = data_df.copy()

    # 按symbol分组处理，计算delay(volume, delay_window)
    # 对volume进行保护，避免出现非正数
    df['delayed_volume'] = df.groupby('symbol')['volume'].shift(delay_window).apply(lambda x: x if x > 0 else 1e-8)

    # 计算delta(close, delta_window)
    df['delta_close'] = df.groupby('symbol')['close'].diff(delta_window)

    # 计算乘积
    df['factor'] = df['delayed_volume'] * df['delta_close']

    # 处理无穷大值和NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    # 乘积结果可能产生NaN，这里不填充，保留真实的缺失情况

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return output_df

