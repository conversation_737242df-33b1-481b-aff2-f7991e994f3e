# Alpha299因子 - factor_349
# 原始因子编号: 349
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_349(data_df, w: int | None = 5, uni_col: str | None = 'close'):
    """
    计算Alpha_183因子

    参数:
    - data_df: 输入数据DataFrame
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一数据列参数，默认为'close'

    返回:
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delta1_window': 16,    # 第一个delta的窗口
        'delta2_window': 5,     # 第二个delta的窗口
        'ts_zscore_window': 20, # ts_zscore的窗口
        'ts_max_window': 14     # ts_max的窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delta1_window = window_sizes['delta1_window']
    delta2_window = window_sizes['delta2_window']
    ts_zscore_window = window_sizes['ts_zscore_window']
    ts_max_window = window_sizes['ts_max_window']

    # 复制数据, 避免修改原始数据
    df = data_df.copy()

    # 转换日期格式以便于后续操作
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 按symbol分组进行计算
    grouped = df.groupby('symbol')

    # 步骤1: 计算收盘价的delta1_window期差值
    df['delta1'] = grouped[uni_col].transform(lambda x: x.diff(delta1_window))

    # 步骤2: 计算delta1的delta2_window期差值（二阶差分）
    df['delta2'] = grouped['delta1'].transform(lambda x: x.diff(delta2_window))

    # 步骤3: 对delta2进行ts_zscore_window期滚动Z-score标准化
    # 计算滚动均值和标准差
    df['roll_mean'] = grouped['delta2'].transform(lambda x: x.rolling(window=ts_zscore_window, min_periods=w).mean())
    df['roll_std'] = grouped['delta2'].transform(lambda x: x.rolling(window=ts_zscore_window, min_periods=w).std())

    # 计算Z-score
    # 避免除以0或接近0的情况
    df['zscore_delta2'] = (df['delta2'] - df['roll_mean']) / (df['roll_std'] + 1e-8)
    # 处理可能产生的inf和nan
    df['zscore_delta2'] = df['zscore_delta2'].replace([np.inf, -np.inf], np.nan)

    # 步骤4: 对成交额进行截面排名
    # 按日期分组进行排名
    df['rank_amount'] = df.groupby('trade_date')['amount'].rank()

    # 步骤5: 计算rank_amount在过去ts_max_window期的滚动最大值
    df['ts_max_rank'] = grouped['rank_amount'].transform(lambda x: x.rolling(window=ts_max_window, min_periods=w).max())

    # 步骤6: 计算zscore_delta2与ts_max_rank的和
    df['factor'] = df['zscore_delta2'] + df['ts_max_rank']

    # 步骤7: 将无穷值替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna(subset=['factor'])

    return result_df

