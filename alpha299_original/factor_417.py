# Alpha299因子 - factor_417
# 原始因子编号: 417
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_417(data_df, w: int | None = None, uni_col: str | None = None):
    """
    向量算术减法 (Vector Arithmetic Subtraction, SUB) 因子计算函数
    
    参数:
    data_df: 输入数据DataFrame
    w: 不适用于本因子，设为None
    uni_col: 不适用于本因子，设为None
    
    返回:
    包含因子值的DataFrame，列为['trade_date', 'time', 'symbol', 'factor']
    """
    # 复制输入数据，避免修改原始数据
    df = data_df.copy()
    
    # 检查必要的列是否存在
    required_columns = ['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"输入数据缺少必要列: {missing_columns}")
    
    # 根据因子思路，我们需要两个输入序列进行相减
    # 在这个示例中，我们使用high和low作为两个输入序列
    # 计算因子：高价减去低价
    # 向量减法本身不会产生inf或nan，除非输入本身就是inf或nan
    # 这里不进行额外的处理，保留原始的inf/nan情况
    df['factor'] = df['high'] - df['low']
    
    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d').dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S').dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 只保留需要的列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]
    
    return result_df

