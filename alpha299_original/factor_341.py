# Alpha299因子 - factor_341
# 原始因子编号: 341
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_341(data_df, w: int | None = 9, uni_col: str | None = None):
    """
    计算Alpha145因子

    参数:
    data_df: 输入的DataFrame
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 单一可替换的基础数据列，本因子使用多列，因此设为None

    返回:
    包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window_regbeta': 18,  # 回归贝塔系数窗口
        'window_max': 9,       # 滚动最大值窗口
        'window_zscore': 20    # 标准化窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window_regbeta = window_sizes['window_regbeta']
    window_max = window_sizes['window_max']
    window_zscore = window_sizes['window_zscore']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 转换日期格式为datetime以便于后续计算
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 1. 计算成交额对收盘价在过去window_regbeta个周期内的滚动回归贝塔系数
    def rolling_beta(x, y, window):
        # 初始化结果数组
        result = np.full(len(x), np.nan)

        # 对每个窗口计算回归系数
        for i in range(window - 1, len(x)):
            x_window = x[i-window+1:i+1]
            y_window = y[i-window+1:i+1]

            # 排除无效值
            mask = ~(np.isnan(x_window) | np.isnan(y_window))
            x_valid = x_window[mask]
            y_valid = y_window[mask]

            # 至少需要2个点进行回归
            if len(x_valid) >= 2:
                # 计算协方差和方差
                cov_xy = np.cov(x_valid, y_valid)[0, 1]
                var_x = np.var(x_valid)

                # 避免除以零
                result[i] = cov_xy / (var_x + 1e-8)

        return result

    # 按symbol分组计算回归贝塔系数
    beta_list = []
    for name, group in df.groupby('symbol'):
        x = group['close'].values
        y = group['amount'].values
        beta = rolling_beta(x, y, window_regbeta)
        beta_list.append(pd.Series(beta, index=group.index))

    df['ts_regbeta'] = pd.concat(beta_list)

    # 2. 计算ts_regbeta在过去window_max个周期内的滚动最大值
    df['ts_max'] = df.groupby('symbol')['ts_regbeta'].transform(
        lambda x: x.rolling(window=window_max, min_periods=w).max()
    )

    # 3. 对ts_max在过去window_zscore个周期内进行滚动标准化
    df['ts_zscore_max'] = df.groupby('symbol')['ts_max'].transform(
        lambda x: (x - x.rolling(window=window_zscore).mean()) / (x.rolling(window=window_zscore).std() + 1e-8)
    )
    # 替换可能产生的inf和nan
    df['ts_zscore_max'] = df['ts_zscore_max'].replace([np.inf, -np.inf], np.nan)


    # 4. 计算成交额的绝对值的自然对数
    # 确保amount大于0，避免log(0)或log(负数)
    df['log_amount'] = np.log(np.abs(df['amount']) + 1e-8)

    # 5. 对log_amount在过去window_zscore个周期内进行滚动标准化
    df['ts_zscore_log'] = df.groupby('symbol')['log_amount'].transform(
        lambda x: (x - x.rolling(window=window_zscore).mean()) / (x.rolling(window=window_zscore).std() + 1e-8)
    )
    # 替换可能产生的inf和nan
    df['ts_zscore_log'] = df['ts_zscore_log'].replace([np.inf, -np.inf], np.nan)


    # 6. 计算两个标准化值的和得到Alpha145
    df['factor'] = df['ts_zscore_max'] + df['ts_zscore_log']

    # 7. 将无穷大值替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果
    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

