# Alpha299因子 - factor_262
# 原始因子编号: 262
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_262(data_df, w: int | None = 10, uni_col: str | None = None):
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 10,       # TS_COV计算窗口
        'delta_window': 16      # DELTA计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    cov_window = window_sizes['cov_window']        # TS_COV计算窗口
    delta_window = window_sizes['delta_window']    # DELTA计算窗口

    df = data_df.copy()

    # 计算VWAP（成交量加权平均价）
    # 避免除以零
    df['vwap'] = df['amount'] / (df['volume'] + 1e-10)

    # 按symbol和时间排序确保时间序列正确性
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 步骤1: -VWAP
    df['neg_vwap'] = -df['vwap']

    # 步骤2: DELTA(AMOUNT, delta_window)
    df['delta_amount'] = df.groupby('symbol')['amount'].transform(lambda x: x - x.shift(delta_window))

    # 步骤3: -VWAP / DELTA(AMOUNT, delta_window)
    # 避免除以零
    df['part1'] = df['neg_vwap'] / (df['delta_amount'] + 1e-8)

    # 步骤4: GP_MAX(LOW, VWAP)
    df['gp_max'] = np.maximum(df['low'], df['vwap'])

    # 步骤5: TS_COV(cov_window, gp_max, volume)
    # 处理滚动协方差可能产生的NaN或Inf
    df['ts_cov'] = df.groupby('symbol').apply(
        lambda x: x['gp_max'].rolling(window=cov_window, min_periods=w).cov(x['volume'])
    ).reset_index(level=0, drop=True)
    df['ts_cov'] = df['ts_cov'].replace([np.inf, -np.inf], np.nan).fillna(0) # 将inf/nan替换为0

    # 步骤6: RANK(ts_cov)
    # rank可能产生NaN，需要处理，这里rank(0)会得到一个值，但如果输入全是0，rank会是NaN
    df['rank'] = df.groupby(['trade_date', 'time'])['ts_cov'].transform(lambda x: x.rank(method='average', na_option='keep'))
    df['rank'] = df['rank'].fillna(0) # 将rank产生的NaN填充为0

    # 步骤7: part1 / rank
    # 避免除以零
    df['factor'] = df['part1'] / (df['rank'] + 1e-8)

    # 步骤8: 替换无穷大为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 处理时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留所需列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

