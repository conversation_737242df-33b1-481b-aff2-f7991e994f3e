# Alpha299因子 - factor_413
# 原始因子编号: 413
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_413(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算快速随机指标K值 (Fast Stochastic %K, FastK)

    参数:
    data_df: 包含OHLC数据的DataFrame
    w: 计算FastK值的时间窗口参数，默认为5。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    uni_col: 这个因子不依赖于单一列，所以设为None

    返回:
    包含因子值的DataFrame，列为['trade_date', 'time', 'symbol', 'factor']
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'fastk_window': 5
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    fastk_window = window_sizes['fastk_window']

    # 复制数据以避免修改原始数据
    df = data_df.copy()

    # 确保数据按照symbol和时间排序
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values(['symbol', 'time'])

    # 对每个symbol分组计算FastK
    result_list = []
    for symbol, group in df.groupby('symbol'):
        # 计算过去fastk_window个周期内的最高价和最低价
        group['highest_high'] = group['high'].rolling(window=fastk_window, min_periods=w).max()
        group['lowest_low'] = group['low'].rolling(window=fastk_window, min_periods=w).min()

        # 计算价格区间差
        group['price_range'] = group['highest_high'] - group['lowest_low']

        # 计算FastK值
        # 当价格区间差为0时，FastK设为0，避免除以零错误
        # 添加1e-8到分母以避免除以零，同时处理可能出现的inf或nan结果
        group['factor'] = (group['close'] - group['lowest_low']) / (group['price_range'] + 1e-8) * 100

        # 处理可能出现的inf或nan结果，将其替换为0
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan).fillna(0)

        result_list.append(group[['trade_date', 'time', 'symbol', 'factor']])

    # 合并所有结果
    result_df = pd.concat(result_list)

    # 去除缺失值
    result_df = result_df.dropna()

    # 将日期和时间格式转换为字符串
    result_df['trade_date'] = pd.to_datetime(result_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = pd.to_datetime(result_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df[['trade_date', 'time', 'symbol', 'factor']]

