# Alpha299因子 - factor_240
# 原始因子编号: 240
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_240(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha_169因子
    参数:
        data_df: 输入数据DataFrame
        w: 可调参数（无实际使用，因子参数固定）
        uni_col: 单列参数（无实际使用，因子使用多列）
    返回:
        包含因子值的DataFrame
    """
    # 检查必要列
    required_cols = ['symbol', 'trade_date', 'time', 'high', 'volume', 'close']
    missing_cols = [col for col in required_cols if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    df = data_df.copy()

    # 按symbol和时间排序
    df.sort_values(['symbol', 'time'], inplace=True)

    # 计算vwap
    df['cum_price_vol'] = df.groupby('symbol').apply(
        lambda x: (x['close'] * x['volume']).cumsum()
    ).reset_index(level=0, drop=True)
    df['cum_vol'] = df.groupby('symbol')['volume'].cumsum()
    # 保护除以0的情况
    df['vwap'] = df['cum_price_vol'] / (df['cum_vol'] + 1e-8)

    # 计算sigmoid(vwap)
    # np.exp(-x)对于很大的x可能导致下溢，但sigmoid本身定义域是实数，这里无需特殊处理
    df['sigmoid_vwap'] = 1 / (1 + np.exp(-df['vwap']))

    # 计算volume的2期差值（delta(volume, 2)）
    df['volume_delta'] = df.groupby('symbol')['volume'].transform(
        lambda x: x - x.shift(2)
    )

    # 取sigmoid_vwap和volume_delta的较大值
    df['gp_max'] = df[['sigmoid_vwap', 'volume_delta']].max(axis=1)

    # 计算high的5期百分比变化率（ts_pctchg(high, 5)）
    df['high_prev5'] = df.groupby('symbol')['high'].transform(
        lambda x: x.shift(5)
    )
    # 保护除以0的情况
    df['high_pctchg'] = (df['high'] - df['high_prev5']) / (df['high_prev5'] + 1e-8)

    # 取gp_max和high_pctchg的较小值作为因子值
    df['factor'] = df[['gp_max', 'high_pctchg']].min(axis=1)

    # 处理无穷值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并去重
    # 保留原始的dropna行为
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

