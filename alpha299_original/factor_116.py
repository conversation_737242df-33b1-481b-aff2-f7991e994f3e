# Alpha299因子 - factor_116
# 原始因子编号: 116
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_116(data_df, w: int | None = 20, uni_col: str | None = 'close'):
    """
    计算Alpha 160因子（条件波动率EMA因子）

    参数:
        data_df (pd.DataFrame): 输入数据，必须包含['symbol', 'trade_date', 'time', 'close']列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 基础数据列，默认为'close'

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']列的因子结果
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'std_window': 20,   # 滚动标准差窗口
        'ema_span': 39      # EMA span (2*w - 1)
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    std_window = window_sizes['std_window']
    ema_span = window_sizes['ema_span']

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', uni_col]
    if not all(col in data_df.columns for col in required_cols):
        missing_cols = [col for col in required_cols if col not in data_df.columns]
        raise ValueError(f"数据中缺少必要列: {missing_cols}")

    df = data_df.copy()

    # 按symbol分组并按时间排序
    df = df.sort_values(['symbol', 'time'])

    # 计算滚动标准差（使用样本标准差 ddof=1）
    # 考虑到滚动标准差可能因为窗口内常数而为0，或者包含NaN/Inf，这里使用transform后直接处理NaN/Inf
    df['std'] = df.groupby('symbol')[uni_col].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).std(ddof=1)
    )
    df['std'] = df['std'].replace([np.inf, -np.inf], np.nan)


    # 计算前一天的收盘价
    df['prev_close'] = df.groupby('symbol')[uni_col].shift(1)

    # 定义条件波动率（Iverson bracket）
    # np.where会自动处理NaN，但为了稳妥，确保输入到np.where的值没有inf
    df['cond_std'] = np.where(df[uni_col].replace([np.inf, -np.inf], np.nan) <= df['prev_close'].replace([np.inf, -np.inf], np.nan), df['std'], 0)

    # 计算EMA
    # EMA计算过程中也可能产生NaN，但不会产生Inf，这里直接使用ewm
    df['ema'] = df.groupby('symbol')['cond_std'].transform(
        lambda x: x.ewm(span=ema_span, adjust=False).mean()
    )

    # 处理EMA结果中的无效值
    df['ema'] = df['ema'].replace([np.inf, -np.inf], np.nan)

    # 过滤掉原始close为NaN的行
    df = df.dropna(subset=[uni_col])

    # 调整日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出结果
    output_df = df[['trade_date', 'time', 'symbol', 'ema']].rename(columns={'ema': 'factor'})
    output_df = output_df.dropna(subset=['factor'])

    return output_df

