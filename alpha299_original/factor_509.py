# Alpha299因子 - factor_509
# 原始因子编号: 509
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_509(data_df, w: int | None = None, uni_col: str | None = 'close'):
    """
    计算正量指标 (Positive Volume Index, PVI)

    参数:
    data_df: 输入数据DataFrame
    w: 窗口期参数，对于PVI不适用，设为None
    uni_col: 用于计算的价格列，默认为'close'

    返回:
    包含因子值的DataFrame
    """
    # 复制数据以避免修改原始数据
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组并按时间排序
    df = df.sort_values(['symbol', 'time'])

    # 初始化PVI列，初始值为100
    df['pvi_initial'] = 100

    # 按symbol分组计算PVI
    def compute_pvi(group):
        # 计算价格和成交量的变化
        price = group[uni_col]
        volume = group['volume']

        # 计算价格变化率
        # 避免除以零的情况，对价格进行微小偏移
        price_change_ratio = price.pct_change()

        # 计算成交量变化标志（成交量增加为True，否则为False）
        # 对成交量进行微小偏移，避免与0精确比较
        volume_increased = (volume + 1e-8) > (volume.shift(1) + 1e-8)

        # 初始化PVI序列
        pvi = np.zeros(len(group))
        pvi[0] = 100  # 初始值设为100

        # 迭代计算PVI
        for i in range(1, len(group)):
            # 检查price_change_ratio是否为nan或inf，如果是则跳过计算
            if pd.isna(price_change_ratio.iloc[i]) or np.isinf(price_change_ratio.iloc[i]):
                 pvi[i] = pvi[i-1]
                 continue

            if volume_increased.iloc[i]:
                # 成交量增加时，根据价格变化调整PVI
                pvi[i] = pvi[i-1] * (1 + price_change_ratio.iloc[i])
            else:
                # 成交量不增加时，PVI保持不变
                pvi[i] = pvi[i-1]

        return pd.Series(pvi, index=group.index)

    # 应用PVI计算函数
    df['factor'] = df.groupby('symbol').apply(compute_pvi).reset_index(level=0, drop=True)

    # 恢复日期和时间格式
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择所需列并返回
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

