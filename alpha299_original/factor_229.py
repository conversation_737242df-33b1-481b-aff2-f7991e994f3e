# Alpha299因子 - factor_229
# 原始因子编号: 229
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_229(
    data_df,
    w: int | None = 12,
    uni_col: str | None = None
):
    """
    计算Alpha151因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含以下列：
            - high: 最高价
            - open: 开盘价
            - volume: 成交量
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为12天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 不适用（因子涉及多列计算）
    """
    # 定义所有窗口的基准值
    window_configs = {
        'pctchg_window': 14,  # ts_pctchg窗口
        'delta_window': 12,   # delta窗口
        'corr_window': 12     # ts_corr窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    n1 = window_sizes['pctchg_window']  # ts_pctchg窗口
    n2 = window_sizes['delta_window']   # delta窗口
    n3 = window_sizes['corr_window']    # ts_corr窗口
    
    # 检查必要列是否存在
    required_cols = ['high', 'open', 'volume']
    if not all(col in data_df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in data_df.columns]
        raise ValueError(f"数据中缺少必要列：{', '.join(missing)}")

    # 步骤1: arctan(high)
    # high通常大于0，但为保险起见，增加一个微小量
    data_df['X1'] = np.arctan(data_df['high'] + 1e-8)

    # 步骤2: ts_pctchg(open, n1)
    data_df['T1'] = data_df.groupby('symbol')['open'].transform(
        lambda x: x.pct_change(n1)
    )

    # 步骤3: delta(volume, n2)
    data_df['T2'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: x.diff(n2)
    )

    # 步骤4: gp_max(T1, T2)
    data_df['X2'] = data_df[['T1', 'T2']].max(axis=1)

    # 步骤5: ts_corr(n3, X1, X2)
    def rolling_corr(group):
        # 在计算相关性之前，先处理可能导致std=0或inf/nan的情况
        # 这里选择在计算corr后将nan/inf填充为0，因为corr为nan/inf通常意味着其中一个序列是常数或包含大量异常值
        corr_result = group['X1'].rolling(n3, min_periods=w).corr(group['X2'])
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    data_df['factor'] = data_df.groupby('symbol').apply(rolling_corr).droplevel('symbol')

    # 步骤6: 无穷大值处理 (已在步骤5的rolling_corr函数中处理)
    # data_df['factor'] = data_df['factor'].replace([np.inf, -np.inf], np.nan) # 移除此行，因为已在rolling_corr中处理

    # 格式标准化处理
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 构建输出DataFrame
    # 保留原始的dropna行为，因为因子计算过程中产生的NaN是真实的缺失值
    output_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

