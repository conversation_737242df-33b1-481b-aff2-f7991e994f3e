# Alpha299因子 - factor_360
# 原始因子编号: 360
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_360(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha1因子

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 不适用于此因子，设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'std_window': 10,         # 滚动标准差窗口
        'max_window_1': 5,        # 第一个滚动最大值窗口
        'mean_window': 12,        # 滚动均值窗口
        'cov_window': 8,          # 滚动协方差窗口
        'max_window_2': 11        # 第二个滚动最大值窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    std_window = window_sizes['std_window']
    max_window_1 = window_sizes['max_window_1']
    mean_window = window_sizes['mean_window']
    cov_window = window_sizes['cov_window']
    max_window_2 = window_sizes['max_window_2']

    # 数据预处理
    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 检查必要的列是否存在
    required_columns = ['volume', 'amount']
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        raise ValueError(f"缺少必要的列: {missing_columns}")

    # 计算vwap (如果不存在)
    if 'vwap' not in df.columns:
        # vwap = 成交额 / 成交量
        df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
        # 处理可能出现的无穷大值
        df['vwap'] = df['vwap'].replace([np.inf, -np.inf], np.nan)

    # 第1步: 计算成交量与成交量加权平均价的乘积
    df['X1'] = df['volume'] * df['vwap']

    # 第2步: 计算X1在过去10个周期内的滚动标准差
    df['X2'] = df.groupby('symbol')['X1'].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).std()
    )

    # 第3步: 计算X2在过去5个周期内的滚动最大值
    df['X3'] = df.groupby('symbol')['X2'].transform(
        lambda x: x.rolling(window=max_window_1, min_periods=w).max()
    )

    # 第4步: 计算成交额在过去12个周期内的滚动均值
    df['X4'] = df.groupby('symbol')['amount'].transform(
        lambda x: x.rolling(window=mean_window, min_periods=w).mean()
    )

    # 第5步: 计算X3和X4在过去8个周期内的滚动协方差
    # 使用手动方法实现滚动协方差
    result_x5 = []

    # 按symbol分组处理
    for symbol, group in df.groupby('symbol'):
        # 获取X3和X4列数据
        x3 = group['X3'].values
        x4 = group['X4'].values
        x5 = np.full(len(group), np.nan)

        # 计算滚动协方差
        for i in range(len(group)):
            if i < cov_window - 1:
                # 不足窗口期时，使用所有可用数据
                window_x3 = x3[:i+1]
                window_x4 = x4[:i+1]
            else:
                # 足够窗口期时，使用完整窗口
                window_x3 = x3[i-cov_window+1:i+1]
                window_x4 = x4[i-cov_window+1:i+1]

            # 移除NaN值
            valid_indices = ~(np.isnan(window_x3) | np.isnan(window_x4))
            valid_x3 = window_x3[valid_indices]
            valid_x4 = window_x4[valid_indices]

            # 至少需要2个有效数据点才能计算协方差
            if len(valid_x3) >= 2:
                # 计算协方差
                x5[i] = np.cov(valid_x3, valid_x4)[0, 1]
            elif len(valid_x3) == 1:
                 # 只有一个有效数据点时，协方差为0
                 x5[i] = 0
            else:
                 # 没有有效数据点，协方差为NaN
                 x5[i] = np.nan

        # 将结果添加到列表中
        group_result = group.copy()
        group_result['X5'] = x5
        result_x5.append(group_result)

    # 合并结果
    df = pd.concat(result_x5)

    # 第6步: 计算X5在过去11个周期内的滚动最大值得到Alpha1
    df['factor'] = df.groupby('symbol')['X5'].transform(
        lambda x: x.rolling(window=max_window_2, min_periods=w).max()
    )

    # 第7步: 将结果中的无穷大值替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 提取所需列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

