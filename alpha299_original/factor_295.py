# Alpha299因子 - factor_295
# 原始因子编号: 295
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_295(data_df, w: int | None = 2, uni_col: str | None = None):
    """
    计算Alpha52因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）

    返回:
        pd.DataFrame: 包含因子值的DataFrame，列包括 ['trade_date', 'time', 'symbol', 'factor']
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 2,      # open_price的delta周期
        'max_window': 6,        # amount的滚动最大值窗口
        'corr_window': 13       # vwap和T2的滚动相关系数窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delta_period = window_sizes['delta_window']    # open_price的delta周期
    max_window = window_sizes['max_window']        # amount的滚动最大值窗口
    corr_window = window_sizes['corr_window']      # vwap和T2的滚动相关系数窗口

    df = data_df.copy()

    # 计算vwap（成交量加权平均价）
    # 避免除以0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 按symbol分组，并按时间排序
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values(['symbol', 'time'])

    # 步骤1: 计算amount的max_window周期滚动最大值 (min_periods=w)
    df['T1'] = df.groupby('symbol')['amount'].transform(
        lambda x: x.rolling(window=max_window, min_periods=w).max()
    )

    # 步骤2: 计算T1的绝对值平方根
    # np.sqrt已经处理了负数，但为了鲁棒性，确保输入非负
    df['T2'] = np.sqrt(np.abs(df['T1']))

    # 步骤3: 计算vwap和T2的corr_window周期滚动相关系数 (min_periods=w)
    def rolling_corr_safe(group):
        # 对每个symbol组计算相关系数
        # 使用try-except块捕获可能的异常，例如常数序列导致std=0
        try:
            corr_result = group['vwap'].rolling(window=corr_window, min_periods=w).corr(group['T2'])
            # 填充NaN和inf为0，因为相关系数在常数序列或包含inf/nan时无意义，0是合理的默认值
            corr_result = corr_result.replace([np.inf, -np.inf], np.nan).fillna(0)
            return corr_result
        except Exception:
            # 如果发生其他异常，返回全NaN序列，后续会dropna处理
            return pd.Series(np.nan, index=group.index)

    df['X1'] = df.groupby('symbol').apply(rolling_corr_safe).reset_index(level=0, drop=True)

    # 步骤4: 计算open_price的delta_period周期delta
    df['X2'] = df.groupby('symbol')['open'].transform(
        lambda x: x - x.shift(delta_period)
    )

    # 步骤5: 逐元素取较小值
    df['factor'] = df[['X1', 'X2']].min(axis=1)

    # 处理无穷大值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去除无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

