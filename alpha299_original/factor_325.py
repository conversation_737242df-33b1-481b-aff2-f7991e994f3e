# Alpha299因子 - factor_325
# 原始因子编号: 325
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_325(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha106因子

    公式：ts_corr(15, delta(amount, 10), add(ts_corr(6, volume, low), ts_zscore(close, 20)))

    参数:
        data_df: 输入数据
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为15天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col: 单一列参数，本因子不使用
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 15,                # 主要相关系数窗口
        'delta_window': 10,               # delta差值窗口
        'volume_low_corr_window': 6,      # volume和low相关系数窗口
        'zscore_window': 20               # 标准化窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']
    delta_window = window_sizes['delta_window']
    volume_low_corr_window = window_sizes['volume_low_corr_window']
    zscore_window = window_sizes['zscore_window']

    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组进行计算
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 按时间排序
        group = group.sort_values('time')

        # 1. 计算成交额在过去delta_window个周期内的差值：delta(amount, delta_window)
        group['delta_amount'] = group['amount'] - group['amount'].shift(delta_window)

        # 2. 计算成交量和最低价在过去volume_low_corr_window个周期内的滚动相关系数：ts_corr(volume_low_corr_window, volume, low)
        # 对可能导致std=0的情况进行处理，或者对结果中的NaN/inf进行处理
        group['volume_low_corr'] = group['volume'].rolling(window=volume_low_corr_window, min_periods=w).corr(group['low'])
        group['volume_low_corr'] = group['volume_low_corr'].fillna(0).replace([np.inf, -np.inf], 0)

        # 3. 对收盘价进行过去zscore_window个周期的滚动Z-score标准化：ts_zscore(close, zscore_window)
        close_mean = group['close'].rolling(window=zscore_window, min_periods=w).mean()
        close_std = group['close'].rolling(window=zscore_window, min_periods=w).std()
        # 避免除以0的情况
        group['close_zscore'] = (group['close'] - close_mean) / (close_std + 1e-8)
        # 对结果中的NaN/inf进行处理
        group['close_zscore'] = group['close_zscore'].fillna(0).replace([np.inf, -np.inf], 0)

        # 4. 计算volume_low_corr与close_zscore的和：add(ts_corr(volume_low_corr_window, volume, low), ts_zscore(close, zscore_window))
        group['combined'] = group['volume_low_corr'] + group['close_zscore']

        # 5. 计算delta_amount和combined在过去corr_window个周期内的滚动相关系数
        # 对可能导致std=0的情况进行处理，或者对结果中的NaN/inf进行处理
        group['factor'] = group['delta_amount'].rolling(window=corr_window, min_periods=w).corr(group['combined'])
        group['factor'] = group['factor'].fillna(0).replace([np.inf, -np.inf], 0)

        # 选择需要的列
        result_df = group[['trade_date', 'time', 'symbol', 'factor']]
        result_dfs.append(result_df)

    # 合并所有结果
    if result_dfs:
        result = pd.concat(result_dfs)
    else:
        result = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    # 恢复日期格式为字符串
    result['trade_date'] = result['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result['time'] = result['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果，去除缺失值
    return result.dropna(subset=['factor'])

