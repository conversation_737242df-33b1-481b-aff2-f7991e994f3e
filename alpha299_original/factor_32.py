# Alpha299因子 - factor_32
# 原始因子编号: 32
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_32(data_df, w: int | None = 6, uni_col: str | None = None):
    # 窗口配置
    window_configs = {
        'main_window': 12.0,        # w，主要计算窗口
        'correlation_window': 6.0   # w//2 = 12//2 = 6，相关系数窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    main_w = window_sizes['main_window']
    correlation_window = window_sizes['correlation_window']
    
    # 检查必要列是否存在
    required_columns = ['low', 'high', 'close', 'volume', 'symbol', 'time', 'trade_date']
    missing_cols = [col for col in required_columns if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    # 确保数据按symbol和time排序
    data_df = data_df.sort_values(by=['symbol', 'time']).copy()

    # 计算ts_min(LOW, main_w)（使用实际窗口大小）
    data_df['low_min_12'] = data_df.groupby('symbol')['low'].transform(
        lambda x: x.rolling(window=main_w, min_periods=w).min()
    )

    # 计算ts_max(HIGH, main_w)（使用实际窗口大小）
    data_df['high_max_12'] = data_df.groupby('symbol')['high'].transform(
        lambda x: x.rolling(window=main_w, min_periods=w).max()
    )

    # 计算temp1 = (close - low_min_12) / (high_max_12 - low_min_12)
    denominator = data_df['high_max_12'] - data_df['low_min_12']
    # 避免除零错误和inf
    data_df['temp1'] = (data_df['close'] - data_df['low_min_12']) / (denominator + 1e-8)
    data_df['temp1'] = data_df['temp1'].replace([np.inf, -np.inf], np.nan)


    # 使用滚动窗口计算排名百分位
    def rolling_rank_pct(x, window_size):
        # 确保输入到rank的数据没有inf或nan，虽然rank函数通常会处理，但提前处理更稳健
        x_cleaned = x.replace([np.inf, -np.inf], np.nan)
        return x_cleaned.rolling(window=window_size, min_periods=window_size).apply(
            lambda window: pd.Series(window).rank(pct=True, na_option='keep').iloc[-1]
        )

    # 对temp1进行滚动窗口排名（百分位）（使用实际窗口大小）
    data_df['rank_temp1'] = data_df.groupby('symbol')['temp1'].transform(
        lambda x: rolling_rank_pct(x, main_w)
    )

    # 对volume进行滚动窗口排名（百分位）（使用实际窗口大小）
    # volume本身通常大于0，但为了通用性，也进行inf/nan处理
    data_df['rank_volume'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: rolling_rank_pct(x, main_w)
    )

    # 计算相关系数（使用实际窗口大小）
    def calculate_corr(group):
        # 确保输入到corr的数据没有inf或nan
        rank_temp1_cleaned = group['rank_temp1'].replace([np.inf, -np.inf], np.nan)
        rank_volume_cleaned = group['rank_volume'].replace([np.inf, -np.inf], np.nan)

        # 计算滚动相关系数
        corr_values = rank_temp1_cleaned.rolling(window=correlation_window, min_periods=w).corr(rank_volume_cleaned)

        # 将计算出的相关系数中的nan或inf替换为0
        corr_values = corr_values.replace([np.inf, -np.inf], np.nan).fillna(0)
        return corr_values

    data_df['corr'] = data_df.groupby('symbol').apply(calculate_corr).reset_index(level=0, drop=True)

    # 对相关系数进行滚动窗口排名（百分位）（使用实际窗口大小）
    # corr值在-1到1之间，通常不会有inf，但可能因为输入nan导致输出nan，rolling_rank_pct会处理nan
    data_df['rank_corr'] = data_df.groupby('symbol')['corr'].transform(
        lambda x: rolling_rank_pct(x, correlation_window)
    )

    # 最终因子值为 -rank_corr
    data_df['factor'] = -data_df['rank_corr']

    # 恢复日期和时间格式
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并dropna
    output_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

