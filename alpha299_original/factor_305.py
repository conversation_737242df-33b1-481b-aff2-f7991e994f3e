# Alpha299因子 - factor_305
# 原始因子编号: 305
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_305(data_df, w: int | None = 11, uni_col: str | None = None):
    """
    计算Alpha69因子。

    参数:
        data_df (pd.DataFrame): 输入数据，包含必需的列。
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为11天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础数据列（此处不适用，设为None）。

    返回:
        pd.DataFrame: 包含因子值的DataFrame。
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 11,      # ts_corr窗口
        'mean_window': 15,      # ts_mean窗口
        'zscore_window': 20     # ts_zscore窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    n1 = window_sizes['corr_window']    # ts_corr窗口
    n2 = window_sizes['mean_window']    # ts_mean窗口
    n3 = window_sizes['zscore_window']  # ts_zscore窗口

    df = data_df.copy()

    # 计算vwap（假设数据中没有vwap列）
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 计算ts_corr(n1, vwap, volume)
    def _rolling_corr(group):
        # 确保输入到corr的数据没有inf或nan
        temp_group = group[['vwap', 'volume']].replace([np.inf, -np.inf], np.nan).dropna()
        corr_result = temp_group['vwap'].rolling(window=n1, min_periods=w).corr(temp_group['volume'])
        # 填充由于常数或inf/nan导致的nan/inf为0
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    # Apply rolling_corr and align index
    ts_corr_result = df.groupby('symbol').apply(_rolling_corr)
    # Need to reindex the result to match the original dataframe's index structure
    df['ts_corr'] = ts_corr_result.reset_index(level=0, drop=True)

    # 计算ts_mean(n2, ts_corr)
    df['X1'] = df.groupby('symbol')['ts_corr'].transform(lambda x: x.rolling(window=n2, min_periods=w).mean())

    # 计算X2_raw = open - high
    df['X2_raw'] = df['open'] - df['high']

    # 计算ts_zscore(n3, X2_raw)
    def _rolling_zscore(group):
        # 确保输入到zscore的数据没有inf或nan
        temp_group = group.replace([np.inf, -np.inf], np.nan)
        rolling_mean = temp_group.rolling(window=n3, min_periods=w).mean()
        rolling_std = temp_group.rolling(window=n3, min_periods=w).std()
        # 避免除以0
        zscore_result = (temp_group - rolling_mean) / (rolling_std + 1e-8)
        # 填充由于常数或inf/nan导致的nan/inf为0
        return zscore_result.fillna(0).replace([np.inf, -np.inf], 0)

    # Apply rolling_zscore and align index
    x2_zscored_result = df.groupby('symbol')['X2_raw'].apply(_rolling_zscore)
    # Need to reindex the result to match the original dataframe's index structure
    df['X2_zscored'] = x2_zscored_result.reset_index(level=0, drop=True)

    # 计算Alpha69 = X1 - X2_zscored
    df['factor'] = df['X1'] - df['X2_zscored']

    # 处理无穷大值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return df[['trade_date', 'time', 'symbol', 'factor']].dropna()

