# Alpha299因子 - factor_197
# 原始因子编号: 197
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_197(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha103因子
    """
    # 窗口配置
    window_configs = {
        'n1': 16.0,   # w，ts_corr窗口
        'n2': 10.0,   # int(10*w/16) = int(10*16/16) = 10，ts_std窗口
        'n3': 18.0    # int(18*w/16) = int(18*16/16) = 18，ts_cov窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'volume', 'amount', 'low']
    for col in required_cols:
        if col not in data_df.columns:
            raise KeyError(f"数据中缺少必要列: {col}")

    df = data_df.copy()

    # 计算vwap（因为输入数据中没有vwap列）
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
    # df['vwap'] = df['vwap'].fillna(0)  # 避免除零错误，保留真实缺值

    # 计算X1: ts_corr(n1, vwap, volume)（使用实际窗口大小）
    def calc_corr(g):
        # 确保输入corr的数据没有inf或nan，并对结果的inf或nan填0
        g_cleaned = g[['vwap', 'volume']].replace([np.inf, -np.inf], np.nan).dropna()
        corr_result = g_cleaned['vwap'].rolling(n1, min_periods=w).corr(g_cleaned['volume'])
        return corr_result.replace([np.inf, -np.inf], 0).fillna(0) # 对corr结果的inf/nan填0
    
    # 使用apply并处理返回的Series，确保索引对齐
    corr_result = df.groupby('symbol').apply(lambda g: calc_corr(g.drop(columns=['symbol'])))

    
    # 确保结果能正确展开为Series
    if isinstance(corr_result, pd.DataFrame):
        # 如果结果是DataFrame，需要将其展开为Series
        df['X1'] = corr_result.stack().reset_index(level=[0,1], drop=True)
    else:
        # 如果结果是Series，直接使用
        df['X1'] = corr_result.reset_index(level=0, drop=True)

    # 计算T1: gp_min(vwap, amount)
    df['T1'] = df[['vwap', 'amount']].min(axis=1)

    # 计算T2: ts_std(low, n2)（使用实际窗口大小）
    df['T2'] = df.groupby('symbol')['low'].transform(lambda x: x.rolling(n2, min_periods=w).std())

    # 计算X2: ts_cov(n3, T1, T2)（使用实际窗口大小）
    def calc_cov(g):
        # 确保输入cov的数据没有inf或nan，并对结果的inf或nan填0
        g_cleaned = g[['T1', 'T2']].replace([np.inf, -np.inf], np.nan).dropna()
        cov_result = g_cleaned['T1'].rolling(n3, min_periods=w).cov(g_cleaned['T2'])
        return cov_result.replace([np.inf, -np.inf], 0).fillna(0) # 对cov结果的inf/nan填0
    
    # 使用apply并处理返回的Series，确保索引对齐
    cov_result = df.groupby('symbol').apply(lambda g: calc_cov(g.drop(columns=['symbol'])))
    
    # 确保结果能正确展开为Series
    if isinstance(cov_result, pd.DataFrame):
        # 如果结果是DataFrame，需要将其展开为Series
        df['X2'] = cov_result.stack().reset_index(level=[0,1], drop=True)
    else:
        # 如果结果是Series，直接使用
        df['X2'] = cov_result.reset_index(level=0, drop=True)

    # 计算Alpha103: gp_min(X1, X2)
    df['factor'] = df[['X1', 'X2']].min(axis=1)

    # 处理无穷大值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 选择输出列
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    # Check if we have any results before processing
    if not output_df.empty:
        # 处理日期和时间格式
        output_df['trade_date'] = pd.to_datetime(output_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
        output_df['time'] = pd.to_datetime(output_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return output_df

