# Alpha299因子 - factor_493
# 原始因子编号: 493
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_493(data_df, w: int | None = 30, uni_col: str | None = 'close'):
    """
    计算周期内最小值索引 (MININDEX) 因子
    
    参数:
    data_df: DataFrame, 输入数据
    w: int, 周期长度，默认为30。该参数将作为最小窗口，所有窗口将根据三段式混合模型动态调整。
    uni_col: str, 用于计算的数据列，默认为'close'
    
    返回:
    DataFrame, 包含['trade_date', 'time', 'symbol', 'factor']列的结果
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}
    # 定义所有窗口的基准值
    window_configs = {
        'minindex_window': 30
    }
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    minindex_window = window_sizes['minindex_window']

    df = data_df.copy()
    
    # 确保日期和时间列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')
    
    # 按symbol分组并排序
    df = df.sort_values(['symbol', 'time'])
    
    # 创建一个空的结果列
    df['factor'] = np.nan
    
    # 按symbol分组计算MININDEX
    for symbol, group in df.groupby('symbol'):
        # 获取该symbol的uni_col列数据
        series = group[uni_col].reset_index(drop=True)
        
        # 创建一个与series长度相同的索引数组
        result = np.full(len(series), np.nan)
        
        # 从第minindex_window个点开始计算(考虑0索引，实际是minindex_window-1)
        # 确保窗口长度大于0
        if minindex_window > 0:
            for i in range(minindex_window-1, len(series)):
                # 获取窗口数据
                window = series.iloc[i-minindex_window+1:i+1]
                
                # 过滤掉窗口中的NaN值，如果窗口全为NaN，则跳过
                valid_window = window.dropna()
                
                if not valid_window.empty:
                    # 找出窗口内最小值的索引
                    # 如果有多个相同的最小值，取最后一个（即索引最大者）
                    min_value = valid_window.min()
                    
                    # 反向查找最小值位置，确保取最后一个
                    # 使用.values.tolist()避免潜在的pandas索引问题
                    window_values = window.values.tolist()
                    
                    try:
                        # 查找最小值在原始窗口中的位置
                        # 从后往前找，确保找到最后一个
                        min_pos_in_window = len(window_values) - 1 - window_values[::-1].index(min_value)
                        
                        # 计算原始索引
                        original_idx = i - minindex_window + 1 + min_pos_in_window
                        result[i] = original_idx
                    except ValueError:
                        # 如果min_value不在window_values中（理论上不应该发生，但作为保护）
                        result[i] = np.nan
                else:
                    # 窗口全为NaN，无法计算最小值索引
                    result[i] = np.nan
        
        # 将结果放回原DataFrame
        group_index = group.index
        df.loc[group_index, 'factor'] = result
    
    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 只保留需要的列
    # 保留NaN值，不进行dropna
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]
    
    return result_df

