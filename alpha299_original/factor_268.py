# Alpha299因子 - factor_268
# 原始因子编号: 268
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_268(data_df, w: int | None = 12, uni_col: str | None = None):
    """
    计算Alpha216因子，基于每日成交金额和VWAP的12周期协方差。

    参数:
    data_df (pd.DataFrame): 输入数据，包含symbol, trade_date, amount, volume等列
    w (int | None): 协方差计算的窗口大小（单位：天），默认12
    uni_col (str | None): 本因子不依赖单一基础列，因此默认为None

    返回:
    pd.DataFrame: 包含trade_date, time, symbol, factor四列的因子结果
    """
    # 定义所有窗口的基准值
    window_configs = {
        'cov_window': 12        # 协方差计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    cov_window = window_sizes['cov_window']        # 协方差计算窗口

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'amount', 'volume']
    for col in required_cols:
        if col not in data_df.columns:
            raise ValueError(f"输入数据缺少必要列: {col}")

    # 1. 计算每日的AMOUNT和VOLUME
    daily_df = data_df.groupby(['symbol', 'trade_date']).agg(
        daily_amount=('amount', 'sum'),
        daily_volume=('volume', 'sum')
    ).reset_index()

    # 2. 计算每日VWAP
    # 避免除以零
    daily_df['vwap'] = daily_df['daily_amount'] / (daily_df['daily_volume'] + 1e-8)

    # 3. 按symbol和trade_date排序
    daily_df.sort_values(['symbol', 'trade_date'], inplace=True)

    # 4. 计算cov_window周期滚动协方差
    # 协方差计算可能产生NaN或Inf，这里不特殊处理，留待后续统一处理
    daily_df['factor'] = daily_df.groupby('symbol').apply(
        lambda x: x['daily_amount'].rolling(window=cov_window, min_periods=w).cov(x['vwap'])
    ).reset_index(level=0, drop=True)

    # 5. 处理无穷大值和NaN值
    # 将协方差计算结果中的Inf和NaN替换为0，因为协方差为0表示没有线性关系，这在数据不足或常数序列时是合理的处理
    daily_df['factor'] = daily_df['factor'].replace([float('inf'), -float('inf')], float('nan')).fillna(0)


    # 6. 获取每个交易日的最后一个时间点
    data_df['time'] = pd.to_datetime(data_df['time'])
    last_time_per_day = data_df.groupby(['symbol', 'trade_date'])['time'].max().reset_index()
    last_time_per_day.rename(columns={'time': 'last_time'}, inplace=True)

    # 7. 合并到结果中
    daily_df = daily_df.merge(last_time_per_day, on=['symbol', 'trade_date'])

    # 8. 转换日期和时间格式为字符串
    daily_df['trade_date'] = pd.to_datetime(daily_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    daily_df['last_time'] = daily_df['last_time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 9. 重命名列并保留必要列
    daily_df.rename(columns={'last_time': 'time'}, inplace=True)
    # 由于步骤5已经处理了NaN，这里不再需要dropna()
    result_df = daily_df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

