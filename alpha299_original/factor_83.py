# Alpha299因子 - factor_83
# 原始因子编号: 83
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_83(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha 104因子：高量相关性变化与收盘价波动率排名乘积因子
    参数:
        data_df (pd.DataFrame): 输入数据
        w (int): 核心可调窗口参数（默认5）
        uni_col (str): 单一基础列参数（默认None）
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    df = data_df.copy()

    # 确保数据按symbol和时间排序
    df = df.sort_values(['symbol', 'trade_date', 'time'])

    # 三段式混合模型窗口配置
    window_configs = {
        'base_window': 5,       # 基础窗口
        'vol_window': 20,       # 成交量窗口
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        base_window = window_sizes.loc[w, 'base_window']
        vol_window = window_sizes.loc[w, 'vol_window']
    else:
        base_window = window_configs['base_window']
        vol_window = window_configs['vol_window']

    # 计算High与Volume的滚动相关系数（窗口w）
    # 处理滚动相关系数可能出现的NaN或inf
    df['corr_hv'] = df.groupby('symbol').apply(
        lambda g: g['high'].rolling(base_window, min_periods=w).corr(g['volume']).replace([np.inf, -np.inf], np.nan).fillna(0)
    ).reset_index(level=0, drop=True)

    # 计算5期差分（Δ5 CorrHV）
    df['delta_corr_hv'] = df.groupby('symbol')['corr_hv'].diff(base_window)

    # 计算Close的20期滚动标准差
    # 处理滚动标准差可能出现的NaN或inf
    df['std_close'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(vol_window, min_periods=w).std().replace([np.inf, -np.inf], np.nan)
    )

    # 横截面百分比排名
    df['rank_std_close'] = df.groupby(['trade_date', 'time'])['std_close'].rank(pct=True)

    # 计算最终因子值
    df['factor'] = - (df['delta_corr_hv'] * df['rank_std_close'])

    # 处理无效值（±∞替换为NaN）
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

