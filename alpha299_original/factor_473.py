# Alpha299因子 - factor_473
# 原始因子编号: 473
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_473(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算蔡金累积/派发线 (<PERSON><PERSON><PERSON> Accumulation/Distribution Line, ADL)

    参数:
    data_df: DataFrame - 包含价格和成交量数据的DataFrame
    w: int | None - 该因子不需要窗口参数，设为None
    uni_col: str | None - 该因子不依赖单一数据列，设为None

    返回:
    DataFrame - 包含trade_date, time, symbol和factor(ADL)列的DataFrame
    """
    # 创建数据副本
    df = data_df.copy()

    # 确保数据类型正确
    for col in ['high', 'low', 'close', 'volume']:
        if col not in df.columns:
            raise ValueError(f"缺少必要的列: {col}")

    # 转换日期格式以便于分组
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 按symbol分组计算ADL
    result_dfs = []

    for symbol, group in df.groupby('symbol'):
        # 确保数据按时间排序
        group = group.sort_values('time')

        # 计算价格区间
        price_range = group['high'] - group['low']

        # 计算资金流量乘数 (Money Flow Multiplier, MFM)
        # MFM = ((Close - Low) - (High - Close)) / (High - Low)
        # 等价于 MFM = (2*Close - Low - High) / (High - Low)
        # 添加对price_range为0的保护
        mfm = ((2 * group['close'] - group['low'] - group['high']) / (price_range + 1e-8))

        # 计算资金流量 (Money Flow Volume, MFV)
        # 确保volume不是nan或inf
        volume_safe = group['volume'].replace([np.inf, -np.inf], np.nan).fillna(0)
        mfv = mfm * volume_safe

        # 计算累积/派发线 (Accumulation/Distribution Line, ADL)
        # cumsum会自动处理nan，但如果mfv中有inf，cumsum结果会是inf
        # 因此在cumsum前将inf替换为nan，cumsum后再将nan替换为0（或者保留nan）
        # 这里选择保留nan，因为ADL是累积值，中间的nan会影响后续计算
        adl = mfv.replace([np.inf, -np.inf], np.nan).cumsum()

        # 创建结果DataFrame
        result = pd.DataFrame({
            'trade_date': group['trade_date'],
            'time': group['time'],
            'symbol': symbol,
            'factor': adl
        })

        result_dfs.append(result)

    # 合并所有symbol的结果
    if result_dfs:
        result_df = pd.concat(result_dfs, ignore_index=True)
    else:
        # 如果没有数据，返回空DataFrame
        result_df = pd.DataFrame(columns=['trade_date', 'time', 'symbol', 'factor'])

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    return result_df

