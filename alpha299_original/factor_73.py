# Alpha299因子 - factor_73
# 原始因子编号: 73
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_73(data_df, w: int | None = 10, uni_col: str | None = None):
    # 三段式混合模型窗口配置
    window_configs = {
        'corr_window': 10,      # 相关性窗口
        'std_window': 20,       # 标准化窗口
    }
    
    def calculate_window_sizes(
        w1_inputs: list,
        alpha: float = 1.0,
        w_max: float = 300.0,
        lambda_rate: float = 0.1
    ) -> pd.DataFrame:
        """
        三段式混合模型：动态窗口计算函数
        """
        if not window_configs:
            raise ValueError("请至少传入一个窗口参数。")

        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            results_list = []
            for w1_raw in w1_inputs:
                w1 = max(1.0, w1_raw)
                row_data = {'w1_input': w1_raw}
                for name in window_configs:
                    final_value = min(max(base_val, w1), w_max)
                    row_data[name] = final_value
                results_list.append(row_data)
            return pd.DataFrame(results_list).set_index('w1_input')

        # 主要逻辑：三段式混合模型
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw} 
            
            # 根据w1的值，选择不同的计算模式
            if w1 < min_base:
                # 模式A: 线性缩放
                max_window_current_val = w1 * (max_base / min_base)
                for name, base_value in window_configs.items():
                    final_value = max_window_current_val * (base_value / max_base)
                    row_data[name] = int(round(final_value))
            elif w1 == min_base:
                # 模式B: 锚点时刻，返回初始值
                for name, base_value in window_configs.items():
                    row_data[name] = base_value
            else: # w1 > min_base
                # 模式C: 动态范围归一化
                dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
                dynamic_max = min(dynamic_max, w_max)
                for name, base_value in window_configs.items():
                    position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                    target_range_size = max(0, dynamic_max - w1)
                    final_value = w1 + position * target_range_size
                    final_value = min(max(final_value, w1), w_max)
                    row_data[name] = int(round(final_value))
                        
            results_list.append(row_data)
            
        df = pd.DataFrame(results_list).set_index('w1_input')
        return df
    
    # 使用动态窗口计算
    if w is not None:
        window_sizes = calculate_window_sizes([w])
        corr_window = window_sizes.loc[w, 'corr_window']
        std_window = window_sizes.loc[w, 'std_window']
    else:
        corr_window = window_configs['corr_window']
        std_window = window_configs['std_window']

    # 参数推导
    # corr_window = w  # 相关性窗口  # 现在使用动态计算的值
    # std_window = 2 * w  # 标准化窗口  # 现在使用动态计算的值

    df = data_df.copy()

    # 1. 计算每日开盘价与收盘价差的绝对值
    df['AbsDiff_CO'] = (df['close'] - df['open']).abs()

    # 2. 计算每个股票的AbsDiff_CO历史标准差（expanding窗口）
    # expanding().std() 在数据不足时会产生NaN，这是正常的，无需额外处理
    df['AbsDiff_CO_std'] = df.groupby('symbol')['AbsDiff_CO'].transform(lambda x: x.expanding().std())

    # 3. 计算每日价差 Diff_CO
    df['Diff_CO'] = df['close'] - df['open']

    # 4. 计算10期滚动相关系数 CorrCO_10
    def calc_corr(group):
        # 确保输入给corr的数据不是常数，避免std=0的情况
        # 同时处理滚动窗口内的NaN/Inf，corr函数会自动处理部分，但为了稳健，对结果进行后处理
        corr_result = group['close'].rolling(window=corr_window).corr(group['open'])
        # 将NaN和Inf填充为0，因为常数序列的相关性无意义，可以视为0
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    df['CorrCO_10'] = df.groupby('symbol').apply(calc_corr).reset_index(level=0, drop=True)

    # 5. 计算20期滚动均值 MeanClose_20
    # mean()函数对NaN有内置处理，但为了避免除以0，在后续计算中添加小常数
    df['MeanClose_20'] = df.groupby('symbol')['close'].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).mean()
    )

    # 6. 对Diff_CO进行20期滚动标准化
    # rolling().mean() 和 rolling().std() 对NaN有内置处理
    df['Diff_CO_mean'] = df.groupby('symbol')['Diff_CO'].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).mean()
    )
    df['Diff_CO_std'] = df.groupby('symbol')['Diff_CO'].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).std()
    )
    # 避免除以0
    df['Term2'] = (df['Diff_CO'] - df['Diff_CO_mean']) / (df['Diff_CO_std'].replace(0, 1e-8) + 1e-8) # 将std为0的替换为NaN再加小常数

    # 7. 对CorrCO_10进行20期滚动标准化
    # rolling().mean() 和 rolling().std() 对NaN有内置处理
    df['CorrCO_mean'] = df.groupby('symbol')['CorrCO_10'].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).mean()
    )
    df['CorrCO_std'] = df.groupby('symbol')['CorrCO_10'].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).std()
    )
    # 避免除以0
    df['Term3'] = (df['CorrCO_10'] - df['CorrCO_mean']) / (df['CorrCO_std'].replace(0, 1e-8) + 1e-8) # 将std为0的替换为NaN再加小常数

    # 8. 计算Term1' = AbsDiff_CO_std / MeanClose_20
    # 避免除以0
    df['Term1'] = df['AbsDiff_CO_std'] / (df['MeanClose_20'].replace(0, 1e-8) + 1e-8) # 将MeanClose_20为0的替换为NaN再加小常数

    # 9. 计算SumTerms' = Term1 + Term2 + Term3
    # 加法操作会自动传播NaN
    df['SumTerms'] = df['Term1'] + df['Term2'] + df['Term3']

    # 10. 横截面百分比排序并取负
    # rank(pct=True) 会自动处理NaN，将其排除在排序之外
    df['rank'] = df.groupby(['trade_date', 'time'])['SumTerms'].transform(lambda x: x.rank(pct=True))
    df['factor'] = -df['rank']

    # 11. 处理无效值
    # 在计算过程中可能产生的Inf/NaN在rank和后续dropna中会处理，但为了保险，再次替换Inf
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择输出列并去重
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

