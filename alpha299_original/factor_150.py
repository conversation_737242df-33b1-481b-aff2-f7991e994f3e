# Alpha299因子 - factor_150
# 原始因子编号: 150
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_150(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算Alpha 45因子：价格动量与VWAP成交量相关性排名乘积因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要的列
        w (int | None): 核心可调参数（此处因子中无天数参数，设为None）
        uni_col (str | None): 基础数据列（此处因子使用多列，设为None）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    df = data_df.copy()

    # 1. 确保数据按symbol和time排序
    df = df.sort_values(by=['symbol', 'time'])

    # 2. 计算VWAP（成交量加权平均价）
    # 避免除以0
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 3. 计算混合价格Pmix
    df['pmix'] = 0.6 * df['close'] + 0.4 * df['open']

    # 4. 计算混合价格1期差分
    df['delta_pmix'] = df.groupby('symbol')['pmix'].transform(lambda x: x.diff())

    # 5. 对delta_pmix进行横截面百分比排序（按时间点分组）
    df['r1'] = df.groupby('time')['delta_pmix'].transform(lambda x: x.rank(pct=True))

    # 6. 计算成交量的150期移动平均
    df['mavol_150'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=150, min_periods=w).mean()
    )

    # 7. 计算MAVol与VWAP的15期滚动相关系数
    # 滚动相关系数可能产生NaN或Inf，需要后处理
    df['corr_volvwap'] = df.groupby('symbol').apply(
        lambda group: group['mavol_150'].rolling(window=15, min_periods=w).corr(group['vwap'])
    ).reset_index(level=0, drop=True)

    # 处理滚动相关系数可能产生的NaN或Inf，替换为0
    df['corr_volvwap'] = df['corr_volvwap'].replace([float('inf'), float('-inf')], np.nan).fillna(0)


    # 8. 对相关系数进行横截面百分比排序
    df['r2'] = df.groupby('time')['corr_volvwap'].transform(lambda x: x.rank(pct=True))

    # 9. 计算最终因子值（R1 * R2）
    df['factor'] = df['r1'] * df['r2']

    # 10. 处理±inf为NaN（因子信息明确要求）
    df['factor'] = df['factor'].replace([float('inf'), float('-inf')], float('nan'))

    # 11. 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 12. 输出结果（保留必要列并删除NaN）
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

