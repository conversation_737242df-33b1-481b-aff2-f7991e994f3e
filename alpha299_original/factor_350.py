# Alpha299因子 - factor_350
# 原始因子编号: 350
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_350(data_df, w: int | None = 5, uni_col: str | None = 'volume'):
    """
    计算Alpha_184因子

    参数:
    - data_df: 输入的DataFrame
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 使用的数据列，默认为'volume'

    返回:
    - 包含因子值的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 5,      # 差值窗口
        'zscore_window': 10     # 滚动Z-score标准化窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']
    zscore_window = window_sizes['zscore_window']

    # 复制数据以避免修改原始数据
    df = data_df.copy()

    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 确保uni_col存在
    if uni_col not in df.columns:
        raise ValueError(f"数据中缺少必要的列: {uni_col}")

    # 步骤1: 对成交量应用反正切函数
    # 确保输入大于0，避免arctan(0)
    df['I1'] = np.arctan(df[uni_col] + 1e-8)

    # 步骤2: 对I1应用余弦函数进行非单调变换
    df['I2'] = np.cos(df['I1'])

    # 步骤3: 对I2进行截面排名
    df['I3'] = df.groupby('trade_date')['I2'].transform(lambda x: x.rank(method='average'))

    # 步骤4: 计算I3的delta_window期差值
    df['I4'] = df.groupby('symbol')['I3'].transform(lambda x: x - x.shift(delta_window))

    # 步骤5: 对成交量取自然对数
    # 确保输入大于0，避免log(0)或log(负数)
    df['I5'] = np.log(df[uni_col].clip(lower=1e-8))

    # 步骤6: 计算I5在过去zscore_window期的滚动Z-score标准化
    # 先计算滚动均值
    df['rolling_mean'] = df.groupby('symbol')['I5'].transform(
        lambda x: x.rolling(window=zscore_window, min_periods=w).mean()
    )

    # 再计算滚动标准差
    df['rolling_std'] = df.groupby('symbol')['I5'].transform(
        lambda x: x.rolling(window=zscore_window, min_periods=w).std(ddof=1)
    )

    # 计算Z-score
    # 避免除以0，增加一个小的常数
    df['I6'] = (df['I5'] - df['rolling_mean']) / (df['rolling_std'] + 1e-8)

    # 步骤7: 计算I4与I6的和，得到因子值
    df['factor'] = df['I4'] + df['I6']

    # 步骤8: 将结果中的正无穷和负无穷替换为NaN
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()
    return result_df

