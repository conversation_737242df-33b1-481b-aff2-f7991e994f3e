# Alpha299因子 - factor_279
# 原始因子编号: 279
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_279(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha30因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 可调参数，单位为天，默认6
        uni_col (str | None): 单一基础列参数，此处设为None

    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 6        # DELTA计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']        # DELTA计算窗口

    # 检查必要列是否存在
    required_columns = ['low', 'open', 'volume', 'close', 'trade_date', 'time', 'symbol']
    missing_cols = [col for col in required_columns if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    # ===== 修复关键点 =====
    # 原有的全局vwap计算被移除，因为它会导致未来数据和跨资产数据泄露
    # if 'vwap' not in data_df.columns:
    #     safe_volume = data_df['volume'].apply(lambda x: max(x, 1e-8))
    #     data_df['vwap'] = (data_df['close'] * safe_volume).cumsum() / (safe_volume.cumsum() + 1e-8)

    def process_group(group):
        # 1. 确保时间排序，这是消除泄露的基础
        group = group.sort_values('time').reset_index(drop=True)

        # 2. ===== 修复关键点 =====
        # 将VWAP的计算移到分组并排序后，确保cumsum在单个资产的有序序列上进行
        if 'vwap' not in group.columns:
            # 确保volume非负且非零
            safe_volume = group['volume'].apply(lambda x: max(x, 1e-8))
            # 这里的 cumsum() 现在是安全的
            group['vwap'] = (group['close'] * safe_volume).cumsum() / (safe_volume.cumsum() + 1e-8)

        # 3. 后续计算现在基于干净、有序的数据
        # 步骤1: 计算T1 = gp_min(low, vwap)
        group['T1'] = group[['low', 'vwap']].min(axis=1)

        # 步骤2: 计算X1 = delta(T1, delta_window)
        # 这里的shift现在是安全的，因为它在有序序列上进行
        group['X1'] = group['T1'] - group['T1'].shift(delta_window)

        # 步骤3-4: 计算T2 = sqrt(abs(open)), T3 = abs(open)
        # 确保open非负
        safe_open_abs = np.abs(group['open'])
        group['T2'] = np.sqrt(safe_open_abs)
        group['T3'] = safe_open_abs

        # 步骤5: 计算X2 = T2 / T3
        # 确保T3非零
        group['X2'] = group['T2'] / (group['T3'] + 1e-8)

        # 步骤6: 计算Alpha30 = X1 / X2
        # 确保X2非零
        group['factor'] = group['X1'] / (group['X2'] + 1e-8)

        # 步骤7: 替换无穷大为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        return group

    # 按symbol分组处理
    df = data_df.copy()
    df = df.groupby('symbol', group_keys=False).apply(process_group)

    # 处理日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去重
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

