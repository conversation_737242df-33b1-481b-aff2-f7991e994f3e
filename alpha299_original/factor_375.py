# Alpha299因子 - factor_375
# 原始因子编号: 375
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_375(data_df, w: int | None = 13, uni_col: str | None = None):
    """
    计算Alpha35因子

    Alpha35 = -max(correlation(amount, close, 14), 13)

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为14天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 不使用，因为需要同时使用amount和close两列数据
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 14,    # 滚动相关系数窗口
        'max_window': 13      # 滚动最大值窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']
    max_window = window_sizes['max_window']

    # 复制输入数据，避免修改原始数据
    df = data_df.copy()

    # 转换日期格式以便进行时间序列操作
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df['time'] = pd.to_datetime(df['time'])

    # 确保数据按照symbol和时间排序
    df = df.sort_values(['symbol', 'time'])

    # 1. 计算成交额和收盘价在过去14个周期内的滚动相关系数
    def rolling_corr(x):
        # 确保输入数据没有inf或nan
        amount_clean = x['amount'].replace([np.inf, -np.inf], np.nan)
        close_clean = x['close'].replace([np.inf, -np.inf], np.nan)

        # 计算滚动相关系数
        corr_result = amount_clean.rolling(window=corr_window, min_periods=w).corr(close_clean)

        # 将相关系数结果中的nan或inf替换为0
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    # 按symbol分组计算滚动相关系数
    df['rolling_corr'] = df.groupby('symbol').apply(
        lambda x: rolling_corr(x)
    ).reset_index(level=0, drop=True)

    # 2. 计算滚动相关系数在过去13个周期内的滚动最大值
    # 确保输入到rolling.max的数据没有inf或nan
    df['rolling_max'] = df.groupby('symbol')['rolling_corr'].transform(
        lambda x: x.replace([np.inf, -np.inf], np.nan).rolling(window=max_window, min_periods=w).max()
    )

    # 3. 计算相反数得到Alpha35
    df['factor'] = -df['rolling_max']

    # 4. 将无穷大值替换为NaN (这一步在计算rolling_max时已经处理，但为了保险保留)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式为字符串
    df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回结果DataFrame，只包含必要的列
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    return result_df

