# Alpha299因子 - factor_62
# 原始因子编号: 62
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_62(data_df, w: int | None = 7, uni_col: str | None = None,
              alpha: float = 1.0, w_max: float = 300.0, lambda_rate: float = 0.1):
    # 定义所有窗口的基准值
    window_configs = {
        'ma_window': 20,        # 均线窗口
        'ts_window': 60,        # 时间序列窗口（3倍关系）
        'delta_window': 7       # 差分窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        nonlocal w_max, lambda_rate, alpha
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    ma_window = window_sizes['ma_window']           # 均线窗口
    ts_window = window_sizes['ts_window']           # 时间序列窗口
    delta_window = window_sizes['delta_window']     # 差分窗口

    """
    计算Alpha 180因子：条件价格动量与成交量因子，使用动态窗口系统

    参数:
        data_df (pd.DataFrame): 输入数据，包含所需列
        w (int | None): 核心可调参数，基础窗口大小 (默认20)
        uni_col (str | None): 基础数据列 (默认None，因涉及多列)
        alpha: 动态窗口的非线性调整参数（默认1.0）
        w_max: 动态窗口的绝对上限（默认300.0）
        lambda_rate: 动态窗口的增长率（默认0.1）

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """

    # 按品种分组计算均线
    # 保护：volume可能为0，但rolling mean本身对0是支持的，无需特殊处理
    data_df['ma_volume'] = data_df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=ma_window, min_periods=w).mean()
    )

    # 计算条件：当日成交量 > 均量
    data_df['cond'] = data_df['volume'] > data_df['ma_volume']

    # 计算价格差分
    data_df['delta_price'] = data_df.groupby('symbol')['close'].transform(
        lambda x: x.diff(delta_window)
    )

    # 计算绝对值
    data_df['abs_delta'] = data_df['delta_price'].abs()

    # 定义ts_rank计算函数（百分比排名）
    def _ts_rank(x):
        # 保护：处理输入为空的情况
        if len(x) == 0 or np.all(np.isnan(x)):
            return np.nan
        # 保护：rankdata的nan_policy='omit'已经处理了nan，但分母需要避免除以0
        ranked_data = rankdata(x, method='average', nan_policy='omit')
        if len(ranked_data) == 0:
             return np.nan
        return ranked_data[-1] / (len(x) + 1e-8) # 保护：分母加1e-8避免除以0

    # 计算时序百分比排名
    # 保护：rolling apply可能产生nan，这是正常的，无需额外处理
    data_df['ts_rank'] = data_df.groupby('symbol')['abs_delta'].transform(
        lambda x: x.rolling(window=ts_window, min_periods=w).apply(_ts_rank, raw=True) # 使用raw=True提高性能
    )

    # 计算TermA和TermB
    # 保护：np.sign对0返回0，对nan返回nan，无需特殊处理
    data_df['sign_delta'] = np.sign(data_df['delta_price'])
    # 保护：ts_rank和sign_delta都可能为nan，乘积会是nan，这是正常的
    data_df['termA'] = -data_df['ts_rank'] * data_df['sign_delta']
    data_df['termB'] = -data_df['volume']

    # 根据条件选择因子值
    # 保护：np.where对nan的处理是正常的，无需额外处理
    data_df['factor'] = np.where(data_df['cond'], data_df['termA'], data_df['termB'])

    # 处理无穷值
    data_df['factor'] = data_df['factor'].replace([np.inf, -np.inf], np.nan)

    # 恢复日期和时间格式
    data_df['trade_date'] = pd.to_datetime(data_df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    data_df['time'] = pd.to_datetime(data_df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 返回所需列并去除NaN
    # 保护：dropna是正常的处理方式
    result_df = data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    return result_df

