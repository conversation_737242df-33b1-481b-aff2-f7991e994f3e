# Alpha299因子 - factor_250
# 原始因子编号: 250
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_250(data_df, w: int | None = 10, uni_col: str | None = None):
    """
    计算Alpha_181因子

    参数:
    data_df (pd.DataFrame): 输入数据，包含必要的列
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为10天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col (str): 单一基础数据列（本因子不适用，设为None）

    返回:
    pd.DataFrame: 包含因子值的DataFrame，列包括['trade_date', 'time', 'symbol', 'factor']
    """
    # 定义所有窗口的基准值
    window_configs = {
        'std_window': 10,  # volume标准差窗口
        'max_window': 10   # low最大值窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    std_window = window_sizes['std_window']
    max_window = window_sizes['max_window']
    
    df = data_df.copy()

    # 确保按symbol和时间排序
    df = df.sort_values(by=['symbol', 'trade_date', 'time'])

    # 计算vwap
    # vwap = (sum((high + low + close)/3 * volume) for each time period) / sum(volume)
    df['price'] = (df['high'] + df['low'] + df['close']) / 3
    df['price_volume'] = df['price'] * df['volume']

    # 使用cumsum计算累计值
    df['cum_price_volume'] = df.groupby('symbol')['price_volume'].cumsum()
    df['cum_volume'] = df.groupby('symbol')['volume'].cumsum()

    # 计算vwap
    df['vwap'] = df['cum_price_volume'] / (df['cum_volume'] + 1e-8)

    # 计算volume的滚动标准差
    # 应对rolling std可能为0的情况
    df['ts_std_volume'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=std_window, min_periods=w).std(ddof=1).fillna(0))  # ddof=1表示无偏标准差

    # 计算元素级最小值
    df['I2'] = df[['vwap', 'ts_std_volume']].min(axis=1)

    # 计算low的滚动最大值
    df['ts_max_low'] = df.groupby('symbol')['low'].transform(
        lambda x: x.rolling(window=max_window, min_periods=w).max())

    # 计算最终因子值
    # 应对ts_max_low可能为0的情况
    df['factor'] = df['I2'] / (df['ts_max_low'] + 1e-8)

    # 替换无穷值为NaN
    df['factor'] = df['factor'].replace([float('inf'), -float('inf')], float('nan'))

    # 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去除NaN
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

