# Alpha299因子 - factor_227
# 原始因子编号: 227
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_227(data_df, w: int = 1, uni_col: str = None):
    """
    计算Alpha14因子

    参数:
        data_df (DataFrame): 包含原始数据的DataFrame
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
           注意：当w小于最小窗口时，所有窗口按比例线性缩放；
           当w等于最小窗口时，所有窗口使用其默认值；
           当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str): 单一列参数（本因子不需要使用，设置为None）

    返回:
        DataFrame: 包含因子结果的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 9    # 滚动相关系数窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']
    
    # 验证必要列存在
    required_columns = ['symbol', 'trade_date', 'time', 'close', 'volume']
    if not all(col in data_df.columns for col in required_columns):
        raise ValueError(f"输入数据缺少必要列: {required_columns}")

    df = data_df.copy()

    # 1. 计算log(volume)，处理volume为0或负数的情况
    df['log_volume'] = np.log(df['volume'].abs() + 1e-8)

    # 2. 按symbol分组计算滚动相关系数
    def compute_corr(group):
        # 计算log_volume和close的滚动相关系数
        # pandas rolling().corr() 在窗口内为常数时会返回NaN，这是正常的，不需要额外处理
        # 如果窗口内包含inf/nan，corr也会返回nan，这也是正常的
        corr_result = group['log_volume'].rolling(
            window=corr_window,
            min_periods=w
        ).corr(group['close'])
        # 确保相关系数结果中的inf/nan被替换为0，因为相关系数的定义域是[-1, 1]
        return corr_result.fillna(0).replace([np.inf, -np.inf], 0)

    # 应用分组计算
    df['factor'] = df.groupby('symbol').apply(compute_corr).reset_index(level=0, drop=True)

    # 3. 处理无穷大值 (在compute_corr中已经处理了，这里保留以防万一)
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 4. 严格格式化日期和时间列
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 5. 构建输出DataFrame
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].copy()

    return result_df

