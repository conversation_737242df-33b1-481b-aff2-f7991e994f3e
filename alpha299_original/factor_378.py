# Alpha299因子 - factor_378
# 原始因子编号: 378
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_378(data_df, w: int | None = 5, uni_col: str | None = None):
    """
    计算Alpha3因子

    参数：
    - data_df: 输入数据框，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close',
               'volume', 'amount', 'open_interest', 'industry_name']
    - w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
         注意：当w小于最小窗口时，所有窗口按比例线性缩放；
         当w等于最小窗口时，所有窗口使用其默认值；
         当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    - uni_col: 单一列参数，该因子不使用单一列，设为None

    返回：
    - 包含['trade_date', 'time', 'symbol', 'factor']的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 5,      # delta窗口
        'rank_window': 8,       # ts_rank窗口
        'zscore_window': 20     # ts_zscore窗口
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    delta_window = window_sizes['delta_window']
    rank_window = window_sizes['rank_window']
    zscore_window = window_sizes['zscore_window']

    # 复制数据，避免修改原始数据
    df = data_df.copy()

    # 确保日期列格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')

    # 计算vwap (成交量加权平均价)
    # 保护：分母volume可能为0，加一个小的常数
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)

    # 按symbol分组计算因子
    result_dfs = []
    for symbol, group in df.groupby('symbol'):
        group = group.sort_values('time')

        # 1. 计算vwap在过去5个周期内的差值: delta(vwap, 5)
        group['delta_vwap'] = group['vwap'].diff(delta_window)

        # 2. 对delta_vwap进行过去20个周期内的滚动Z-score标准化: ts_zscore(delta_vwap, 20)
        # 保护：滚动标准差可能为0，加一个小的常数
        group['rolling_mean'] = group['delta_vwap'].rolling(window=zscore_window, min_periods=w).mean()
        group['rolling_std'] = group['delta_vwap'].rolling(window=zscore_window, min_periods=w).std()
        group['ts_zscore'] = (group['delta_vwap'] - group['rolling_mean']) / (group['rolling_std'] + 1e-8)

        # 3. 计算收盘价的相反数: neg(close)
        group['neg_close'] = -group['close']

        # 4. 计算neg_close在过去8个周期内的滚动排名并归一化: ts_rank(neg_close, 8)
        def rolling_rank(series, window):
            result = []
            values = series.values
            for i in range(len(values)):
                if i >= window - 1:
                    window_data = values[i-window+1:i+1]
                    # 过滤掉NaN值
                    valid_data = window_data[~np.isnan(window_data)]
                    if len(valid_data) == 0:
                         result.append(np.nan)
                         continue
                    # 降序排名（最大值为1）
                    ranks = np.zeros(len(valid_data))
                    temp = np.argsort(valid_data)[::-1]
                    ranks[temp] = np.arange(1, len(valid_data) + 1)
                    # 归一化排名到(0,1]区间
                    # 保护：分母可能为0，加一个小的常数
                    result.append(ranks[-1] / (len(valid_data) + 1e-8))
                elif i > 0:  # min_periods=w
                    window_data = values[:i+1]
                    # 过滤掉NaN值
                    valid_data = window_data[~np.isnan(window_data)]
                    if len(valid_data) == 0:
                         result.append(np.nan)
                         continue
                    ranks = np.zeros(len(valid_data))
                    temp = np.argsort(valid_data)[::-1]
                    ranks[temp] = np.arange(1, len(valid_data) + 1)
                    # 保护：分母可能为0，加一个小的常数
                    result.append(ranks[-1] / (len(valid_data) + 1e-8))
                else:
                    # 保护：只有一个元素时，如果该元素是NaN，则排名为NaN
                    if np.isnan(values[i]):
                        result.append(np.nan)
                    else:
                        result.append(1.0)  # 只有一个元素时，排名为1

            return pd.Series(result, index=series.index)

        group['ts_rank'] = rolling_rank(group['neg_close'], rank_window)

        # 5. 计算ts_rank的绝对值的平方根: sqrt(ts_rank)
        # 保护：ts_rank可能为负数（虽然理论上不会，但为了鲁棒性），取绝对值
        group['sqrt_ts_rank'] = np.sqrt(np.abs(group['ts_rank']))

        # 6. 计算ts_zscore与sqrt_ts_rank的差值得到Alpha3
        group['factor'] = group['ts_zscore'] - group['sqrt_ts_rank']

        # 7. 将结果中的无穷大值替换为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        # 只保留需要的列
        result_dfs.append(group[['trade_date', 'time', 'symbol', 'factor']])

    # 合并所有结果
    result_df = pd.concat(result_dfs)

    # 恢复日期和时间格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 只保留非NaN的行
    result_df = result_df.dropna(subset=['factor'])

    return result_df

