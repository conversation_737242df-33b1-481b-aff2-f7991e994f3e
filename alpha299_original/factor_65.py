# Alpha299因子 - factor_65
# 原始因子编号: 65
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_65(data_df, w=5, uni_col=None):    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 5,        # 相关系数计算窗口 (对应corr_window)
        'sma_window': 20,        # SMA移动平均窗口 (对应sma_window)
        'zscore_window': 20      # Z-score标准化窗口 (对应ts_zscore_window)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    corr_window = window_sizes['corr_window']       # 相关系数计算窗口
    sma_window = window_sizes['sma_window']         # SMA移动平均窗口  
    zscore_window = window_sizes['zscore_window']   # Z-score标准化窗口

    """
    计算Alpha_191因子
    参数说明：
        w: 核心时间窗口参数（单位：天），用于推导其他时间参数
        uni_col: 单一基础列参数（此处设为None，因为因子涉及多个基础列）
    """
    # 参数推导
    ts_zscore_window = zscore_window  # Z-score标准化窗口

    df = data_df.copy()

    # 按symbol分组，并在每个分组内按时间排序
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values(by=['symbol', 'time']).copy()

    # 1. 计算20周期SMA(Volume)
    # 对volume进行保护，避免出现负值或0
    df['volume_protected'] = df['volume'].apply(lambda x: x if x > 0 else 1e-8)
    df['sma_volume'] = (
        df.groupby('symbol')['volume_protected']
        .transform(lambda x: x.rolling(window=sma_window, min_periods=w).mean())
    )

    # 2. 计算SMA(Volume,20)与Low的5周期相关系数
    # 对low进行保护，避免出现异常值影响相关系数计算
    df['low_protected'] = df['low'].apply(lambda x: x if np.isfinite(x) else np.nan)
    df['corr'] = (
        df.groupby('symbol').apply(lambda x:
            x['sma_volume'].rolling(window=corr_window, min_periods=w).corr(x['low_protected'])
        ).reset_index(level=0, drop=True)
    )
    # 对相关系数结果进行保护，将nan或inf填0
    df['corr'] = df['corr'].fillna(0).replace([np.inf, -np.inf], 0)


    # 3. 计算MidPoint
    # 对high和low进行保护，避免出现异常值影响MidPoint计算
    df['high_protected'] = df['high'].apply(lambda x: x if np.isfinite(x) else np.nan)
    df['low_protected'] = df['low'].apply(lambda x: x if np.isfinite(x) else np.nan)
    df['midpoint'] = (df['high_protected'] + df['low_protected']) / (2 + 1e-8)

    # 4. 计算MidPoint - Close
    # 对close进行保护，避免出现异常值影响差值计算
    df['close_protected'] = df['close'].apply(lambda x: x if np.isfinite(x) else np.nan)
    df['midpoint_close_diff'] = df['midpoint'] - df['close_protected']

    # 5. 计算20周期Z-score标准化
    # Z-score标准化中分母为标准差，需要保护避免除以0
    df['zscore'] = (
        df.groupby('symbol')['midpoint_close_diff']
        .transform(lambda x: (x - x.rolling(window=ts_zscore_window, min_periods=w).mean())
                 / (x.rolling(window=ts_zscore_window, min_periods=w).std() + 1e-8))
    )
    # 对zscore结果进行保护，将nan或inf填0
    df['zscore'] = df['zscore'].fillna(0).replace([np.inf, -np.inf], 0)


    # 6. 组合因子值
    df['factor'] = df['corr'] + df['zscore']

    # 7. 处理无效值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)

    # 8. 恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 9. 选择必要列并清理无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

