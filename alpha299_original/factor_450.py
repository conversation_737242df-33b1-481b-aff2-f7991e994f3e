# Alpha299因子 - factor_450
# 原始因子编号: 450
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_450(data_df, w: int | None = None, uni_col: str | None = None):
    """
    计算向量除法因子

    参数:
    data_df: 输入数据DataFrame
    w: 窗口参数，本因子不需要窗口，设为None
    uni_col: 单一列参数，本因子需要两列数据进行计算，设为None

    返回:
    包含因子值的DataFrame，列为['trade_date', 'time', 'symbol', 'factor']
    """
    # 因子思路中要求两个输入序列进行向量除法
    # 这里我们使用close作为被除数，volume作为除数
    # 这样计算的因子可以理解为每单位成交量对应的价格

    df = data_df.copy()

    # 确保输入数据包含必要的列
    required_columns = ['close', 'volume', 'trade_date', 'time', 'symbol']
    if not all(col in df.columns for col in required_columns):
        missing_cols = [col for col in required_columns if col not in df.columns]
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    # 执行向量除法运算: close / volume
    # 为了避免除以0导致无穷大，对volume加上一个很小的数
    df['factor'] = df['close'] / (df['volume'] + 1e-8)

    # 处理可能出现的无穷大或NaN值
    # 当volume为0时，除法会产生无穷大，我们将其标记为NaN
    # 实际上由于加了1e-8，这里不会产生inf，但为了鲁棒性保留
    df.loc[np.isinf(df['factor']), 'factor'] = np.nan
    df.loc[df['factor'].isnull(), 'factor'] = np.nan # 显式处理NaN

    # 确保日期和时间格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并删除含有NaN的行
    # 根据要求，不刻意fillna(0)，保留真实的NaN
    result_df = df[['trade_date', 'time', 'symbol', 'factor']]

    return result_df

