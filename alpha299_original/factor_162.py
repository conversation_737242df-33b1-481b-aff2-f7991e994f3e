# Alpha299因子 - factor_162
# 原始因子编号: 162
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_162(data_df, w: int | None = 8, uni_col: str | None = None):
    """
    计算Alpha 61因子：VWAP动量与最低价成交量相关性排名的最大值因子

    参数:
        data_df (pd.DataFrame): 输入数据，包含['symbol', 'trade_date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest', 'industry_name']
        w (int | None): 核心可调参数（天数），默认为12
        uni_col (str | None): 单一基础数据列参数，此处设为None因为使用多列数据

    返回:
        pd.DataFrame: 包含['trade_date', 'time', 'symbol', 'factor']的因子结果
    """
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        
        参数:
            w1_input: 外部输入的窗口参数
            
        返回:
            dict: 包含各个窗口大小的字典
        """
        import numpy as np
        
        # 窗口配置
        window_configs = {
            'n_decay1': 12.0,      # w
            'n_decay2': 17.0,      # int(17 * w / 12)
            'n_corr_window': 8.0,  # int(8 * w / 12)
            'n_ma_window': 80.0    # int(80 * w / 12)
        }
        
        # 模型参数
        w_max = 30.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        # 三段式混合模型
        final_sizes = {}
        
        if w1 < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            # 模式B: 锚点时刻，返回初始值
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:  # w1 > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    # 计算实际窗口大小
    window_sizes = calculate_window_sizes(w)
    n_decay1 = window_sizes['n_decay1']
    n_decay2 = window_sizes['n_decay2']
    n_corr_window = window_sizes['n_corr_window']
    n_ma_window = window_sizes['n_ma_window']

    df = data_df.copy()

    # 1. 计算VWAP
    df['cum_amount'] = df.groupby('symbol')['amount'].cumsum()
    df['cum_volume'] = df.groupby('symbol')['volume'].cumsum()
    # 保护除以0
    df['vwap'] = df['cum_amount'] / (df['cum_volume'] + 1e-8)

    # 2. 计算VWAP的1期差分
    df['delta_vwap'] = df.groupby('symbol')['vwap'].diff(1)

    # 3. 对delta_vwap应用线性衰减加权求和（使用实际窗口大小）
    def linear_decay(series, window):
        # 保护除以0
        weights = 2 * np.arange(1, window+1) / (window * (window + 1) + 1e-8)
        # 保护rolling apply可能产生的nan/inf
        result = series.rolling(window).apply(lambda x: np.sum(x * weights), raw=True)
        return result.replace([np.inf, -np.inf], np.nan)

    df['dl1'] = df.groupby('symbol')['delta_vwap'].transform(lambda x: linear_decay(x, n_decay1))

    # 4. 横截面百分比排序R1
    df['r1'] = df.groupby('time')['dl1'].rank(pct=True)

    # 5. 计算最低价(Low)与成交量滚动均值的滚动相关系数（使用实际窗口大小）
    df['ma_volume'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(n_ma_window, min_periods=w).mean())

    # 保护滚动相关系数可能产生的nan/inf
    df['corr_lv'] = df.groupby('symbol').apply(
        lambda group: group['low'].rolling(n_corr_window, min_periods=w).corr(group['ma_volume']).replace([np.inf, -np.inf], np.nan).fillna(0)
    ).reset_index(level=0, drop=True)


    # 6. 对corr_lv进行横截面百分比排序
    df['rank_corr_lv'] = df.groupby('time')['corr_lv'].rank(pct=True)

    # 7. 对rank_corr_lv应用线性衰减加权求和（使用实际窗口大小）
    df['dl2'] = df.groupby('symbol')['rank_corr_lv'].transform(lambda x: linear_decay(x, n_decay2))

    # 8. 对dl2进行横截面百分比排序R2
    df['r2'] = df.groupby('time')['dl2'].rank(pct=True)

    # 9. 取R1和R2的最大值并取负
    df['factor'] = -df[['r1', 'r2']].max(axis=1)

    # 10. 处理无效值
    df['factor'] = df['factor'].replace([np.inf, -np.inf], np.nan)
    df = df.dropna(subset=['factor'])

    # 严格按照要求恢复日期和时间格式
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    output_df = df[['trade_date', 'time', 'symbol', 'factor']]
    return output_df

