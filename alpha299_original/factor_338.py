# Alpha299因子 - factor_338
# 原始因子编号: 338
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_338(data_df, w: int | None = 6, uni_col: str | None = None):
    """
    计算Alpha135因子
    
    参数:
    data_df: 输入数据DataFrame
    w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
       当w等于最小窗口时，所有窗口使用其默认值；
       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
    uni_col: 单一列参数，本因子不使用单一列，设为None
    
    返回:
    包含trade_date, time, symbol和factor列的DataFrame
    """
    def calculate_window_sizes(w: float | None, window_configs: dict) -> dict:
        """
        计算所有窗口的最终大小，使用三段式混合模型。
        
        参数：
        - w: 基准参数，单位为天，代表因子内部最小窗口期
        - window_configs: 包含所有窗口基准值的字典
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 模型参数
        alpha = 1.0  # 非线性调整参数
        w_max = 30.0  # 绝对上限
        lambda_rate = 0.1  # 动态天花板增长率
        
        # 获取最小和最大基准窗口
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 如果w为None，返回所有窗口的默认值
        if w is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 确保w至少为1
        w = max(1.0, w)
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            # 处理所有窗口值相同的情况（避免除零错误）
            if max_base == min_base:
                # 如果所有窗口值相同，直接使用w作为结果
                return {k: max(1, int(w)) for k, v in window_configs.items()}
            
            def calculate_final_window(base_value):
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                return max(1, int(min(max(final_value, w), w_max)))
            
            return {k: calculate_final_window(v) for k, v in window_configs.items()}

    # 定义所有窗口的基准值
    window_configs = {
        'window': 6  # 所有滚动计算的窗口期
    }
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w, window_configs)
    
    # 解包窗口大小
    window = window_sizes['window']

    df = data_df.copy()
    
    # 确保日期格式正确
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time'], format='%Y-%m-%dT%H:%M:%S')
    
    # 计算vwap (成交量加权平均价)
    # 保护成交量为0的情况
    df['vwap'] = df['amount'] / (df['volume'] + 1e-8)
    
    # 定义辅助函数
    def ts_rank(series, window):
        """
        计算滚动排名并归一化到(0, 1]区间
        min_periods=w，表示只要有一个有效值就进行计算
        """
        result = np.full(len(series), np.nan)
        
        for i in range(len(series)):
            if i < window - 1:
                # 当数据不足window个时，只要有数据就计算（min_periods=w）
                if i >= 0:  # 确保至少有1个数据点
                    valid_data = series.iloc[:i+1].dropna().values
                    if len(valid_data) > 0:
                        # 使用scipy.stats.rankdata计算排名（降序）
                        ranks = stats.rankdata(-valid_data, nan_policy='omit')
                        # 归一化到(0, 1]区间
                        # 保护分母为0的情况
                        result[i] = ranks[-1] / (len(valid_data) + 1e-8)
            else:
                valid_data = series.iloc[i-window+1:i+1].dropna().values
                if len(valid_data) > 0:
                    ranks = stats.rankdata(-valid_data, nan_policy='omit')
                    # 保护分母为0的情况
                    result[i] = ranks[-1] / (len(valid_data) + 1e-8)
                
        return result
    
    def sigmoid(x):
        """计算sigmoid函数值"""
        # np.exp(-x)可能产生inf，但sigmoid函数本身不会产生inf或nan，除非输入是inf或nan
        # 对输入进行保护，防止inf或nan导致exp溢出
        x_protected = np.clip(x, -700, 700) # 限制x的范围，防止exp溢出
        return 1 / (1 + np.exp(-x_protected))
    
    def ts_regbeta(x, y, window):
        """
        计算y对x的滚动回归贝塔系数
        min_periods=w，表示只要有一个有效值就进行计算
        """
        result = np.full(len(x), np.nan)
        
        for i in range(len(x)):
            if i < window - 1:
                # 当数据不足window个时，只要有数据就计算（min_periods=w）
                if i >= 0:  # 确保至少有1个数据点
                    x_valid = x.iloc[:i+1].dropna()
                    y_valid = y.iloc[:i+1].dropna()
                    
                    # 确保x和y有相同的索引
                    common_idx = x_valid.index.intersection(y_valid.index)
                    if len(common_idx) > 1:  # 至少需要2个点才能计算回归
                        x_data = x_valid.loc[common_idx].values
                        y_data = y_valid.loc[common_idx].values
                        
                        # 计算协方差和方差
                        cov_xy = np.cov(x_data, y_data)[0, 1]
                        var_x = np.var(x_data, ddof=1)
                        
                        # 保护var_x为0的情况
                        result[i] = cov_xy / (var_x + 1e-8)
            else:
                x_valid = x.iloc[i-window+1:i+1].dropna()
                y_valid = y.iloc[i-window+1:i+1].dropna()
                
                common_idx = x_valid.index.intersection(y_valid.index)
                if len(common_idx) > 1:
                    x_data = x_valid.loc[common_idx].values
                    y_data = y_valid.loc[common_idx].values
                    
                    cov_xy = np.cov(x_data, y_data)[0, 1]
                    var_x = np.var(x_data, ddof=1)
                    
                    # 保护var_x为0的情况
                    result[i] = cov_xy / (var_x + 1e-8)
                
        return result
    
    # 按symbol分组计算因子
    result_dfs = []
    for symbol, group in df.groupby('symbol'):
        # 对group按时间排序
        group = group.sort_values('time')
        
        # 1. 计算成交量在过去window个周期内的滚动排名并归一化
        volume_rank = ts_rank(group['volume'], window)
        
        # 2. 计算sigmoid值
        sigmoid_rank = sigmoid(volume_rank)
        
        # 3. 计算最高价对成交额在过去window个周期内的滚动回归贝塔系数
        regbeta = ts_regbeta(group['amount'], group['high'], window)
        
        # 4. 计算绝对值
        abs_regbeta = np.abs(regbeta)
        
        # 5. 计算除法
        # 保护abs_regbeta为0的情况
        div_result = sigmoid_rank / (abs_regbeta + 1e-8)
        
        # 6. 计算乘积得到Alpha135
        group['factor'] = group['vwap'] * div_result
        
        # 7. 将无穷大值替换为NaN
        group['factor'] = np.where(np.isinf(group['factor']), np.nan, group['factor'])
        # 8. 将NaN值替换为0 (根据因子定义，如果计算失败，结果应为0)
        # 注意：这里根据原代码逻辑，最后是dropna，所以不在这里fillna(0)
        # group['factor'] = np.nan_to_num(group['factor'], nan=0.0)
        
        result_dfs.append(group)
    
    # 合并结果
    result_df = pd.concat(result_dfs)
    
    # 恢复日期格式为字符串
    result_df['trade_date'] = result_df['trade_date'].dt.strftime('%Y-%m-%d').astype('string')
    result_df['time'] = result_df['time'].dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')
    
    # 只保留需要的列
    # 保持原代码逻辑，最后dropna
    result_df = result_df[['trade_date', 'time', 'symbol', 'factor']].dropna()
    
    return result_df

