# Alpha299因子 - factor_252
# 原始因子编号: 252
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_252(data_df, w: int | None = 8, uni_col: str | None = None):
    """计算Alpha_185因子，解决量纲合法性问题"""

    # 定义所有窗口的基准值
    window_configs = {
        'beta_window': 8,      # beta计算窗口
        'zscore_window': 8     # zscore计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    beta_window = window_sizes['beta_window']      # beta计算窗口
    zscore_window = window_sizes['zscore_window']  # zscore计算窗口

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'close', 'open', 'amount', 'low']
    if not all(col in data_df.columns for col in required_cols):
        missing_cols = [col for col in required_cols if col not in data_df.columns]
        raise ValueError(f"输入数据缺少必要列: {missing_cols}")

    df = data_df.copy()

    # 强制转换所有数值列（关键修复点）
    numeric_cols = ['close', 'open', 'amount', 'low', 'volume', 'high', 'open_interest']
    # 使用errors='coerce'将非数值转换为NaN
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')


    # 按symbol分组计算
    def _process_group(group):
        # 使用pandas内置方法计算beta（更稳定）
        def _rolling_beta(y, x, window):
            # 避免除以0，同时处理常数序列的std=0情况
            cov = y.rolling(window=window, min_periods=w).cov(x)
            var_x = x.rolling(window=window, min_periods=w).var()
            # 当var_x接近0时，beta无意义，设为NaN
            beta = cov / (var_x + 1e-8)
            return beta

        # 计算I1: ts_regbeta(amount, low, beta_window)
        # 确保输入amount和low不是全NaN或全inf
        amount_safe = group['amount'].replace([np.inf, -np.inf], np.nan)
        low_safe = group['low'].replace([np.inf, -np.inf], np.nan)
        I1 = _rolling_beta(low_safe, amount_safe, beta_window)

        # 计算I2: div(close, open)
        # 避免除以0
        I2 = group['close'] / (group['open'] + 1e-8)

        # 计算ts_zscore
        def _rolling_zscore(s, window):
            # 避免除以0，同时处理常数序列的std=0情况
            mean_s = s.rolling(window=window, min_periods=w).mean()
            std_s = s.rolling(window=window, min_periods=w).std()
            # 当std_s接近0时，zscore无意义，设为NaN
            zscore = (s - mean_s) / (std_s + 1e-8)
            return zscore

        # 标准化I1和I2
        I1_std = _rolling_zscore(I1, zscore_window)
        I2_std = _rolling_zscore(I2, zscore_window)

        # 使用pandas算术运算确保类型安全（关键修复点）
        group['factor'] = np.abs(I1_std - I2_std)

        # 替换inf为NaN
        group['factor'] = group['factor'].replace([np.inf, -np.inf], np.nan)

        return group

    # 按symbol分组处理（关键修复点：正确保留symbol列）
    df = df.set_index('symbol')
    df = df.groupby('symbol', group_keys=False).apply(_process_group)
    df = df.reset_index()  # 保留symbol作为列

    # 严格按要求格式化日期和时间
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 保留必要列并去重
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].drop_duplicates()

    # 按照要求，最后一步dropna()
    return output_df.dropna()

