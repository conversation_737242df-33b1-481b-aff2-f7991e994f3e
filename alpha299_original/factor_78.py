# Alpha299因子 - factor_78
# 原始因子编号: 78
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_78(data_df, w: int | None = None, uni_col: str | None = None):
    df = data_df.sort_values('time').copy()  # 按时间排序确保滚动窗口正确

    # 计算Term1_t = close - max(close, 5)
    df['Term1_t'] = df['close'] - np.maximum(df['close'], 5.0)

    # 横截面百分比排序R1
    # 按时间分组，每个时间点内对Term1_t进行排名
    df['Term1_t_rank'] = df.groupby('time')['Term1_t'].transform(lambda x: x.rank(pct=True))

    # 计算成交量的40期移动平均
    df['MAVol_40'] = df.groupby('symbol')['volume'].transform(lambda x: x.rolling(window=40, min_periods=w).mean())

    # 计算MAVol_40与Low的5期滚动相关系数
    def compute_corr(group):
        # 滚动窗口计算相关系数
        # 考虑到滚动相关可能出现常数序列导致std=0或包含inf/nan无法计算的情况，对结果进行处理
        corr_result = group['MAVol_40'].rolling(window=5, min_periods=w).corr(group['low'])
        # 将NaN和inf/-inf替换为0
        corr_result.replace([np.inf, -np.inf], np.nan, inplace=True)
        corr_result.fillna(0, inplace=True)
        return corr_result

    df['CorrLV_t'] = df.groupby('symbol').apply(compute_corr).reset_index(level=0, drop=True)

    # 横截面百分比排序R2
    df['CorrLV_t_rank'] = df.groupby('time')['CorrLV_t'].transform(lambda x: x.rank(pct=True))

    # 计算最终因子值
    df['factor'] = - (df['Term1_t_rank'] * df['CorrLV_t_rank'])

    # 替换正负无穷为NaN
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 严格恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S')

    # 选择需要的列并去除无效值
    result_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return result_df

