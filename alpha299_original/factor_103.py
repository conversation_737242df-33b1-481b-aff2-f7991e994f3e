# Alpha299因子 - factor_103
# 原始因子编号: 103
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_103(data_df, w: int | None = None, uni_col: str | None = 'close'):
    """
    计算Alpha 143因子：条件累积乘积因子
    核心逻辑：
    1. 当Close_t > Close_{t-1}时，Term_t = Ret_t
    2. 否则Term_t = 1
    3. 因子值为Term的累积乘积
    """
    # 创建副本避免警告
    data_df = data_df.copy()
    
    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', uni_col]
    missing_cols = [col for col in required_cols if col not in data_df.columns]
    if missing_cols:
        raise ValueError(f"数据缺少必要列: {missing_cols}")
    
    # 确保按股票、日期、时间排序
    data_df = data_df.sort_values(by=['symbol', 'trade_date', 'time'])
    
    # 分组计算收益率（处理了每个股票的第一行NaN）
    data_df['Ret'] = data_df.groupby('symbol')[uni_col].pct_change()
    
    # 定义计算Term的函数（确保在股票分组内比较）
    def calculate_term(group):
        group['Term'] = np.where(
            group[uni_col] > group[uni_col].shift(1),
            group['Ret'],
            1
        )
        # 填充第一个数据点（前序不存在时设为1）
        group['Term'] = group['Term'].fillna(1)  
        return group
    
    # 应用分组计算
    data_df = data_df.groupby('symbol', group_keys=False).apply(calculate_term)
    
    # 处理异常值（无穷大和NaN都设为1，不影响乘积）
    data_df['Term'] = data_df['Term'].replace([np.inf, -np.inf, np.nan], 1)
    
    # 分组计算累积乘积
    data_df['factor'] = data_df.groupby('symbol')['Term'].cumprod()
    
    # 再次处理可能的无穷大
    data_df['factor'] = data_df['factor'].replace([np.inf, -np.inf], np.nan)
    
    # 尝试格式化日期（增加异常处理）
    for col in ['trade_date', 'time']:
        if col in data_df.columns:
            try:
                data_df[col] = pd.to_datetime(data_df[col]).dt.strftime('%Y-%m-%d' if col == 'trade_date' else '%Y-%m-%dT%H:%M:%S')
            except:
                pass  # 保持原格式
    
    # 返回结果（自动过滤NaN）
    return data_df[['trade_date', 'time', 'symbol', 'factor']].dropna()

