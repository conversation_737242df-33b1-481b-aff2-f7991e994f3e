# Alpha299因子 - factor_22
# 原始因子编号: 22
# 自动生成时间: 2025-07-17 15:14:16

# 好因子代码提取结果
# 生成时间: 2025-06-19 21:33:49
# 总共处理因子数: 299
# 成功提取因子数: 299
# 未找到因子数: 0

from math import atan
from math import atan, log
from math import atan, log, sqrt
from math import tanh
from numba import njit
from pandas import DataFrame
from pandas import to_datetime
from scipy import stats
from scipy.special import expit
from scipy.stats import linregress
from scipy.stats import pearsonr
from scipy.stats import rankdata
from scipy.stats import zscore
import math
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import scipy.stats
import scipy.stats as stats
import statsmodels.api as sm



def factor_22(data_df, w: int | None = 7, uni_col: str | None = None):
    """
    计算Alpha43因子：VOLUME/ADV20的时间序列排名与-Δ(CLOSE,7)的时间序列排名的乘积因子
    
    参数:
        data_df (pd.DataFrame): 输入数据，包含必要列
        w (int | None): 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
                       注意：当w小于最小窗口时，所有窗口按比例线性缩放；
                       当w等于最小窗口时，所有窗口使用其默认值；
                       当w大于最小窗口时，窗口值会非线性增长，但不会超过w_max。
        uni_col (str | None): 单一基础列参数（本因子涉及多列，设为None）
        
    返回:
        pd.DataFrame: 包含因子值的DataFrame
    """
    # 定义所有窗口的基准值
    window_configs = {
        'w1': 20,      # ADV和第一个时间序列排名窗口
        'w2': 8,       # 第二个时间序列排名窗口
        'delta_lag': 7 # CLOSE差分滞后期
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        
        参数：
        - w1_input: 外部输入信号，代表因子内部最小窗口期
        
        返回：
        - 包含所有窗口最终大小的字典
        """
        # 核心参数
        w_max = 30.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    
    # 解包窗口大小
    w1 = window_sizes['w1']          # ADV和第一个时间序列排名窗口
    w2 = window_sizes['w2']          # 第二个时间序列排名窗口
    delta_lag = window_sizes['delta_lag']  # CLOSE差分滞后期

    # 检查必要列是否存在
    required_cols = ['symbol', 'trade_date', 'time', 'volume', 'close']
    for col in required_cols:
        if col not in data_df.columns:
            raise ValueError(f"输入数据缺少必要列: {col}")

    df = data_df.copy()

    # 按symbol分组处理，确保数据按时间排序
    df.sort_values(by=['symbol', 'trade_date', 'time'], inplace=True)

    # 1. 计算ADV（平均成交量）
    df['adv20'] = df.groupby('symbol')['volume'].transform(
        lambda x: x.rolling(window=w1, min_periods=w).mean()
    )

    # 2. 计算VOLUME/ADV20
    # 避免除以0，对adv20加一个微小值
    df['volume_adv_ratio'] = df['volume'] / (df['adv20'] + 1e-8)

    # 3. 计算VOLUME/ADV20的时间序列排名
    # rolling().rank(pct=True)会自动处理NaN，但为了健壮性，确保输入没有inf
    df['ts_rank_vol_adv'] = df.groupby('symbol')['volume_adv_ratio'].transform(
        lambda x: x.replace([np.inf, -np.inf], np.nan).rolling(window=w1, min_periods=w).rank(pct=True)
    )

    # 4. 计算CLOSE的差分
    df['delta_close_7'] = df.groupby('symbol')['close'].transform(
        lambda x: x - x.shift(delta_lag)
    )

    # 5. 取负后计算时间序列排名
    df['delta_close_neg'] = -df['delta_close_7']
    # rolling().rank(pct=True)会自动处理NaN，但为了健壮性，确保输入没有inf
    df['ts_rank_close_diff'] = df.groupby('symbol')['delta_close_neg'].transform(
        lambda x: x.replace([np.inf, -np.inf], np.nan).rolling(window=w2, min_periods=w).rank(pct=True)
    )

    # 6. 两部分结果相乘
    df['factor'] = df['ts_rank_vol_adv'] * df['ts_rank_close_diff']

    # 严格按照要求恢复日期和时间格式为字符串
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d').astype('string')
    df['time'] = pd.to_datetime(df['time']).dt.strftime('%Y-%m-%dT%H:%M:%S').astype('string')

    # 选择需要的列并去重NaN
    output_df = df[['trade_date', 'time', 'symbol', 'factor']].dropna()

    return output_df

