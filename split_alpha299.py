#!/usr/bin/env python3
"""
Alpha299因子拆分脚本
使用inspect库将Alpha299.py中的因子函数拆分成独立的文件
"""

import inspect
import importlib.util
import sys
from pathlib import Path
import datetime

def load_alpha299_module():
    """动态加载Alpha299.py模块"""
    try:
        spec = importlib.util.spec_from_file_location("alpha299", "Alpha299.py")
        alpha299_module = importlib.util.module_from_spec(spec)
        sys.modules["alpha299"] = alpha299_module
        spec.loader.exec_module(alpha299_module)
        return alpha299_module
    except Exception as e:
        print(f"❌ 无法加载模块: {e}")
        return None

def extract_imports_from_file():
    """从Alpha299.py文件中提取import语句"""
    with open("Alpha299.py", 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    import_lines = []
    for line in lines:
        stripped = line.strip()
        if (stripped.startswith('import ') or 
            stripped.startswith('from ') or 
            stripped.startswith('#') and 'import' not in stripped.lower() or
            stripped == ''):
            import_lines.append(line.rstrip())
        elif stripped.startswith('def '):
            break
    
    return '\n'.join(import_lines)

def get_factor_functions(module):
    """使用inspect获取所有factor_函数"""
    factors = []
    
    for name, obj in inspect.getmembers(module):
        if (inspect.isfunction(obj) and 
            name.startswith('factor_') and 
            name[7:].isdigit()):  # factor_后面是数字
            
            try:
                # 获取函数源代码
                source = inspect.getsource(obj)
                factor_num = int(name[7:])  # 提取数字部分
                
                factors.append({
                    'name': name,
                    'number': factor_num,
                    'source': source,
                    'function': obj
                })
            except Exception as e:
                print(f"⚠️  无法获取 {name} 的源代码: {e}")
    
    return sorted(factors, key=lambda x: x['number'])

def create_factor_file(factor_info, imports, output_dir):
    """创建单个因子文件"""
    factor_name = factor_info['name']
    factor_num = factor_info['number']
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    file_content = f"""# Alpha299因子 - {factor_name}
# 原始因子编号: {factor_num}
# 自动生成时间: {current_time}

{imports}

{factor_info['source']}
"""
    
    # 保存文件
    file_path = output_dir / f"{factor_name}.py"
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(file_content)
    
    return file_path

def main():
    """主函数"""
    print("=== Alpha299因子拆分工具（使用inspect） ===")
    
    # 检查Alpha299.py文件
    if not Path("Alpha299.py").exists():
        print("❌ 找不到Alpha299.py文件")
        return
    
    # 提取import语句
    print("📦 提取import语句...")
    imports = extract_imports_from_file()
    
    # 加载模块
    print("📖 加载Alpha299模块...")
    module = load_alpha299_module()
    if module is None:
        return
    
    # 获取所有factor函数
    print("🔍 使用inspect获取因子函数...")
    factors = get_factor_functions(module)
    print(f"找到 {len(factors)} 个因子函数")
    
    # 创建输出目录
    original_dir = Path("alpha299_original")
    polars_dir = Path("alpha299_polars")
    original_dir.mkdir(exist_ok=True)
    polars_dir.mkdir(exist_ok=True)
    
    # 拆分因子文件
    print("📝 创建因子文件...")
    success_count = 0
    
    for factor in factors:
        try:
            file_path = create_factor_file(factor, imports, original_dir)
            print(f"✅ 创建: {file_path}")
            success_count += 1
        except Exception as e:
            print(f"❌ 创建失败 {factor['name']}: {e}")
    
    # 创建README文件
    readme_content = f"""# Alpha299原始因子文件

本目录包含从Alpha299.py拆分出的{len(factors)}个独立因子文件。

## 文件说明

- 每个文件包含一个因子函数的完整实现
- 使用inspect库提取，保证代码完整性
- 保留了原始的import语句

## 使用方法

```python
# 导入单个因子
from factor_1 import factor_1

# 使用因子
result = factor_1(data_df)
```

## 转换状态

- 原始因子: {success_count}/{len(factors)} 个文件
- Polars版本: 待开发

生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open(original_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 创建polars目录的README
    polars_readme = f"""# Alpha299 Polars版本因子

本目录将包含转换为Polars表达式的因子实现。

## 转换原则

1. 只关心因子计算逻辑，忽略格式转换和dropna
2. 涉及industry字段的因子暂不复现
3. 优先使用已有的高性能算子
4. 目标是跑通代码，先不关心因子质量

## 目标

- 高性能计算（10-17x加速）
- 内存效率优化
- 准确性保证

待转换因子数量: {len(factors)}
"""
    
    with open(polars_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(polars_readme)
    
    print(f"\n=== 拆分完成 ===")
    print(f"✅ 成功创建: {success_count}/{len(factors)} 个因子文件")
    print(f"📁 原始因子目录: {original_dir}")
    print(f"📁 Polars目录: {polars_dir}")

if __name__ == "__main__":
    main()
