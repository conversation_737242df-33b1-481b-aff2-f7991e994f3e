#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF因子信息添加脚本
将因子类型和备注信息添加到PDF文件的顶部
"""

import fitz  # PyMuPDF
import pandas as pd
import os
import re
from pathlib import Path

def load_factor_description(csv_path):
    """加载因子说明CSV文件，使用中文编码"""
    try:
        # 尝试不同的中文编码
        encodings = ['gbk', 'gb2312', 'utf-8', 'cp936']
        for encoding in encodings:
            try:
                df = pd.read_csv(csv_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取因子说明文件")
                return df
            except UnicodeDecodeError:
                continue
        
        # 如果所有编码都失败，使用默认编码并忽略错误
        df = pd.read_csv(csv_path, encoding='utf-8', errors='ignore')
        print("使用UTF-8编码（忽略错误）读取因子说明文件")
        return df
        
    except Exception as e:
        print(f"读取因子说明文件失败: {e}")
        return None

def extract_factor_number(filename):
    """从文件名中提取因子编号"""
    match = re.search(r'factor_(\d+)_report\.pdf', filename)
    if match:
        return f"factor_{match.group(1)}"
    return None

def add_text_to_pdf(pdf_path, output_path, factor_info, font_size=14, margin_top=18):
    """在PDF顶部添加因子信息"""
    try:
        # 打开PDF文件
        doc = fitz.open(pdf_path)

        # 获取第一页
        page = doc[0]

        # 获取页面尺寸
        page_rect = page.rect

        # 准备要添加的文本
        factor_name = factor_info.get('factor_name', '')
        factor_type = factor_info.get('type', '')
        factor_remark = factor_info.get('remark', '')

        # 构建文本内容 - 只包含类型和备注
        text_lines = []
        if factor_type:
            text_lines.append(f"类型: {factor_type}")
        if factor_remark:
            text_lines.append(f"备注: {factor_remark}")

        if not text_lines:
            print(f"没有找到因子 {factor_name} 的信息")
            doc.close()
            return False

        # 设置字体和颜色
        text_color = (0, 0, 0)  # 黑色

        # 计算文本位置
        x = 50  # 左边距
        y = margin_top + font_size  # 顶部边距

        # 添加每行文本
        for i, line in enumerate(text_lines):
            # 计算当前行的y坐标
            current_y = y + i * (font_size + 2)

            # 添加文本 - 使用中文字体
            page.insert_text(
                (x, current_y),
                line,
                fontsize=font_size,
                color=text_color,
                fontname="china-s"  # 使用宋体
            )

        # 保存修改后的PDF
        doc.save(output_path)
        doc.close()

        print(f"成功为 {factor_name} 添加信息到PDF: {output_path}")
        return True

    except Exception as e:
        print(f"处理PDF文件 {pdf_path} 时出错: {e}")
        return False

def test_single_file():
    """测试单个文件"""
    # 设置路径
    factor_desc_path = "factorDescNew.csv"
    pdf_dir = "icBacktestLog/alpha299/img"
    test_file = "factor_3_report.pdf"  # 测试文件
    
    # 检查文件是否存在
    pdf_path = os.path.join(pdf_dir, test_file)
    if not os.path.exists(pdf_path):
        print(f"测试PDF文件不存在: {pdf_path}")
        return
    
    if not os.path.exists(factor_desc_path):
        print(f"因子说明文件不存在: {factor_desc_path}")
        return
    
    # 加载因子说明
    factor_desc_df = load_factor_description(factor_desc_path)
    if factor_desc_df is None:
        print("无法加载因子说明文件")
        return
    
    # 创建因子说明字典
    factor_desc_dict = {}
    for _, row in factor_desc_df.iterrows():
        factor_id = row.iloc[0]  # 第一列是因子编号
        factor_type = row.iloc[1] if len(row) > 1 else ''  # 第二列是类型
        remark = row.iloc[2] if len(row) > 2 else ''  # 第三列是备注
        factor_desc_dict[factor_id] = {
            'type': factor_type,
            'remark': remark
        }
    
    # 提取因子编号
    factor_name = extract_factor_number(test_file)
    if not factor_name:
        print(f"无法从文件名提取因子编号: {test_file}")
        return
    
    # 获取因子信息
    factor_info = factor_desc_dict.get(factor_name, {})
    factor_info['factor_name'] = factor_name
    
    # 创建输出文件名
    output_file = f"{factor_name}_with_info.pdf"
    output_path = os.path.join(".", output_file)
    
    # 添加信息到PDF
    success = add_text_to_pdf(pdf_path, output_path, factor_info, font_size=14, margin_top=18)
    
    if success:
        print(f"\n测试完成！")
        print(f"原文件: {pdf_path}")
        print(f"输出文件: {output_path}")
        print(f"因子信息: {factor_info}")
    else:
        print("测试失败")

def process_all_pdfs():
    """批量处理所有PDF文件"""
    # 设置路径
    factor_desc_path = "factorDescNew.csv"
    pdf_dir = "icBacktestLog/alpha299/img"
    output_dir = "pdfs_with_factor_info"

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 检查文件是否存在
    if not os.path.exists(factor_desc_path):
        print(f"因子说明文件不存在: {factor_desc_path}")
        return

    if not os.path.exists(pdf_dir):
        print(f"PDF目录不存在: {pdf_dir}")
        return

    # 加载因子说明
    factor_desc_df = load_factor_description(factor_desc_path)
    if factor_desc_df is None:
        print("无法加载因子说明文件")
        return

    # 创建因子说明字典
    factor_desc_dict = {}
    for _, row in factor_desc_df.iterrows():
        factor_id = row.iloc[0]  # 第一列是因子编号
        factor_type = row.iloc[1] if len(row) > 1 else ''  # 第二列是类型
        remark = row.iloc[2] if len(row) > 2 else ''  # 第三列是备注
        factor_desc_dict[factor_id] = {
            'type': factor_type,
            'remark': remark
        }

    # 获取所有PDF文件
    pdf_files = []
    for filename in os.listdir(pdf_dir):
        if filename.endswith('_report.pdf'):
            factor_name = extract_factor_number(filename)
            if factor_name is not None:
                pdf_files.append((factor_name, filename))

    # 按因子编号排序
    pdf_files.sort(key=lambda x: int(x[0].split('_')[1]))

    print(f"找到 {len(pdf_files)} 个PDF文件")

    # 处理所有PDF文件
    success_count = 0
    for factor_name, filename in pdf_files:
        pdf_path = os.path.join(pdf_dir, filename)
        output_file = filename  # 保持原文件名
        output_path = os.path.join(output_dir, output_file)

        # 获取因子信息
        factor_info = factor_desc_dict.get(factor_name, {})
        factor_info['factor_name'] = factor_name

        # 添加信息到PDF
        success = add_text_to_pdf(pdf_path, output_path, factor_info, font_size=14, margin_top=18)

        if success:
            success_count += 1
        else:
            print(f"处理失败: {factor_name}")

    print(f"\n批量处理完成！")
    print(f"成功处理: {success_count}/{len(pdf_files)} 个文件")
    print(f"输出目录: {output_dir}")

def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        print("开始测试单个PDF文件...")
        test_single_file()
    else:
        print("开始批量处理所有PDF文件...")
        process_all_pdfs()

if __name__ == "__main__":
    main()
