# Alpha299因子转写状态表



| 因子编号 | 状态 | 实现方式 | 测试状态 | 备注 | 最后更新 |
|---------|------|----------|----------|------|----------|
| factor_1 | ✅ | 准确实现 | 🧪 已测试 | 简单算术因子，无窗口操作 | 2025-07-18 |
| factor_2 | ✅ | 准确实现 | 🧪 已测试 | 差分+符号函数，使用shift操作 | 2025-07-18 |
| factor_3 | ✅ | 准确实现 | 🧪 已测试 | 收益率差分排名与相关性乘积 | 2025-07-18 |
| factor_4 | ✅ | 准确实现 | 🧪 已测试 | 排名协方差的负排名，5万行测试通过 | 2025-07-18 |
| factor_5 | ✅ | 准确实现 | 🧪 已测试 | 收益率累计和排名复合因子 | 2025-07-18 |
| factor_10 | ✅ | 准确实现 | 🧪 已测试 | 基于收益率、成交量、VWAP复合因子 | 2025-07-18 |
| factor_11 | ✅ | 准确实现 | 🧪 已测试 | 成交量与价格相关性组合因子 | 2025-07-18 |
| factor_12 | ✅ | 准确实现 | 🧪 已测试 | 复杂时间序列嵌套运算因子 | 2025-07-18 |
| factor_13 | ✅ | 准确实现 | 🧪 已测试 | 成交量变化与价格变动相关性因子 | 2025-07-18 |
| factor_15 | ✅ | 准确实现 | 🧪 已测试 | 收益率标准差比值和价格差分排名因子 | 2025-07-18 |
| factor_16 | ✅ | 准确实现 | 🧪 已测试 | 成交量、价格组合和收益率排名乘积因子 | 2025-07-18 |
| factor_17 | ✅ | 准确实现 | 🧪 已测试 | CLOSE排名和CLOSE/OPEN比值排名乘积因子 | 2025-07-18 |
| factor_18 | ✅ | 准确实现 | 🧪 已测试 | 排名相关系数，使用算子组合 | 2025-07-18 |
| factor_19 | ✅ | 准确实现 | 🧪 已测试 | HIGH标准差排名与HIGH-VOLUME相关系数负乘积因子 | 2025-07-18 |
| factor_21 | ✅ | 准确实现 | 🧪 已测试 | VWAP与CLOSE差值和和的排名比值因子 | 2025-07-18 |
| factor_22 | ✅ | 准确实现 | 🧪 已测试 | VOLUME/ADV20与CLOSE差分排名乘积因子 | 2025-07-18 |
| factor_23 | ✅ | 准确实现 | 🧪 已测试 | VOLUME排名与HIGH相关系数负值因子 | 2025-07-18 |
| factor_25 | ✅ | 准确实现 | 🧪 已测试 | 基于价格差分条件判断的因子 | 2025-07-18 |
| factor_26 | ⚠️ | 有问题 | ❌ 未测试 | 截面+时序嵌套操作，表达式限制无法实现 | 2025-07-18 |
| factor_30 | ✅ | 准确实现 | 🧪 已测试 | 价格位置比值差分累积排名因子 | 2025-07-18 |
| factor_32 | ✅ | 准确实现 | 🧪 已测试 | 价格位置与成交量排名相关性因子 | 2025-07-18 |
| factor_33 | ✅ | 准确实现 | 🧪 已测试 | VWAP与ADV相关性排名比较因子 | 2025-07-18 |
| factor_34 | 🔶 | 近似实现 | 🧪 已测试 | 复杂因子：基于VWAP行业中性化、截面排名、相关性等的复合计算，简化了行业中性化和截面排名 | 2025-07-18 |
| factor_36 | ✅ | 准确实现 | 🧪 已测试 | 价格排名相关性和价格差值复合因子 | 2025-07-18 |
| factor_37 | ✅ | 准确实现 | 🧪 已测试 | 价格位置与成交量相关性复合因子 | 2025-07-18 |
| factor_38 | ✅ | 准确实现 | 🧪 已测试 | 价格位置与成交量相关性最小排名因子 | 2025-07-18 |
| factor_39 | ✅ | 准确实现 | 🧪 已测试 | 价格变化排名与成交量条件因子 | 2025-07-18 |
| factor_40 | ✅ | 准确实现 | 🧪 已测试 | 开盘价和收益率乘积标准化排名因子 | 2025-07-18 |
| factor_42 | ✅ | 准确实现 | 🧪 已测试 | 价格条件和排名相关性复合因子 | 2025-07-18 |
| factor_43 | ✅ | 准确实现 | 🧪 已测试 | VWAP差值排名与相关性排名幂运算因子 | 2025-07-18 |
| factor_44 | ✅ | 准确实现 | 🧪 已测试 | 价格变化条件符号调整因子 | 2025-07-18 |
| factor_45 | 🔶 | 近似实现 | 🧪 已测试 | 开盘价和成交量Z-score排名相关性负值因子，简化了横截面排名 | 2025-07-18 |
| factor_46 | 🔶 | 近似实现 | 🧪 已测试 | 开盘价与前日价格差值Z-score乘积因子，简化了横截面排名 | 2025-07-18 |
| factor_49 | 🔶 | 近似实现 | 🧪 已测试 | 价格波动率、成交量Z-score和VWAP复合因子，简化了横截面排名 | 2025-07-18 |
| factor_54 | 🔶 | 近似实现 | 🧪 已测试 | 高价排名与成交量Z-score排名相关性负值因子，简化了横截面排名 | 2025-07-18 |
| factor_55 | 🔶 | 近似实现 | 🧪 已测试 | 三重排名乘积因子，简化了横截面排名 | 2025-07-18 |
| factor_56 | ✅ | 准确实现 | 🧪 已测试 | 20日价格动量EMA因子 | 2025-07-18 |
| factor_57 | ✅ | 准确实现 | 🧪 已测试 | VWAP条件比较因子 | 2025-07-18 |
| factor_58 | 🔶 | 近似实现 | 🧪 已测试 | 双重衰减排名最大值因子，简化了横截面排名 | 2025-07-18 |
| factor_61 | ✅ | 准确实现 | 🧪 已测试 | 价格排名与成交量排名最大值因子 | 2025-07-18 |
| factor_62 | ✅ | 准确实现 | 🧪 已测试 | 高价与成交量相关性排名负值因子 | 2025-07-18 |
| factor_64 | ✅ | 准确实现 | 🧪 已测试 | 价格变化排名因子 | 2025-07-18 |
| factor_65 | ✅ | 准确实现 | 🧪 已测试 | 收盘价标准差排名因子 | 2025-07-18 |
| factor_73 | ✅ | 准确实现 | 🧪 已测试 | 线性衰减价格排名因子 | 2025-07-18 |
| factor_74 | ✅ | 准确实现 | 🧪 已测试 | 收盘价与成交量相关性因子 | 2025-07-18 |
| factor_77 | ✅ | 准确实现 | 🧪 已测试 | 价格最小值与成交量最大值因子 | 2025-07-18 |
| factor_78 | ✅ | 准确实现 | 🧪 已测试 | 高价与低价差值排名因子 | 2025-07-18 |
| factor_79 | ✅ | 准确实现 | 🧪 已测试 | 收盘价标准差因子 | 2025-07-18 |
| factor_81 | 🔶 | 近似实现 | 🧪 已测试 | 收盘价排名与成交量Z-score排名协方差负值因子，简化了横截面排名 | 2025-07-18 |
| factor_82 | ✅ | 准确实现 | 🧪 已测试 | 最低价位置LOWDAY指标因子 | 2025-07-18 |
| factor_83 | 🔶 | 近似实现 | 🧪 已测试 | 高价与成交量相关性差分与收盘价标准差排名乘积负值因子，简化了横截面排名 | 2025-07-18 |
| factor_85 | ✅ | 准确实现 | 🧪 已测试 | 成交量与价格相关性因子 | 2025-07-18 |
| factor_86 | ✅ | 准确实现 | 🧪 已测试 | 收盘价延迟差分因子 | 2025-07-18 |
| factor_90 | ✅ | 准确实现 | 🧪 已测试 | VWAP与成交量相关性排名因子 | 2025-07-18 |
| factor_92 | ✅ | 准确实现 | 🧪 已测试 | 价格最大值与最小值因子 | 2025-07-18 |
| factor_93 | ✅ | 准确实现 | 🧪 已测试 | 开盘价与成交量相关性因子 | 2025-07-18 |
| factor_96 | ✅ | 准确实现 | 🧪 已测试 | 价格变化标准差因子 | 2025-07-18 |
| factor_98 | ✅ | 准确实现 | 🧪 已测试 | ADV与价格相关性因子 | 2025-07-18 |
| factor_100 | ✅ | 准确实现 | 🧪 已测试 | 成交量标准差因子 | 2025-07-18 |
| factor_101 | ✅ | 准确实现 | 🧪 已测试 | 价格移动和波动幅度复合因子 | 2025-07-18 |
| factor_102 | 🔶 | 近似实现 | 🧪 已测试 | 多价格Z-score和相关性复合因子，简化了复杂逻辑 | 2025-07-18 |
| factor_103 | ✅ | 准确实现 | 🧪 已测试 | 低价标准差因子 | 2025-07-18 |
| factor_104 | ✅ | 准确实现 | 🧪 已测试 | 高价与成交量相关性负值因子 | 2025-07-18 |
| factor_105 | ✅ | 准确实现 | 🧪 已测试 | 开盘价与成交量Z-score排名相关性负值因子 | 2025-07-18 |
| factor_106 | ✅ | 准确实现 | 🧪 已测试 | 收盘价1日差分因子 | 2025-07-18 |
| factor_107 | ✅ | 准确实现 | 🧪 已测试 | 开盘价与高价差值排名负值因子 | 2025-07-18 |
| factor_108 | ✅ | 准确实现 | 🧪 已测试 | 高价与低价差值排名因子 | 2025-07-18 |
| factor_109 | ✅ | 准确实现 | 🧪 已测试 | 收盘价与开盘价差值标准差因子 | 2025-07-18 |
| factor_110 | ✅ | 准确实现 | 🧪 已测试 | 收盘价与低价差值最大值因子 | 2025-07-18 |
| factor_111 | ✅ | 准确实现 | 🧪 已测试 | 四个不同周期均线平均值因子 | 2025-07-18 |
| factor_112 | ✅ | 准确实现 | 🧪 已测试 | Alpha15隔夜跳空因子 | 2025-07-18 |
| factor_113 | ✅ | 准确实现 | 🧪 已测试 | Alpha155成交量MACD因子 | 2025-07-18 |
| factor_114 | ✅ | 准确实现 | 🧪 已测试 | Alpha158价格振幅与收盘价比率因子 | 2025-07-18 |
| factor_115 | ✅ | 准确实现 | 🧪 已测试 | Alpha157基于修正价格范围的加权指标 | 2025-07-18 |
| factor_116 | ✅ | 准确实现 | 🧪 已测试 | Alpha160条件波动率EMA因子 | 2025-07-18 |
| factor_117 | 🔶 | 近似实现 | 🧪 已测试 | 收益率、成交量和价格复合指标，简化了横截面排名 | 2025-07-18 |
| factor_118 | ✅ | 准确实现 | 🧪 已测试 | Alpha166收益率分布偏度近似因子 | 2025-07-18 |
| factor_119 | ✅ | 准确实现 | 🧪 已测试 | Alpha167累积上涨幅度因子 | 2025-07-18 |
| factor_120 | ✅ | 准确实现 | 🧪 已测试 | 简单因子：-volume/移动平均volume | 2025-07-18 |
| factor_122 | 🔶 | 近似实现 | 🧪 已测试 | VWAP、成交量和高价复合因子，简化了横截面排名 | 2025-07-18 |
| factor_123 | ✅ | 准确实现 | 🧪 已测试 | Alpha171价格相对位置与动能比率因子 | 2025-07-18 |
| factor_124 | ✅ | 准确实现 | 🧪 已测试 | ADX指标技术分析因子 | 2025-07-18 |
| factor_125 | ✅ | 准确实现 | 🧪 已测试 | DEMA和TEMA组合Z-score标准化因子 | 2025-07-18 |
| factor_128 | 🔶 | 近似实现 | 🧪 已测试 | 随机震荡值排名与成交量排名相关性因子，简化了横截面排名 | 2025-07-18 |
| factor_129 | ✅ | 准确实现 | 🧪 已测试 | Alpha178成交量加权1日价格变化率因子 | 2025-07-18 |
| factor_130 | 🔶 | 近似实现 | 🧪 已测试 | VWAP、成交量相关性和价格低点复合因子，简化了横截面排名 | 2025-07-18 |
| factor_131 | 🔶 | 近似实现 | 🧪 已测试 | Alpha185开盘收盘价比率平方负排名因子，简化了横截面排名 | 2025-07-18 |
| factor_132 | ✅ | 准确实现 | 🧪 已测试 | Alpha189基于收盘价SMA绝对偏差因子 | 2025-07-18 |
| factor_133 | 🔶 | 近似实现 | 🧪 已测试 | 成交量变化排名与收益率排名滚动相关性因子，简化了横截面排名 | 2025-07-18 |
| factor_134 | ✅ | 准确实现 | 🧪 已测试 | Alpha23条件波动率强度因子 | 2025-07-18 |
| factor_135 | 🔶 | 近似实现 | 🧪 已测试 | Alpha25价格动量、成交量相对强度和长期收益表现组合，简化了横截面排名 | 2025-07-18 |
| factor_138 | ✅ | 准确实现 | 🧪 已测试 | Alpha29成交量加权价格变化率因子 | 2025-07-18 |
| factor_140 | ✅ | 准确实现 | 🧪 已测试 | Alpha2日内价格振幅结构因子 | 2025-07-18 |
| factor_142 | 🔶 | 近似实现 | 🧪 已测试 | 开盘价变化和开盘价-成交量相关性复合因子，简化了横截面排名 | 2025-07-18 |
| factor_143 | 🔶 | 近似实现 | 🧪 已测试 | VWAP和成交量标准化排名相关性因子，简化了横截面排名 | 2025-07-18 |
| factor_144 | 🔶 | 近似实现 | 🧪 已测试 | 开盘价和收益率滚动求和乘积差分排名因子，简化了横截面排名 | 2025-07-18 |
| factor_145 | ✅ | 准确实现 | 🧪 已测试 | Alpha38条件高点价格变化因子 | 2025-07-18 |
| factor_146 | ✅ | 准确实现 | 🧪 已测试 | Alpha40条件成交量比率因子 | 2025-07-18 |
| factor_147 | 🔶 | 近似实现 | 🧪 已测试 | Alpha41 VWAP变化最大值负排名因子，简化了横截面排名 | 2025-07-18 |
| factor_149 | ✅ | 准确实现 | 🧪 已测试 | Alpha43 OBV变种累积因子 | 2025-07-18 |
| factor_150 | 🔶 | 近似实现 | 🧪 已测试 | Alpha45价格动量与VWAP成交量相关性排名乘积因子，简化了横截面排名 | 2025-07-18 |
| factor_152 | ✅ | 准确实现 | 🧪 已测试 | Alpha49基于趋向指标UOS的一部分 | 2025-07-18 |
| factor_155 | ✅ | 准确实现 | 🧪 已测试 | 基于多重条件判断的因子 | 2025-07-18 |
| factor_156 | ✅ | 准确实现 | 🧪 已测试 | Alpha52价格相对位置的累积比率因子 | 2025-07-18 |
| factor_158 | ✅ | 准确实现 | 🧪 已测试 | Alpha55复杂条件价格变动累积因子 | 2025-07-18 |
| factor_159 | ✅ | 准确实现 | 🧪 已测试 | Alpha57平滑随机震荡%K因子 | 2025-07-18 |
| factor_161 | ✅ | 准确实现 | 🧪 已测试 | Alpha60成交量加权日内价格位置累积因子 | 2025-07-18 |
| factor_162 | 🔶 | 近似实现 | 🧪 已测试 | Alpha61 VWAP动量与最低价成交量相关性排名最大值因子，简化了横截面排名 | 2025-07-18 |
| factor_163 | ✅ | 准确实现 | 🧪 已测试 | Alpha5高点与成交量时序排名的相关性因子 | 2025-07-18 |
| factor_168 | ✅ | 准确实现 | 🧪 已测试 | Alpha68平滑日内波动率调整的价格动量因子 | 2025-07-18 |
| factor_169 | ✅ | 准确实现 | 🧪 已测试 | Alpha69开盘价定向运动比率因子 | 2025-07-18 |
| factor_170 | ✅ | 准确实现 | 🧪 已测试 | Alpha70成交金额6日滚动标准差 | 2025-07-18 |
| factor_172 | 🔶 | 近似实现 | 🧪 已测试 | 基于加权价格差分信号的横截面排名因子，简化了横截面排名 | 2025-07-18 |
| factor_174 | 🔶 | 近似实现 | 🧪 已测试 | Alpha77价格与VWAP偏离及价格与成交量相关性衰减排名最小化因子，简化了横截面排名 | 2025-07-18 |
| factor_176 | ✅ | 准确实现 | 🧪 已测试 | Alpha80五日成交量变化率 | 2025-07-18 |
| factor_177 | ✅ | 准确实现 | 🧪 已测试 | Alpha81成交量指数移动平均因子 | 2025-07-18 |
| factor_178 | 🔶 | 近似实现 | 🧪 已测试 | Alpha7 VWAP与收盘价偏离及成交量变化因子，简化了横截面排名 | 2025-07-18 |
| factor_180 | ✅ | 准确实现 | 🧪 已测试 | Alpha84二十日OBV变种累积因子 | 2025-07-18 |
| factor_181 | ✅ | 准确实现 | 🧪 已测试 | Alpha85成交量强度与价格动量时序排名乘积因子 | 2025-07-18 |
| factor_182 | ✅ | 准确实现 | 🧪 已测试 | Alpha86条件价格加速因子 | 2025-07-18 |
| factor_183 | 🔶 | 近似实现 | 🧪 已测试 | 基于VWAP差分和价格结构的复合因子，简化了横截面排名 | 2025-07-18 |
| factor_185 | ✅ | 准确实现 | 🧪 已测试 | Alpha89基于MACD指标变体，应用tanh变换 | 2025-07-18 |
| factor_186 | 🔶 | 近似实现 | 🧪 已测试 | 基于加权平均价格差分的横截面排名因子，简化了横截面排名 | 2025-07-18 |
| factor_187 | ✅ | 准确实现 | 🧪 已测试 | Alpha93条件开盘价差累积因子 | 2025-07-18 |
| factor_190 | ✅ | 准确实现 | 🧪 已测试 | Alpha98条件价格偏离因子 | 2025-07-18 |
| factor_192 | 🔶 | 近似实现 | 🧪 已测试 | Alpha101基于成交量相关性和价格排名的复合因子，简化了横截面排名 | 2025-07-18 |
| factor_194 | ✅ | 准确实现 | 🧪 已测试 | Alpha100 VWAP滚动最大值与开盘价比率因子 | 2025-07-18 |
| factor_195 | ✅ | 准确实现 | 🧪 已测试 | Alpha101复杂的协方差、回归beta和最小值计算因子 | 2025-07-18 |
| factor_196 | ✅ | 准确实现 | 🧪 已测试 | Alpha102基于成交金额最大值、VWAP相关性和价格差分的最小值因子 | 2025-07-18 |
| factor_197 | ✅ | 准确实现 | 🧪 已测试 | Alpha103基于VWAP-成交量相关性和VWAP-成交金额最小值与低价标准差协方差的最小值因子 | 2025-07-18 |
| factor_198 | ✅ | 准确实现 | 🧪 已测试 | Alpha104基于成交量-低价相关性和成交量最大值变化率的反正切因子 | 2025-07-18 |
| factor_199 | ✅ | 准确实现 | 🧪 已测试 | 优化后的Alpha10基于收盘价和成交量的Z-score标准化，然后计算标准差 | 2025-07-18 |
| factor_200 | ✅ | 准确实现 | 🧪 已测试 | Alpha111基于成交量与开盘价/收盘价的相关性、成交金额平方根等的复合计算 | 2025-07-18 |
| factor_201 | ✅ | 准确实现 | 🧪 已测试 | Alpha112基于volume和high的Z-score差值与low+close对数的滚动回归贝塔因子 | 2025-07-18 |
| factor_202 | ✅ | 准确实现 | 🧪 已测试 | Alpha114基于close排名、vwap对数和回归残差的复合因子 | 2025-07-18 |
| factor_203 | ✅ | 准确实现 | 🧪 已测试 | Alpha115基于volume和close的Z-score与close变化率的相关性因子 | 2025-07-18 |
| factor_204 | ✅ | 准确实现 | 🧪 已测试 | Alpha11基于收盘价对数的差分因子 | 2025-07-18 |
| factor_205 | ✅ | 准确实现 | 🧪 已测试 | Alpha120基于成交量对数差分的滚动标准差因子 | 2025-07-18 |
| factor_207 | 🔶 | 近似实现 | 🧪 已测试 | Alpha122基于amount和open相关性的截面排名因子，简化了横截面排名 | 2025-07-18 |
| factor_208 | ✅ | 准确实现 | 🧪 已测试 | Alpha123基于volume标准差与amount对close回归贝塔乘积的反正切因子 | 2025-07-18 |
| factor_209 | ✅ | 准确实现 | 🧪 已测试 | 基于VWAP差分与low-volume相关性最小值的因子 | 2025-07-18 |
| factor_210 | ✅ | 准确实现 | 🧪 已测试 | 基于成交量绝对值与开盘价变化率相关性的因子 | 2025-07-18 |
| factor_211 | ✅ | 准确实现 | 🧪 已测试 | Alpha127基于高价差分的Z-score与成交量-收盘价协方差最小值的反正切之和 | 2025-07-18 |
| factor_212 | ✅ | 准确实现 | 🧪 已测试 | Alpha126基于close差分Z-score和high反正切的复合因子 | 2025-07-18 |
| factor_213 | ✅ | 准确实现 | 🧪 已测试 | Alpha127基于VWAP、amount和close的复合技术分析因子 | 2025-07-18 |
| factor_214 | 🔶 | 近似实现 | 🧪 已测试 | Alpha131基于volume-open回归贝塔的Z-score排名与low-close回归贝塔负值的和，简化了横截面排名 | 2025-07-18 |
| factor_215 | 🔶 | 近似实现 | 🧪 已测试 | Alpha132基于low-volume最大值排名、volume Z-score排名与high-volume乘积比值和vwap-close和的协方差，简化了横截面排名 | 2025-07-18 |
| factor_216 | ✅ | 准确实现 | 🧪 已测试 | Alpha133基于high对amount和volume对close的回归贝塔最大值 | 2025-07-18 |
| factor_218 | ✅ | 准确实现 | 🧪 已测试 | Alpha131基于VWAP与close差值的滚动均值因子 | 2025-07-18 |
| factor_219 | ✅ | 准确实现 | 🧪 已测试 | Alpha137基于VWAP的百分比变化率因子 | 2025-07-18 |
| factor_220 | ✅ | 准确实现 | 🧪 已测试 | Alpha138基于open差分Z-score和low-volume相关性的复合因子 | 2025-07-18 |
| factor_221 | 🔶 | 近似实现 | 🧪 已测试 | 基于成交量与最高价的协方差排名，简化了横截面排名 | 2025-07-18 |
| factor_222 | ✅ | 准确实现 | 🧪 已测试 | Alpha141基于延迟成交量与收盘价差分的乘积 | 2025-07-18 |
| factor_223 | ✅ | 准确实现 | 🧪 已测试 | 基于高低价回归贝塔和回归残差的因子 | 2025-07-18 |
| factor_225 | ✅ | 准确实现 | 🧪 已测试 | Alpha146基于VWAP-amount协方差与low均值乘积和amount绝对值标准差的相关性因子 | 2025-07-18 |
| factor_226 | ✅ | 准确实现 | 🧪 已测试 | Alpha147基于Z-score和价格变化的组合，包含sigmoid变换和对数价格变化率 | 2025-07-18 |
| factor_227 | ✅ | 准确实现 | 🧪 已测试 | Alpha14基于log(volume)和close的滚动相关系数 | 2025-07-18 |
| factor_228 | 🔶 | 近似实现 | 🧪 已测试 | Alpha150基于成交量平方根与收盘价排名-VWAP最大值协方差的Z-score和收盘价Z-score排名的和，简化了横截面排名 | 2025-07-18 |
| factor_229 | ✅ | 准确实现 | 🧪 已测试 | Alpha151基于arctan(high)与open百分比变化和volume差分最大值的相关性 | 2025-07-18 |
| factor_230 | ✅ | 准确实现 | 🧪 已测试 | 基于价格差分和成交量相关性的组合 | 2025-07-18 |
| factor_231 | ✅ | 准确实现 | 🧪 已测试 | Alpha153基于成交量标准化、开盘价Z-score和高价差分的复合因子 | 2025-07-18 |
| factor_232 | 🔶 | 近似实现 | 🧪 已测试 | Alpha154基于low、high、vwap和volume的复合技术分析因子，简化了横截面排名 | 2025-07-18 |
| factor_233 | 🔶 | 近似实现 | 🧪 已测试 | Alpha155基于VWAP负值与amount均值比值和close-vwap最大值与volume协方差排名的比值，简化了横截面排名 | 2025-07-18 |
| factor_236 | ✅ | 准确实现 | 🧪 已测试 | Alpha164基于low最大值与amount均值协方差的最大值因子 | 2025-07-18 |
| factor_237 | ✅ | 准确实现 | 🧪 已测试 | Alpha165基于volume-amount协方差与amount-high回归贝塔均值的比值因子 | 2025-07-18 |
| factor_238 | ✅ | 准确实现 | 🧪 已测试 | Alpha167基于volume-high相关系数的滚动最大值因子 | 2025-07-18 |
| factor_240 | ✅ | 准确实现 | 🧪 已测试 | Alpha169基于VWAP sigmoid变换、成交量差分最大值与高价百分比变化率最小值的因子 | 2025-07-18 |
| factor_242 | ✅ | 准确实现 | 🧪 已测试 | Alpha170基于close标准差和volume最大值的复合Z-score标准差因子 | 2025-07-18 |
| factor_245 | 🔶 | 近似实现 | 🧪 已测试 | Alpha173对过去5期成交量和最高价的滚动协方差进行截面排名，简化了横截面排名 | 2025-07-18 |
| factor_248 | ✅ | 准确实现 | 🧪 已测试 | Alpha178基于延迟16期的high与close的回归残差 | 2025-07-18 |
| factor_250 | ✅ | 准确实现 | 🧪 已测试 | Alpha181基于VWAP与volume标准差最小值除以low最大值的比值因子 | 2025-07-18 |
| factor_251 | ✅ | 准确实现 | 🧪 已测试 | Alpha182基于volume绝对值与close平方根的回归贝塔sigmoid变换，然后与low的回归残差的反正切 | 2025-07-18 |
| factor_252 | ✅ | 准确实现 | 🧪 已测试 | Alpha185基于amount-low回归贝塔与close/open比值的Z-score差值绝对值 | 2025-07-18 |
| factor_253 | ✅ | 准确实现 | 🧪 已测试 | Alpha186基于volume和vwap在过去10期的滚动相关系数 | 2025-07-18 |
| factor_255 | ✅ | 准确实现 | 🧪 已测试 | Alpha191基于open-close回归残差与high绝对值的差值，再与low+high的和的差值的百分比变化率 | 2025-07-18 |
| factor_256 | ✅ | 准确实现 | 🧪 已测试 | Alpha192基于VWAP与volume时序排名的协方差 | 2025-07-18 |
| factor_259 | 🔶 | 近似实现 | 🧪 已测试 | Alpha195基于amount-high协方差的Z-score截面排名，简化了横截面排名 | 2025-07-18 |
| factor_260 | ✅ | 准确实现 | 🧪 已测试 | Alpha202基于high差分与负成交量的滚动相关系数 | 2025-07-18 |
| factor_261 | ✅ | 准确实现 | 🧪 已测试 | Alpha203基于close、volume、vwap的Z-score组合与high差分的乘积 | 2025-07-18 |
| factor_262 | 🔶 | 近似实现 | 🧪 已测试 | Alpha204基于负VWAP与amount差分的比值除以low-vwap最大值与volume协方差排名的比值，简化了横截面排名 | 2025-07-18 |
| factor_264 | ✅ | 准确实现 | 🧪 已测试 | Alpha206基于low和VWAP百分比变化率的最小值 | 2025-07-18 |
| factor_265 | ✅ | 准确实现 | 🧪 已测试 | Alpha208基于VWAP、CLOSE和HIGH的组合逻辑，包含sigmoid变换 | 2025-07-18 |
| factor_266 | 🔶 | 近似实现 | 🧪 已测试 | Alpha210基于VWAP排名与成交金额排名的相关系数，简化了横截面排名 | 2025-07-18 |
| factor_267 | ✅ | 准确实现 | 🧪 已测试 | Alpha211基于volume*close标准差最大值与amount均值协方差的最大值 | 2025-07-18 |
| factor_268 | ✅ | 准确实现 | 🧪 已测试 | Alpha216基于每日成交金额和VWAP的12周期协方差 | 2025-07-18 |
| factor_269 | ✅ | 准确实现 | 🧪 已测试 | Alpha217基于volume和VWAP相关系数的滚动最大值 | 2025-07-18 |
| factor_270 | ✅ | 准确实现 | 🧪 已测试 | Alpha218基于arctan(volume)与volume-vwap相关系数乘积的对数绝对值与vwap差分最大值的平方根 | 2025-07-18 |
| factor_271 | ✅ | 准确实现 | 🧪 已测试 | Alpha220基于VWAP标准差Z-score、VOLUME最大值Z-score的最大值与HIGH标准差Z-score的和的标准差 | 2025-07-18 |
| factor_272 | ✅ | 准确实现 | 🧪 已测试 | Alpha221基于LOG(VWAP)的差分 | 2025-07-18 |
| factor_274 | 🔶 | 近似实现 | 🧪 已测试 | Alpha223基于VWAP差分与amount排名最大值的最大值与VWAP-amount相关系数最小值的标准差，简化了横截面排名 | 2025-07-18 |
| factor_277 | ✅ | 准确实现 | 🧪 已测试 | Alpha27基于volume和high的Z-score和与amount反正切乘积的对数绝对值，与close/open比值标准差减去low的Z-score的回归贝塔 | 2025-07-18 |
| factor_278 | ✅ | 准确实现 | 🧪 已测试 | Alpha28基于close/open比率的滚动均值 | 2025-07-18 |
| factor_279 | ✅ | 准确实现 | 🧪 已测试 | Alpha30基于low和vwap最小值的差分除以open绝对值平方根与open绝对值的比值 | 2025-07-18 |
| factor_280 | ✅ | 准确实现 | 🧪 已测试 | Alpha31基于uni_col绝对值标准差对uni_col的回归贝塔 | 2025-07-18 |
| factor_281 | ✅ | 准确实现 | 🧪 已测试 | Alpha33基于volume的时序排名与vwap的协方差 | 2025-07-18 |
| factor_283 | ✅ | 准确实现 | 🧪 已测试 | Alpha36基于VWAP与volume滚动均值的相关系数 | 2025-07-18 |
| factor_284 | 🔶 | 近似实现 | 🧪 已测试 | Alpha37基于amount-close协方差的Z-score截面排名，简化了横截面排名 | 2025-07-18 |
| factor_285 | ✅ | 准确实现 | 🧪 已测试 | Alpha38基于close反正切与open百分比变化和volume差分最大值的相关系数 | 2025-07-18 |
| factor_286 | ✅ | 准确实现 | 🧪 已测试 | Alpha42基于high差分与负volume的滚动相关系数 | 2025-07-18 |
| factor_287 | ✅ | 准确实现 | 🧪 已测试 | Alpha43基于open、volume、vwap的Z-score组合与close差分的乘积 | 2025-07-18 |
| factor_289 | 🔶 | 近似实现 | 🧪 已测试 | Alpha45基于负close除以amount均值与high-vwap最大值和volume协方差排名的比值，简化了横截面排名 | 2025-07-18 |
| factor_290 | ✅ | 准确实现 | 🧪 已测试 | Alpha48基于low-amount最小值的sigmoid变换与vwap/close比值的差值和close百分比变化的最小值 | 2025-07-18 |
| factor_295 | ✅ | 准确实现 | 🧪 已测试 | Alpha52基于amount最大值平方根与vwap相关系数和open差分的最小值 | 2025-07-18 |
| factor_298 | 🔶 | 近似实现 | 🧪 已测试 | Alpha55基于复杂的Z-score、排名、回归残差等计算，简化了横截面排名 | 2025-07-18 |
| factor_299 | ✅ | 准确实现 | 🧪 已测试 | Alpha57基于low和volume-low最大值的Z-score和与log(vwap)百分比变化的相关系数 | 2025-07-18 |
| factor_300 | 🔶 | 近似实现 | 🧪 已测试 | Alpha58基于延迟amount的对数与amount排名对high+close回归残差的最小值，简化了横截面排名和回归 | 2025-07-18 |
| factor_301 | ✅ | 准确实现 | 🧪 已测试 | Alpha5基于volume-amount协方差除以amount-high回归贝塔均值的比值 | 2025-07-18 |
| factor_302 | ✅ | 准确实现 | 🧪 已测试 | Alpha60基于volume平方根和open的Z-score最小值与volume最大值的负相关系数减去low和volume的Z-score最小值差分 | 2025-07-18 |
| factor_303 | ✅ | 准确实现 | 🧪 已测试 | Alpha61基于volume-close相关系数最大值与amount平方根和相关系数最大值延迟的乘积 | 2025-07-18 |
| factor_304 | ✅ | 准确实现 | 🧪 已测试 | Alpha62基于amount最大值与vwap-amount回归贝塔最大值的最小值 | 2025-07-18 |
| factor_305 | ✅ | 准确实现 | 🧪 已测试 | Alpha69基于vwap-volume相关系数均值减去open-high差值的Z-score | 2025-07-18 |
| factor_309 | ✅ | 准确实现 | 🧪 已测试 | Alpha76基于成交量绝对值与收盘价百分比变化率的滚动相关系数 | 2025-07-18 |
| factor_310 | ✅ | 准确实现 | 🧪 已测试 | Alpha78基于low差分的Z-score标准化与high反正切的和 | 2025-07-18 |
| factor_313 | 🔶 | 近似实现 | 🧪 已测试 | Alpha80基于volume-close回归贝塔的tanh变换截面排名加上close-low回归贝塔的负值，简化了横截面排名 | 2025-07-18 |
| factor_314 | 🔶 | 近似实现 | 🧪 已测试 | Alpha82基于复杂的Z-score计算、截面排名和协方差，简化了横截面排名 | 2025-07-18 |
| factor_316 | ✅ | 准确实现 | 🧪 已测试 | Alpha90基于volume排名的sigmoid变换除以low-amount回归贝塔绝对值再乘以vwap | 2025-07-18 |
| factor_317 | ✅ | 准确实现 | 🧪 已测试 | VWAP百分比变化率因子 | 2025-07-18 |
| factor_318 | ✅ | 准确实现 | 🧪 已测试 | Alpha92基于close差分的Z-score减去log(volume)和low-volume相关系数最小值的负数 | 2025-07-18 |
| factor_319 | 🔶 | 近似实现 | 🧪 已测试 | Alpha93基于log(volume)乘以close截面排名差分，简化了横截面排名 | 2025-07-18 |
| factor_321 | ✅ | 准确实现 | 🧪 已测试 | Alpha96基于vwap-amount协方差乘以low均值与amount绝对值标准差的相关系数 | 2025-07-18 |
| factor_323 | ✅ | 准确实现 | 🧪 已测试 | Alpha101基于amount和low的Z-score tanh变换和与open Z-score回归贝塔和volume-vwap回归贝塔的最小值 | 2025-07-18 |
| factor_324 | ✅ | 准确实现 | 🧪 已测试 | Alpha99基于volume平方根与close排名和vwap最大值的协方差和high-vwap协方差对open-close最小值回归贝塔的最小值 | 2025-07-18 |
| factor_325 | ✅ | 准确实现 | 🧪 已测试 | Alpha106基于amount差分与volume-low相关系数和close Z-score和的相关系数 | 2025-07-18 |
| factor_326 | ✅ | 准确实现 | 🧪 已测试 | Alpha107基于volume-high最大值和low的Z-score和与log(vwap)百分比变化的相关系数 | 2025-07-18 |
| factor_328 | ✅ | 准确实现 | 🧪 已测试 | Alpha109基于延迟amount的对数与延迟vwap对vwap+high+close回归残差Z-score的最大值 | 2025-07-18 |
| factor_329 | ✅ | 准确实现 | 🧪 已测试 | Alpha110基于volume平方根与open最小值和volume最大值相关系数的负数加上low-volume最小值差分的Z-score | 2025-07-18 |
| factor_330 | ✅ | 准确实现 | 🧪 已测试 | Alpha113基于延迟low对open*close乘积的回归残差 | 2025-07-18 |
| factor_332 | ✅ | 准确实现 | 🧪 已测试 | Alpha117基于vwap-open差值的双重时序排名对vwap平方根反正切的回归残差 | 2025-07-18 |
| factor_333 | ✅ | 准确实现 | 🧪 已测试 | Alpha118基于high百分比变化率与volume最小值Z-score的最大值乘以low | 2025-07-18 |
| factor_335 | 🔶 | 近似实现 | 🧪 已测试 | Alpha125基于amount-vwap协方差与amount-open回归贝塔均值最小值的Z-score截面排名乘以amount差分除以close标准差的最小值，简化了横截面排名 | 2025-07-18 |
| factor_337 | ✅ | 准确实现 | 🧪 已测试 | Alpha130基于vwap滚动均值与high-amount滚动相关系数的乘积 | 2025-07-18 |
| factor_338 | ✅ | 准确实现 | 🧪 已测试 | Alpha135基于volume排名的sigmoid变换除以amount-high回归贝塔绝对值再乘以vwap | 2025-07-18 |
| factor_339 | 🔶 | 近似实现 | 🧪 已测试 | Alpha139基于log(abs(volume))乘以open截面排名的差分，简化了横截面排名 | 2025-07-18 |
| factor_340 | 🔶 | 近似实现 | 🧪 已测试 | Alpha140基于复杂的延迟、排名、反正切、百分比变化、最小值、回归残差等计算，简化了横截面排名 | 2025-07-18 |
| factor_341 | ✅ | 准确实现 | 🧪 已测试 | Alpha145基于close-amount回归贝塔最大值的Z-score加上log(amount)的Z-score | 2025-07-18 |
| factor_342 | ✅ | 准确实现 | 🧪 已测试 | Alpha148基于amount和vwap的Z-score和与open余弦值的回归贝塔和volume-vwap回归贝塔的最大值 | 2025-07-18 |
| factor_345 | ✅ | 准确实现 | 🧪 已测试 | Alpha159基于amount最大值反正切与close-amount回归贝塔双重排名对low的回归残差 | 2025-07-18 |
| factor_346 | 🔶 | 近似实现 | 🧪 已测试 | Alpha15因子：基于vwap差值标准化、amount排名、协方差等的复合计算，简化了截面排名 | 2025-07-18 |
| factor_347 | ✅ | 准确实现 | 🧪 已测试 | Alpha179基于close-low回归贝塔对close的回归残差 | 2025-07-18 |
| factor_349 | 🔶 | 近似实现 | 🧪 已测试 | Alpha183基于close的二阶差分Z-score加上amount截面排名的滚动最大值，简化了横截面排名 | 2025-07-18 |
| factor_350 | 🔶 | 近似实现 | 🧪 已测试 | Alpha184基于volume反正切余弦值的截面排名差分加上log(volume)的Z-score，简化了横截面排名 | 2025-07-18 |
| factor_353 | ✅ | 准确实现 | 🧪 已测试 | Alpha189基于vwap-amount回归贝塔的滚动最大值 | 2025-07-18 |
| factor_354 | ✅ | 准确实现 | 🧪 已测试 | Alpha18基于延迟open对close的回归残差 | 2025-07-18 |
| factor_355 | ✅ | 准确实现 | 🧪 已测试 | Alpha192基于high对close绝对值标准差的回归贝塔 | 2025-07-18 |
| factor_356 | ✅ | 准确实现 | 🧪 已测试 | Alpha196基于close与volume滚动均值的滚动相关系数 | 2025-07-18 |
| factor_358 | ✅ | 准确实现 | 🧪 已测试 | Alpha199基于volume的Z-score加上amount-vwap协方差均值的Z-score | 2025-07-18 |
| factor_359 | ✅ | 准确实现 | 🧪 已测试 | Alpha19基于open-low回归贝塔对close的回归残差 | 2025-07-18 |
| factor_360 | ✅ | 准确实现 | 🧪 已测试 | Alpha1基于volume*vwap标准差最大值与amount均值的协方差的滚动最大值 | 2025-07-18 |
| factor_361 | ✅ | 准确实现 | 🧪 已测试 | Alpha201基于low反正切与open百分比变化和volume差分最小值的滚动相关系数 | 2025-07-18 |
| factor_366 | ✅ | 准确实现 | 🧪 已测试 | Alpha214基于low最小值与amount均值协方差的滚动最小值 | 2025-07-18 |
| factor_367 | ✅ | 准确实现 | 🧪 已测试 | Alpha215基于volume-amount协方差除以amount-low回归贝塔均值 | 2025-07-18 |
| factor_368 | ✅ | 准确实现 | 🧪 已测试 | Alpha219基于vwap sigmoid与volume差分最小值和low百分比变化的最大值 | 2025-07-18 |
| factor_369 | ✅ | 准确实现 | 🧪 已测试 | Alpha21基于vwap与volume标准差最小值除以low滚动最大值 | 2025-07-18 |
| factor_371 | ✅ | 准确实现 | 🧪 已测试 | Alpha224基于volume对数与open-close最小值的滚动相关系数 | 2025-07-18 |
| factor_372 | 🔶 | 近似实现 | 🧪 已测试 | Alpha23基于close二阶差分Z-score加上amount截面排名滚动最大值，简化了横截面排名 | 2025-07-18 |
| factor_373 | 🔶 | 近似实现 | 🧪 已测试 | Alpha24基于volume反正切截面排名差分加上log(volume)标准差，简化了横截面排名 | 2025-07-18 |
| factor_375 | ✅ | 准确实现 | 🧪 已测试 | Alpha35基于amount-close相关系数滚动最大值的负数 | 2025-07-18 |
| factor_376 | ✅ | 准确实现 | 🧪 已测试 | Alpha38基于vwap对log(volume)的回归贝塔系数 | 2025-07-18 |
| factor_378 | ✅ | 准确实现 | 🧪 已测试 | Alpha3基于vwap差分Z-score减去负close排名平方根 | 2025-07-18 |
| factor_379 | 🔶 | 近似实现 | 🧪 已测试 | Alpha40基于vwap截面排名与volume的滚动相关系数，简化了横截面排名 | 2025-07-18 |
| factor_383 | ✅ | 准确实现 | 🧪 已测试 | Alpha62基于log(volume)-low对log(low+close)的回归贝塔系数 | 2025-07-18 |
| factor_384 | ✅ | 准确实现 | 🧪 已测试 | Alpha63基于延迟high对open*close乘积的回归残差 | 2025-07-18 |
| factor_386 | ✅ | 准确实现 | 🧪 已测试 | Alpha65基于volume和close的Z-score和与close百分比变化平方根的滚动相关系数 | 2025-07-18 |
| factor_387 | ✅ | 准确实现 | 🧪 已测试 | Alpha67基于vwap-close差值的双重排名对vwap平方根反正切的回归残差 | 2025-07-18 |
| factor_390 | 🔶 | 近似实现 | 🧪 已测试 | Alpha72基于amount-close滚动相关系数的截面排名，简化了横截面排名 | 2025-07-18 |
| factor_391 | ✅ | 准确实现 | 🧪 已测试 | 基于成交量标准差和回归贝塔系数乘积的反正切因子 | 2025-07-18 |
| factor_392 | 🔶 | 近似实现 | 🧪 已测试 | Alpha75基于amount-vwap协方差与amount-low回归贝塔均值最小值的Z-score截面排名乘以amount差分除以close标准差的最小值，简化了横截面排名 | 2025-07-18 |
| factor_394 | ✅ | 准确实现 | 🧪 已测试 | Alpha79基于复杂的vwap、amount计算的最小值 | 2025-07-18 |
| factor_399 | ✅ | 准确实现 | 🧪 已测试 | Alpha91基于延迟amount乘以close差分 | 2025-07-18 |
| factor_400 | ✅ | 准确实现 | 🧪 已测试 | Alpha92基于复杂的low-open最小值与low标准差相关系数、amount Z-score、volume反正切等计算 | 2025-07-18 |
| factor_401 | ✅ | 准确实现 | 🧪 已测试 | Alpha93基于high-low回归贝塔系数，然后close除以该贝塔，最后计算对close的回归残差 | 2025-07-18 |
| factor_402 | ✅ | 准确实现 | 🧪 已测试 | Alpha95基于close-amount回归贝塔最大值的Z-score减去log(amount)的Z-score | 2025-07-18 |
| factor_403 | ✅ | 准确实现 | 🧪 已测试 | Alpha9基于vwap sigmoid与volume差分最大值和high百分比变化的最小值 | 2025-07-18 |
| factor_406 | ✅ | 准确实现 | 🧪 已测试 | SAREXT抛物线转向指标扩展版，使用Numba优化 | 2025-07-18 |
| factor_407 | ✅ | 准确实现 | 🧪 已测试 | SIN正弦函数因子 | 2025-07-18 |
| factor_413 | ✅ | 准确实现 | 🧪 已测试 | FastK快速随机指标K值，使用Numba优化 | 2025-07-18 |
| factor_415 | ✅ | 准确实现 | 🧪 已测试 | StochRSI K随机相对强弱指标K值，使用Numba优化 | 2025-07-18 |
| factor_417 | ✅ | 准确实现 | 🧪 已测试 | SUB向量算术减法因子，计算high-low | 2025-07-18 |
| factor_420 | ✅ | 准确实现 | 🧪 已测试 | TAN正切函数因子 | 2025-07-18 |
| factor_422 | ✅ | 准确实现 | 🧪 已测试 | Aroon Up阿隆上升指标，使用Numba优化 | 2025-07-18 |
| factor_428 | ✅ | 准确实现 | 🧪 已测试 | ULTOSC终极振荡指标，使用Numba优化 | 2025-07-18 |
| factor_429 | ✅ | 准确实现 | 🧪 已测试 | VAR方差因子，使用Numba优化 | 2025-07-18 |
| factor_433 | ✅ | 准确实现 | 🧪 已测试 | Aroon Down阿隆下降指标，使用Numba优化 | 2025-07-18 |
| factor_435 | ✅ | 准确实现 | 🧪 已测试 | ATR平均真实波幅，使用Numba优化 | 2025-07-18 |
| factor_436 | ✅ | 准确实现 | 🧪 已测试 | AVGDEV平均离差因子，使用Numba优化 | 2025-07-18 |
| factor_446 | ✅ | 准确实现 | 🧪 已测试 | CORR皮尔逊相关系数因子，计算close和volume的相关系数 | 2025-07-18 |
| factor_447 | ✅ | 准确实现 | 🧪 已测试 | COS向量三角余弦因子 | 2025-07-18 |
| factor_450 | ✅ | 准确实现 | 🧪 已测试 | DIV向量除法因子，计算close/volume | 2025-07-18 |
| factor_453 | ✅ | 准确实现 | 🧪 已测试 | HT_INPHASE希尔伯特变换瞬时相位，使用Numba优化 | 2025-07-18 |
| factor_454 | ✅ | 准确实现 | 🧪 已测试 | HT_QUAD希尔伯特变换正交分量，使用Numba优化 | 2025-07-18 |
| factor_473 | ✅ | 准确实现 | 🧪 已测试 | ADL蔡金累积/派发线 | 2025-07-18 |
| factor_488 | ✅ | 准确实现 | 🧪 已测试 | MFI资金流量指标，使用Numba优化 | 2025-07-18 |
| factor_492 | ✅ | 准确实现 | 🧪 已测试 | ADX平均趋向指数，使用Numba优化（重新准确实现） | 2025-07-18 |
| factor_493 | ✅ | 准确实现 | 🧪 已测试 | MININDEX周期内最小值索引，使用Numba优化 | 2025-07-18 |
| factor_498 | ✅ | 准确实现 | 🧪 已测试 | MINUS_DI下降方向线，使用Numba优化 | 2025-07-18 |
| factor_504 | ✅ | 准确实现 | 🧪 已测试 | NVI负成交量指标，使用Numba优化 | 2025-07-18 |
| factor_505 | ✅ | 准确实现 | 🧪 已测试 | OBV能量潮指标 | 2025-07-18 |
| factor_507 | ✅ | 准确实现 | 🧪 已测试 | PLUS_DM正向趋向移动，使用Numba优化 | 2025-07-18 |
| factor_509 | ✅ | 准确实现 | 🧪 已测试 | PVI正量指标，使用Numba优化 | 2025-07-18 |

