#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
因子回测报告指标提取脚本
从JSON文件中提取因子统计指标和IC结果，结合因子说明CSV生成汇总报告
"""

import json
import pandas as pd
import os
import re
from pathlib import Path

def extract_factor_number(filename):
    """从文件名中提取因子编号"""
    return filename.split('_')[1]

def load_factor_description(csv_path):
    """加载因子说明CSV文件，使用中文编码"""
    try:
        # 尝试不同的中文编码
        encodings = ['gbk', 'gb2312', 'utf-8', 'cp936']
        for encoding in encodings:
            try:
                df = pd.read_csv(csv_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取因子说明文件")
                return df
            except UnicodeDecodeError:
                continue
        
        # 如果所有编码都失败，使用默认编码并忽略错误
        df = pd.read_csv(csv_path, encoding='utf-8', errors='ignore')
        print("使用UTF-8编码（忽略错误）读取因子说明文件")
        return df
        
    except Exception as e:
        print(f"读取因子说明文件失败: {e}")
        return None

def extract_json_metrics(json_file_path):
    """从JSON文件中提取指标"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取因子名称
        factor_name = data.get('factor_name', '')
        
        # 提取factor_stats下的指标
        factor_stats = data.get('factor_stats', {})
        mean = factor_stats.get('mean', None)
        std = factor_stats.get('std', None)
        max_val = factor_stats.get('max', None)
        min_val = factor_stats.get('min', None)
        valid_ratio = factor_stats.get('valid_ratio', None)
        
        # 提取ic_results下global_ic的指标
        ic_results = data.get('ic_results', {})
        global_ic = ic_results.get('global_ic', {})
        mean_ic = global_ic.get('mean_ic', None)
        icir = global_ic.get('icir', None)
        ic_positive_ratio = global_ic.get('ic_positive_ratio', None)
        ic_count = global_ic.get('ic_count', None)
        
        return {
            'factor_name': factor_name,
            'mean': mean,
            'std': std,
            'max': max_val,
            'min': min_val,
            'valid_ratio': valid_ratio,
            'mean_ic': mean_ic,
            'icir': icir,
            'ic_positive_ratio': ic_positive_ratio,
            'ic_count': ic_count
        }
        
    except Exception as e:
        print(f"处理文件 {json_file_path} 时出错: {e}")
        return None

def main():
    """主函数"""
    # 设置路径
    json_dir = "icBacktestLog/alpha299/json"
    factor_desc_path = "factorDesc.CSV"
    output_path = "factor_metrics_summary.csv"
    
    # 检查路径是否存在
    if not os.path.exists(json_dir):
        print(f"JSON目录不存在: {json_dir}")
        return
    
    if not os.path.exists(factor_desc_path):
        print(f"因子说明文件不存在: {factor_desc_path}")
        return
    
    # 加载因子说明
    factor_desc_df = load_factor_description(factor_desc_path)
    if factor_desc_df is None:
        print("无法加载因子说明文件")
        return
    
    # 创建因子说明字典，方便查找
    factor_desc_dict = {}
    for _, row in factor_desc_df.iterrows():
        factor_id = row.iloc[0]  # 第一列是因子编号
        i = row.iloc[1]
        status = row.iloc[2] if len(row) > 1 else ''  # 第二列是状态
        remark = row.iloc[3] if len(row) > 2 else ''  # 第三列是备注
        formula = row.iloc[4] if len(row) > 3 else ''  # 第四列是公式
        factor_desc_dict[factor_id] = {
            'status': status,
            'remark': remark, 
            'i': i, 
            'formula': formula
        }
    
    # 获取所有JSON文件
    json_files = []
    for filename in os.listdir(json_dir):
        if filename.endswith('_report.json'):
            factor_num = extract_factor_number(filename)
            if factor_num is not None:
                json_files.append((factor_num, filename))
    
    # 按因子编号排序
    json_files.sort(key=lambda x: x[0])
    
    print(f"找到 {len(json_files)} 个JSON文件")
    
    # 提取所有指标
    results = []
    for factor_num, filename in json_files:
        json_path = os.path.join(json_dir, filename)
        metrics = extract_json_metrics(json_path)
        
        if metrics is not None:
            # 添加因子说明信息
            factor_name = metrics['factor_name']
            desc_info = factor_desc_dict.get(factor_name, {'status': '', 'remark': ''})
            
            result_row = {
                '因子编号': factor_name,
                'i': desc_info['i'],
                '状态': desc_info['status'],
                'mean': metrics['mean'],
                'std': metrics['std'],
                'max': metrics['max'],
                'min': metrics['min'],
                'valid_ratio': metrics['valid_ratio'],
                'mean_ic': metrics['mean_ic'],
                'icir': metrics['icir'],
                'ic_positive_ratio': metrics['ic_positive_ratio'],
                'ic_count': metrics['ic_count'], 
                '备注': desc_info['remark'],
                '公式': desc_info['formula']
            }
            results.append(result_row)
            print(f"已处理: {factor_name}")
    
    # 创建DataFrame并保存
    if results:
        df = pd.DataFrame(results)

        # 格式化数值列，保留4位小数
        numeric_columns = ['mean', 'std', 'max', 'min', 'valid_ratio', 'mean_ic', 'icir', 'ic_positive_ratio']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = df[col].apply(lambda x: round(x, 4) if pd.notnull(x) and isinstance(x, (int, float)) else x)

        df.to_csv(output_path, index=False, encoding='utf-8-sig')  # 使用utf-8-sig确保Excel能正确显示中文
        print(f"\n成功生成汇总报告: {output_path}")
        print(f"共处理 {len(results)} 个因子")
        print(f"输出文件列名: {list(df.columns)}")

        # 显示前几行作为预览
        print("\n前5行预览:")
        print(df.head().to_string())
    else:
        print("没有成功提取到任何数据")

if __name__ == "__main__":
    main()
