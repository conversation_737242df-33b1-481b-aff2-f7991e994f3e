import polars as pl

univ = pl.read_parquet("/disk4/shared/intern/laiyc/preprocess/univ_1bar.parquet")
periods = [("0930","1129"), ("1300","1459")]

VALID_TIME = ["0925"] + [
    f"{m//60:02d}{m%60:02d}"
    for start, end in periods
    for m in range(
        int(start[:2])*60 + int(start[2:]),
        int(end[:2])*60   + int(end[2:]) + 1
    )
] 

len(VALID_TIME)

univ

import polars as pl

class CONFIG:
    DATA_DIR = "/disk4/shared/intern/laiyc/minRawData/"
    PREPROCESS_DIR = "/disk4/shared/intern/laiyc/preprocess/"
    RU_DIR = DATA_DIR + "ru_min1/"
    DSH_COLS = ['date', 'symbol', 'hhmm']
    SPEC_TIME = ["0959", "1029", "1059", "1129", "1329", "1359", "1429"]
    LOCAL_DIR = "/home/<USER>/polarsAlpha/diskBig/"


y1 = pl.read_parquet(CONFIG.RU_DIR + "y@inter@1_lag@1_full.parquet")
y1

y1 = y1.filter(pl.col("hhmm").is_in(VALID_TIME))

y1 = y1.filter(pl.col("year") != 2024)

y1 = y1.hstack(univ.select(pl.col("univ_1bar")))

y1.filter(pl.col("univ_1bar") == 1).select(pl.col("y@inter@1_lag@1_full").alias("y1"))

y1.filter(pl.col("univ_1bar") == 1).select(pl.col("y@inter@1_lag@1_full").alias("y1").cast(pl.Float32)).write_parquet("/home/<USER>/polarsAlpha/diskBig/y1.parquet")

!pwd

import polars as pl

df = pl.read_parquet("/home/<USER>/polarsAlpha/diskBig/OHLCVA_vwap.parquet")

df = df.with_columns(
    # 1) 先把 date 和 hhmm 用空格拼成 "20150105 0925"
    pl.concat_str([pl.col("date"), pl.col("hhmm")], separator=" ")
      # 2) 再按 "%Y%m%d %H%M" 模式解析成 datetime
      .str.strptime(pl.Datetime, format="%Y%m%d %H%M")
      .alias("datetime")
)

df

df.write_parquet("/home/<USER>/polarsAlpha/diskBig/OHLCVA_vwap.parquet")

small_df = df.filter((pl.col("datetime") >= pl.datetime(2023, 6, 1, 9, 25)) 
                     & (pl.col("datetime") <= pl.datetime(2023, 7, 31, 15, 0)))
small_df

small_df.write_parquet("disk/OHLCVA_vwap.parquet")

